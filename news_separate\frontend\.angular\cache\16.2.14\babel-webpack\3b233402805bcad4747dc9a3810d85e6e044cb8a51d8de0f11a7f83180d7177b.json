{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./chat/chat.component\";\nimport * as i2 from \"./news/news.component\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'News Agent';\n    // Sample news data for testing search and filter functionality\n    this.sampleNews = [{\n      country: 'ukraine',\n      source: 'Kyiv Independent',\n      title: 'Ukraine receives new military aid package from NATO allies',\n      link: 'https://example.com/news1',\n      published: '2024-01-15T10:30:00Z',\n      description: 'NATO allies announce comprehensive military aid package including advanced defense systems and humanitarian support for Ukraine.'\n    }, {\n      country: 'poland',\n      source: 'Warsaw Times',\n      title: 'Poland strengthens eastern border security measures',\n      link: 'https://example.com/news2',\n      published: '2024-01-14T15:45:00Z',\n      description: 'Polish government implements enhanced security protocols along its eastern border to ensure regional stability.'\n    }, {\n      country: 'russia',\n      source: 'Moscow Herald',\n      title: 'Russian economic indicators show mixed results',\n      link: 'https://example.com/news3',\n      published: '2024-01-13T09:15:00Z',\n      description: 'Latest economic data from Russia reveals varying performance across different sectors amid ongoing international sanctions.'\n    }, {\n      country: 'belarus',\n      source: 'Minsk Daily',\n      title: 'Belarus announces new agricultural initiatives',\n      link: 'https://example.com/news4',\n      published: '2024-01-12T14:20:00Z',\n      description: 'Government unveils comprehensive agricultural development program aimed at increasing food security and export potential.'\n    }, {\n      country: 'ukraine',\n      source: 'Ukrainian Voice',\n      title: 'Reconstruction efforts accelerate in liberated territories',\n      link: 'https://example.com/news5',\n      published: '2024-01-11T11:00:00Z',\n      description: 'International cooperation drives rapid reconstruction of infrastructure and essential services in recently liberated areas.'\n    }, {\n      country: 'poland',\n      source: 'Polish Tribune',\n      title: 'Cultural exchange program launched with neighboring countries',\n      link: 'https://example.com/news6',\n      published: '2024-01-10T16:30:00Z',\n      description: 'New cultural initiative promotes understanding and cooperation through educational and artistic exchanges.'\n    }, {\n      country: 'russia',\n      source: 'Siberian Times',\n      title: 'Arctic research station reports significant climate findings',\n      link: 'https://example.com/news7',\n      published: '2024-01-09T08:45:00Z',\n      description: 'Scientists at remote Arctic facility publish groundbreaking research on climate change impacts in polar regions.'\n    }, {\n      country: 'ukraine',\n      source: 'Lviv Post',\n      title: 'Technology sector shows remarkable growth despite challenges',\n      link: 'https://example.com/news8',\n      published: '2024-01-08T13:15:00Z',\n      description: 'Ukrainian tech companies demonstrate resilience and innovation, attracting international investment and partnerships.'\n    }];\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 6,\n    vars: 5,\n    consts: [[1, \"app-container\"], [1, \"main-content\"], [3, \"news\", \"showTitle\", \"compact\", \"showFilters\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"main\", 1)(2, \"h1\");\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(4, \"app-chat\")(5, \"app-news\", 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.title);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"news\", ctx.sampleNews)(\"showTitle\", true)(\"compact\", false)(\"showFilters\", true);\n      }\n    },\n    dependencies: [i1.ChatComponent, i2.NewsComponent],\n    styles: [\"\\n\\n.app-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  background: transparent;\\n  overflow: hidden;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: auto;\\n  background-color: transparent;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.main-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 50%, #8b5cf6 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  margin: 0;\\n  padding: var(--space-8) 0 var(--space-6) 0;\\n  font-size: var(--font-size-3xl);\\n  font-weight: var(--font-weight-bold);\\n  flex-shrink: 0;\\n  letter-spacing: -0.025em;\\n  position: relative;\\n}\\n.main-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: var(--space-4);\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(90deg, #4f46e5 0%, #8b5cf6 100%);\\n  border-radius: 2px;\\n}\\n\\n\\n\\napp-news[_ngcontent-%COMP%] {\\n  margin-top: var(--space-8);\\n  padding: 0 var(--space-4);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .app-container[_ngcontent-%COMP%] {\\n    height: 100vh;\\n  }\\n}\\n\\n\\n.sr-only[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border: 0;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.text-left[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n\\n.text-right[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.mb-0[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.mb-2[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-2);\\n}\\n\\n.mb-4[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n}\\n\\n.mb-6[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-6);\\n}\\n\\n.mt-0[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n}\\n\\n.mt-2[_ngcontent-%COMP%] {\\n  margin-top: var(--space-2);\\n}\\n\\n.mt-4[_ngcontent-%COMP%] {\\n  margin-top: var(--space-4);\\n}\\n\\n.mt-6[_ngcontent-%COMP%] {\\n  margin-top: var(--space-6);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDJCQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUVBO0VBQ0UsT0FBQTtFQUNBLGNBQUE7RUFDQSw2QkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQUNGO0FBQ0U7RUFDRSxrQkFBQTtFQUNBLDBFQUFBO0VBQ0EsNkJBQUE7RUFDQSxvQ0FBQTtFQUNBLHFCQUFBO0VBQ0EsU0FBQTtFQUNBLDBDQUFBO0VBQ0EsK0JBQUE7RUFDQSxvQ0FBQTtFQUNBLGNBQUE7RUFDQSx3QkFBQTtFQUNBLGtCQUFBO0FBQ0o7QUFDSTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLHNCQUFBO0VBQ0EsU0FBQTtFQUNBLDJCQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSw0REFBQTtFQUNBLGtCQUFBO0FBQ047O0FBSUEseUJBQUE7QUFDQTtFQUNFLDBCQUFBO0VBQ0EseUJBQUE7QUFERjs7QUFJQSxxQ0FBQTtBQUNBO0VBQ0U7SUFDRSxhQUFBO0VBREY7QUFDRjtBQUlBLGdDQUFBO0FBQ0E7RUFDRSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsVUFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0FBRkY7O0FBS0E7RUFBZSxrQkFBQTtBQURmOztBQUVBO0VBQWEsZ0JBQUE7QUFFYjs7QUFEQTtFQUFjLGlCQUFBO0FBS2Q7O0FBSEE7RUFBUSxnQkFBQTtBQU9SOztBQU5BO0VBQVEsNkJBQUE7QUFVUjs7QUFUQTtFQUFRLDZCQUFBO0FBYVI7O0FBWkE7RUFBUSw2QkFBQTtBQWdCUjs7QUFkQTtFQUFRLGFBQUE7QUFrQlI7O0FBakJBO0VBQVEsMEJBQUE7QUFxQlI7O0FBcEJBO0VBQVEsMEJBQUE7QUF3QlI7O0FBdkJBO0VBQVEsMEJBQUE7QUEyQlIiLCJzb3VyY2VzQ29udGVudCI6WyIvKiA9PT09PSBBUFAgTEFZT1VUID09PT09ICovXG4uYXBwLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGhlaWdodDogMTAwdmg7XG4gIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuXG4ubWFpbi1jb250ZW50IHtcbiAgZmxleDogMTtcbiAgb3ZlcmZsb3c6IGF1dG87XG4gIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuXG4gIGgxIHtcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRmNDZlNSAwJSwgIzYzNjZmMSA1MCUsICM4YjVjZjYgMTAwJSk7XG4gICAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XG4gICAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xuICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgICBtYXJnaW46IDA7XG4gICAgcGFkZGluZzogdmFyKC0tc3BhY2UtOCkgMCB2YXIoLS1zcGFjZS02KSAwO1xuICAgIGZvbnQtc2l6ZTogdmFyKC0tZm9udC1zaXplLTN4bCk7XG4gICAgZm9udC13ZWlnaHQ6IHZhcigtLWZvbnQtd2VpZ2h0LWJvbGQpO1xuICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgIGxldHRlci1zcGFjaW5nOiAtMC4wMjVlbTtcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG5cbiAgICAmOjphZnRlciB7XG4gICAgICBjb250ZW50OiAnJztcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgIGJvdHRvbTogdmFyKC0tc3BhY2UtNCk7XG4gICAgICBsZWZ0OiA1MCU7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSk7XG4gICAgICB3aWR0aDogNjBweDtcbiAgICAgIGhlaWdodDogM3B4O1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjNGY0NmU1IDAlLCAjOGI1Y2Y2IDEwMCUpO1xuICAgICAgYm9yZGVyLXJhZGl1czogMnB4O1xuICAgIH1cbiAgfVxufVxuXG4vKiBOZXdzIHNlY3Rpb24gc3R5bGluZyAqL1xuYXBwLW5ld3Mge1xuICBtYXJnaW4tdG9wOiB2YXIoLS1zcGFjZS04KTtcbiAgcGFkZGluZzogMCB2YXIoLS1zcGFjZS00KTtcbn1cblxuLyogPT09PT0gUkVTUE9OU0lWRSBVVElMSVRJRVMgPT09PT0gKi9cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuYXBwLWNvbnRhaW5lciB7XG4gICAgaGVpZ2h0OiAxMDB2aDtcbiAgfVxufVxuXG4vKiA9PT09PSBVVElMSVRZIENMQVNTRVMgPT09PT0gKi9cbi5zci1vbmx5IHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB3aWR0aDogMXB4O1xuICBoZWlnaHQ6IDFweDtcbiAgcGFkZGluZzogMDtcbiAgbWFyZ2luOiAtMXB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBjbGlwOiByZWN0KDAsIDAsIDAsIDApO1xuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICBib3JkZXI6IDA7XG59XG5cbi50ZXh0LWNlbnRlciB7IHRleHQtYWxpZ246IGNlbnRlcjsgfVxuLnRleHQtbGVmdCB7IHRleHQtYWxpZ246IGxlZnQ7IH1cbi50ZXh0LXJpZ2h0IHsgdGV4dC1hbGlnbjogcmlnaHQ7IH1cblxuLm1iLTAgeyBtYXJnaW4tYm90dG9tOiAwOyB9XG4ubWItMiB7IG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNlLTIpOyB9XG4ubWItNCB7IG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNlLTQpOyB9XG4ubWItNiB7IG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNlLTYpOyB9XG5cbi5tdC0wIHsgbWFyZ2luLXRvcDogMDsgfVxuLm10LTIgeyBtYXJnaW4tdG9wOiB2YXIoLS1zcGFjZS0yKTsgfVxuLm10LTQgeyBtYXJnaW4tdG9wOiB2YXIoLS1zcGFjZS00KTsgfVxuLm10LTYgeyBtYXJnaW4tdG9wOiB2YXIoLS1zcGFjZS02KTsgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "sampleNews", "country", "source", "link", "published", "description", "_", "_2", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { NewsItem } from './news/news.component';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = 'News Agent';\n\n  // Sample news data for testing search and filter functionality\n  sampleNews: NewsItem[] = [\n    {\n      country: 'ukraine',\n      source: 'Kyiv Independent',\n      title: 'Ukraine receives new military aid package from NATO allies',\n      link: 'https://example.com/news1',\n      published: '2024-01-15T10:30:00Z',\n      description: 'NATO allies announce comprehensive military aid package including advanced defense systems and humanitarian support for Ukraine.'\n    },\n    {\n      country: 'poland',\n      source: 'Warsaw Times',\n      title: 'Poland strengthens eastern border security measures',\n      link: 'https://example.com/news2',\n      published: '2024-01-14T15:45:00Z',\n      description: 'Polish government implements enhanced security protocols along its eastern border to ensure regional stability.'\n    },\n    {\n      country: 'russia',\n      source: 'Moscow Herald',\n      title: 'Russian economic indicators show mixed results',\n      link: 'https://example.com/news3',\n      published: '2024-01-13T09:15:00Z',\n      description: 'Latest economic data from Russia reveals varying performance across different sectors amid ongoing international sanctions.'\n    },\n    {\n      country: 'belarus',\n      source: 'Minsk Daily',\n      title: 'Belarus announces new agricultural initiatives',\n      link: 'https://example.com/news4',\n      published: '2024-01-12T14:20:00Z',\n      description: 'Government unveils comprehensive agricultural development program aimed at increasing food security and export potential.'\n    },\n    {\n      country: 'ukraine',\n      source: 'Ukrainian Voice',\n      title: 'Reconstruction efforts accelerate in liberated territories',\n      link: 'https://example.com/news5',\n      published: '2024-01-11T11:00:00Z',\n      description: 'International cooperation drives rapid reconstruction of infrastructure and essential services in recently liberated areas.'\n    },\n    {\n      country: 'poland',\n      source: 'Polish Tribune',\n      title: 'Cultural exchange program launched with neighboring countries',\n      link: 'https://example.com/news6',\n      published: '2024-01-10T16:30:00Z',\n      description: 'New cultural initiative promotes understanding and cooperation through educational and artistic exchanges.'\n    },\n    {\n      country: 'russia',\n      source: 'Siberian Times',\n      title: 'Arctic research station reports significant climate findings',\n      link: 'https://example.com/news7',\n      published: '2024-01-09T08:45:00Z',\n      description: 'Scientists at remote Arctic facility publish groundbreaking research on climate change impacts in polar regions.'\n    },\n    {\n      country: 'ukraine',\n      source: 'Lviv Post',\n      title: 'Technology sector shows remarkable growth despite challenges',\n      link: 'https://example.com/news8',\n      published: '2024-01-08T13:15:00Z',\n      description: 'Ukrainian tech companies demonstrate resilience and innovation, attracting international investment and partnerships.'\n    }\n  ];\n}\n", "<div class=\"app-container\">\n  <main class=\"main-content\">\n    <h1>{{ title }}</h1>\n    <app-chat></app-chat>\n    <!-- Uncommented for testing search and filter functionality -->\n    <app-news [news]=\"sampleNews\" [showTitle]=\"true\" [compact]=\"false\" [showFilters]=\"true\"></app-news>\n  </main>\n</div>\n"], "mappings": ";;;AAQA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,YAAY;IAEpB;IACA,KAAAC,UAAU,GAAe,CACvB;MACEC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,kBAAkB;MAC1BH,KAAK,EAAE,4DAA4D;MACnEI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,cAAc;MACtBH,KAAK,EAAE,qDAAqD;MAC5DI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,eAAe;MACvBH,KAAK,EAAE,gDAAgD;MACvDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,aAAa;MACrBH,KAAK,EAAE,gDAAgD;MACvDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,iBAAiB;MACzBH,KAAK,EAAE,4DAA4D;MACnEI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,gBAAgB;MACxBH,KAAK,EAAE,+DAA+D;MACtEI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,gBAAgB;MACxBH,KAAK,EAAE,8DAA8D;MACrEI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,WAAW;MACnBH,KAAK,EAAE,8DAA8D;MACrEI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,CACF;;EACF,QAAAC,CAAA,G;qBAtEYT,YAAY;EAAA;EAAA,QAAAU,EAAA,G;UAAZV,YAAY;IAAAW,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRzBE,EAAA,CAAAC,cAAA,aAA2B;QAEnBD,EAAA,CAAAE,MAAA,GAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpBH,EAAA,CAAAI,SAAA,eAAqB;QAGvBJ,EAAA,CAAAG,YAAA,EAAO;;;QAJDH,EAAA,CAAAK,SAAA,GAAW;QAAXL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAAhB,KAAA,CAAW;QAGLiB,EAAA,CAAAK,SAAA,GAAmB;QAAnBL,EAAA,CAAAO,UAAA,SAAAR,GAAA,CAAAf,UAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}