{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction NewsComponent_div_0_h2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\", 5);\n    i0.ɵɵtext(1, \"Latest News\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewsComponent_div_0_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 6)(1, \"div\", 7)(2, \"span\", 8);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 9);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"h3\", 10);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 11);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 12)(12, \"span\", 13);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 14);\n    i0.ɵɵtext(16, \"Read \\u2192\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"href\", item_r3.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 6, item_r3.country));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.source);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 8, item_r3.published, \"medium\"));\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"compact\": a0\n  };\n};\nfunction NewsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, NewsComponent_div_0_h2_1_Template, 2, 0, \"h2\", 2);\n    i0.ɵɵelementStart(2, \"div\", 3);\n    i0.ɵɵtemplate(3, NewsComponent_div_0_a_3_Template, 17, 11, \"a\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r0.compact));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showTitle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.news)(\"ngForTrackBy\", ctx_r0.trackByTitle);\n  }\n}\nexport class NewsComponent {\n  constructor() {\n    this.news = [];\n    this.showTitle = true;\n    this.compact = false;\n  }\n  trackByTitle(index, item) {\n    return item.title;\n  }\n  static #_ = this.ɵfac = function NewsComponent_Factory(t) {\n    return new (t || NewsComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NewsComponent,\n    selectors: [[\"app-news\"]],\n    inputs: {\n      news: \"news\",\n      showTitle: \"showTitle\",\n      compact: \"compact\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"news-wrapper\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"news-wrapper\", 3, \"ngClass\"], [\"class\", \"section-title\", 4, \"ngIf\"], [1, \"news-grid\"], [\"class\", \"news-card\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"section-title\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"news-card\", 3, \"href\"], [1, \"news-card-header\"], [1, \"badge\"], [1, \"source\"], [1, \"title\"], [1, \"description\"], [1, \"news-card-footer\"], [1, \"published\"], [1, \"cta\"]],\n    template: function NewsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NewsComponent_div_0_Template, 4, 6, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.news && ctx.news.length);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.TitleCasePipe, i1.DatePipe],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n\\n\\n.news-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  line-height: 1.3;\\n  background: var(--gradient-dark);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  font-weight: var(--font-weight-semibold);\\n}\\n\\n.news-card[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n  color: var(--color-gray-600);\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%] {\\n  transition: all var(--transition-normal);\\n}\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px) scale(1.02);\\n  box-shadow: var(--shadow-glow);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbmV3cy9uZXdzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsY0FBQTtBQUNGOztBQUVBLGlGQUFBO0FBQ0E7RUFDRSxnQkFBQTtFQUNBLGdDQUFBO0VBQ0EsNkJBQUE7RUFDQSxvQ0FBQTtFQUNBLHFCQUFBO0VBQ0Esd0NBQUE7QUFDRjs7QUFFQTtFQUNFLG9CQUFBO0VBQ0EscUJBQUE7RUFDQSw0QkFBQTtFQUNBLGdCQUFBO0VBQ0EsNEJBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUVBLGtEQUFBO0FBQ0E7RUFDRSx3Q0FBQTtBQUNGO0FBQ0U7RUFDRSx1Q0FBQTtFQUNBLDhCQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbn1cclxuXHJcbi8qIEtlZXAgY29tcG9uZW50LXNwZWNpZmljIG92ZXJyaWRlcyBtaW5pbWFsOyBtb3N0IHN0eWxlcyBpbiBnbG9iYWwgc3R5bGVzLnNjc3MgKi9cclxuLm5ld3MtY2FyZCAudGl0bGUge1xyXG4gIGxpbmUtaGVpZ2h0OiAxLjM7XHJcbiAgYmFja2dyb3VuZDogdmFyKC0tZ3JhZGllbnQtZGFyayk7XHJcbiAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XHJcbiAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gIGJhY2tncm91bmQtY2xpcDogdGV4dDtcclxuICBmb250LXdlaWdodDogdmFyKC0tZm9udC13ZWlnaHQtc2VtaWJvbGQpO1xyXG59XHJcblxyXG4ubmV3cy1jYXJkIC5kZXNjcmlwdGlvbiB7XHJcbiAgZGlzcGxheTogLXdlYmtpdC1ib3g7XHJcbiAgLXdlYmtpdC1saW5lLWNsYW1wOiAzO1xyXG4gIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICBjb2xvcjogdmFyKC0tY29sb3ItZ3JheS02MDApO1xyXG4gIGxpbmUtaGVpZ2h0OiAxLjU7XHJcbn1cclxuXHJcbi8qIEVuaGFuY2VkIGhvdmVyIGVmZmVjdHMgZm9yIG5ld3MgY2FyZHMgaW4gY2hhdCAqL1xyXG4ubmV3cy13cmFwcGVyLmNvbXBhY3QgLm5ld3MtY2FyZCB7XHJcbiAgdHJhbnNpdGlvbjogYWxsIHZhcigtLXRyYW5zaXRpb24tbm9ybWFsKTtcclxuXHJcbiAgJjpob3ZlciB7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCkgc2NhbGUoMS4wMik7XHJcbiAgICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3ctZ2xvdyk7XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "item_r3", "link", "ɵɵsanitizeUrl", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "country", "source", "title", "description", "ɵɵpipeBind2", "published", "ɵɵtemplate", "NewsComponent_div_0_h2_1_Template", "NewsComponent_div_0_a_3_Template", "ɵɵpureFunction1", "_c0", "ctx_r0", "compact", "showTitle", "news", "trackByTitle", "NewsComponent", "constructor", "index", "item", "_", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "NewsComponent_Template", "rf", "ctx", "NewsComponent_div_0_Template", "length"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\news\\news.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\news\\news.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\n\nexport interface NewsItem {\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string; // ISO string\n  description: string;\n  fetched_at?: string;\n  // allow extra fields (e.g., id, created_at) from backend without strict typing\n  [key: string]: any;\n}\n\n@Component({\n  selector: 'app-news',\n  templateUrl: './news.component.html',\n  styleUrls: ['./news.component.scss']\n})\nexport class NewsComponent {\n  @Input() news: NewsItem[] = [];\n  @Input() showTitle: boolean = true;\n  @Input() compact: boolean = false;\n\n  trackByTitle(index: number, item: NewsItem) {\n    return item.title;\n  }\n}\n", "<div class=\"news-wrapper\" [ngClass]=\"{ 'compact': compact }\" *ngIf=\"news && news.length\">\n  <h2 class=\"section-title\" *ngIf=\"showTitle\">Latest News</h2>\n\n  <div class=\"news-grid\">\n    <a\n      class=\"news-card\"\n      *ngFor=\"let item of news; trackBy: trackByTitle\"\n      [href]=\"item.link\"\n      target=\"_blank\"\n      rel=\"noopener noreferrer\"\n    >\n      <div class=\"news-card-header\">\n        <span class=\"badge\">{{ item.country | titlecase }}</span>\n        <span class=\"source\">{{ item.source }}</span>\n      </div>\n\n      <h3 class=\"title\">{{ item.title }}</h3>\n      <p class=\"description\">{{ item.description }}</p>\n\n      <div class=\"news-card-footer\">\n        <span class=\"published\">{{ item.published | date:'medium' }}</span>\n        <span class=\"cta\">Read →</span>\n      </div>\n    </a>\n  </div>\n</div>\n"], "mappings": ";;;;ICCEA,EAAA,CAAAC,cAAA,YAA4C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG1DH,EAAA,CAAAC,cAAA,WAMC;IAEuBD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG/CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEjDH,EAAA,CAAAC,cAAA,eAA8B;IACJD,EAAA,CAAAE,MAAA,IAAoC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,mBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAdjCH,EAAA,CAAAI,UAAA,SAAAC,OAAA,CAAAC,IAAA,EAAAN,EAAA,CAAAO,aAAA,CAAkB;IAKIP,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAU,WAAA,OAAAL,OAAA,CAAAM,OAAA,EAA8B;IAC7BX,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAO,MAAA,CAAiB;IAGtBZ,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAQ,KAAA,CAAgB;IACXb,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAS,WAAA,CAAsB;IAGnBd,EAAA,CAAAQ,SAAA,GAAoC;IAApCR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAe,WAAA,QAAAV,OAAA,CAAAW,SAAA,YAAoC;;;;;;;;;;IApBpEhB,EAAA,CAAAC,cAAA,aAAyF;IACvFD,EAAA,CAAAiB,UAAA,IAAAC,iCAAA,gBAA4D;IAE5DlB,EAAA,CAAAC,cAAA,aAAuB;IACrBD,EAAA,CAAAiB,UAAA,IAAAE,gCAAA,iBAmBI;IACNnB,EAAA,CAAAG,YAAA,EAAM;;;;IAxBkBH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAoB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,OAAA,EAAkC;IAC/BvB,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAI,UAAA,SAAAkB,MAAA,CAAAE,SAAA,CAAe;IAKrBxB,EAAA,CAAAQ,SAAA,GAAS;IAATR,EAAA,CAAAI,UAAA,YAAAkB,MAAA,CAAAG,IAAA,CAAS,iBAAAH,MAAA,CAAAI,YAAA;;;ADahC,OAAM,MAAOC,aAAa;EAL1BC,YAAA;IAMW,KAAAH,IAAI,GAAe,EAAE;IACrB,KAAAD,SAAS,GAAY,IAAI;IACzB,KAAAD,OAAO,GAAY,KAAK;;EAEjCG,YAAYA,CAACG,KAAa,EAAEC,IAAc;IACxC,OAAOA,IAAI,CAACjB,KAAK;EACnB;EAAC,QAAAkB,CAAA,G;qBAPUJ,aAAa;EAAA;EAAA,QAAAK,EAAA,G;UAAbL,aAAa;IAAAM,SAAA;IAAAC,MAAA;MAAAT,IAAA;MAAAD,SAAA;MAAAD,OAAA;IAAA;IAAAY,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnB1BxC,EAAA,CAAAiB,UAAA,IAAAyB,4BAAA,iBAyBM;;;QAzBwD1C,EAAA,CAAAI,UAAA,SAAAqC,GAAA,CAAAhB,IAAA,IAAAgB,GAAA,CAAAhB,IAAA,CAAAkB,MAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}