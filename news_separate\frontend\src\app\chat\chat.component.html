<!-- chat.component.html -->

<!-- Anonymous user component runs in background -->
<app-anonymous-user></app-anonymous-user>

<div class="chat-container" *ngIf="isAuthenticated">
  <!-- Messages container - only show when there are messages -->
  <div 
    #messagesContainer
    class="messages-container" 
    (scroll)="onScroll($event)"
    *ngIf="messages.length > 0">
    
    <!-- Loading indicator for pagination at the top -->
    <div class="pagination-loading" *ngIf="isLoadingHistory">
      <div class="loading-spinner"></div>
    </div>
    
    <div class="message"
         *ngFor="let message of messages"
         [ngClass]="{'user-message': message.isUser, 'bot-message': !message.isUser}">
      <div class="message-content">
        <div class="message-text" [innerHTML]="formatMessageText(message.text)"></div>
        <div class="message-time">{{ message.timestamp | date:'short' }}</div>
      </div>

    </div>

    <!-- Loading indicator for current message -->
    <div class="message bot-message" *ngIf="isLoading">
      <div class="message-content">
        <div class="typing-indicator">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>
  </div>

  <!-- Input container - always visible -->
  <div class="input-container" [class.centered]="messages.length === 0" [class.bottom]="messages.length > 0">
    <div class="input-wrapper">
      <textarea
        #messageTextarea
        [(ngModel)]="currentMessage"
        (keydown)="onKeyPress($event)"
        (input)="adjustTextareaHeight($event)"
        placeholder="Ask anything"
        class="message-input"
        rows="1">
      </textarea>
      <button
        (click)="sendMessage()"
        class="send-button"
        [disabled]="!currentMessage.trim() || isLoading">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
        </svg>
      </button>
    </div>
  </div>
</div>

<!-- Show loading message while authenticating -->
<div *ngIf="!isAuthenticated" class="auth-loading">
  <div class="loading-spinner"></div>
  <p>Initializing chat...</p>
</div>