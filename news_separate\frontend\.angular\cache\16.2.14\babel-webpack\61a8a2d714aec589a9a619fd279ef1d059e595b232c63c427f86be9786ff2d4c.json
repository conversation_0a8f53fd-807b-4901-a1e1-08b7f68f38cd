{"ast": null, "code": "import { argsOrArgArray } from '../util/argsOrArgArray';\nimport { raceWith } from './raceWith';\nexport function race(...args) {\n  return raceWith(...argsOrArgArray(args));\n}", "map": {"version": 3, "names": ["argsOrArgArray", "raceWith", "race", "args"], "sources": ["C:/Users/<USER>/PycharmProjects/GenAI/Borys-Slavic-News-Chatbot/frontend/node_modules/rxjs/dist/esm/internal/operators/race.js"], "sourcesContent": ["import { argsOrArgArray } from '../util/argsOrArgArray';\nimport { raceWith } from './raceWith';\nexport function race(...args) {\n    return raceWith(...argsOrArgArray(args));\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,wBAAwB;AACvD,SAASC,QAAQ,QAAQ,YAAY;AACrC,OAAO,SAASC,IAAIA,CAAC,GAAGC,IAAI,EAAE;EAC1B,OAAOF,QAAQ,CAAC,GAAGD,cAAc,CAACG,IAAI,CAAC,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}