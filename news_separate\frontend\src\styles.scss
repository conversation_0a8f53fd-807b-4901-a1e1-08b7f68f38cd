/* ===== GLOBAL DESIGN SYSTEM ===== */

/* CSS Custom Properties for Consistent Design */
:root {
  /* Colors - Clean Professional Theme */
  --color-primary: #4f46e5;
  --color-primary-dark: #3730a3;
  --color-primary-light: #6366f1;
  --color-secondary: #059669;
  --color-secondary-dark: #047857;
  --color-accent: #0ea5e9;
  --color-accent-dark: #0284c7;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;

  /* Gradient Colors - Subtle and Professional */
  --gradient-primary: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
  --gradient-secondary: linear-gradient(135deg, #059669 0%, #10b981 100%);
  --gradient-accent: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
  --gradient-dark: linear-gradient(135deg, #374151 0%, #4b5563 100%);

  /* Neutral Colors */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows - Clean and Subtle */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-colored: 0 8px 25px -8px rgba(79, 70, 229, 0.15);
  --shadow-glow: 0 0 20px rgba(79, 70, 229, 0.1);

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== RESET & BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
  color: var(--color-gray-800);
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #cbd5e1 100%);
  background-attachment: fixed;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--space-4) 0;
  font-weight: var(--font-weight-semibold);
  line-height: 1.2;
  color: var(--color-gray-900);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
  margin: 0 0 var(--space-4) 0;
  color: var(--color-gray-700);
}

/* ===== FORM ELEMENTS ===== */
input, textarea, select {
  font-family: inherit;
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--color-gray-800);
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  padding: var(--space-3) var(--space-4);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);

  &:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  }

  &::placeholder {
    color: var(--color-gray-500);
  }
}

/* ===== BUTTONS ===== */
button {
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  color: var(--color-white);
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-5);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-normal);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-colored);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow-sm);

    &::before {
      display: none;
    }
  }
}

/* Button Variants */
.btn-secondary {
  background: var(--gradient-secondary);
  color: var(--color-white);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-colored);
  }
}

.btn-accent {
  background: var(--gradient-accent);
  color: var(--color-white);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
  }
}

.btn-sm {
  font-size: var(--font-size-sm);
  padding: var(--space-2) var(--space-3);
}

.btn-lg {
  font-size: var(--font-size-lg);
  padding: var(--space-4) var(--space-6);
}


/* ===== SHARED: News grid & card (Tailwind-inspired) ===== */
.news-wrapper {
  max-width: 1100px;
  margin: 0 auto;
  padding: var(--space-6) var(--space-6);
}

.section-title {
  margin-bottom: var(--space-6);
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  align-items: stretch;
}

.news-card {
  display: flex;
  flex-direction: column;
  text-decoration: none;
  color: inherit;
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, transparent 50%);
    opacity: 0;
    transition: opacity var(--transition-normal);
  }
}

.news-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(79, 70, 229, 0.1);
  border-color: rgba(79, 70, 229, 0.2);

  &::before {
    transform: scaleX(1);
  }

  &::after {
    opacity: 1;
  }
}

.news-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.badge {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--color-white);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border-radius: 999px;
  padding: 0.375rem 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.source {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

.title {
  font-size: var(--font-size-lg);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
}

.description {
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
  margin: 0 0 var(--space-4) 0;
}

.news-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
}

.cta {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);

  &:hover {
    color: var(--color-primary-dark);
  }
}

.empty-state {
  text-align: center;
  color: var(--color-gray-600);
}

@media (max-width: 768px) {
  .news-wrapper {
    padding: var(--space-5) var(--space-4);
  }
  .news-grid {
    gap: var(--space-4);
    grid-template-columns: 1fr;
  }
}

/* ===== Chat-embedded news wrapper tweaks ===== */
.message-news {
  margin-top: var(--space-3);
}

/* Compact mode for NewsComponent (used under chat bubble) */
.news-wrapper.compact {
  padding: var(--space-4);
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.news-wrapper.compact .news-grid {
  gap: var(--space-4);
  grid-template-columns: 1fr;
}

/* Make the compact wrapper scrollable if height grows too much */
.news-wrapper.compact {
  max-height: 320px;
  overflow-y: auto;
}

.news-wrapper.compact::-webkit-scrollbar {
  width: 6px;
}

.news-wrapper.compact::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-md);
}

/* Make news wrapper in chat scrollable when not compact but in chat context */
.message-news .news-wrapper {
  max-height: 500px;
  overflow-y: auto;
}

.message-news .news-wrapper::-webkit-scrollbar {
  width: 6px;
}

.message-news .news-wrapper::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: var(--radius-md);
}

.message-news .news-wrapper::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.message-news .news-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

