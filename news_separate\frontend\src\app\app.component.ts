import { Component } from '@angular/core';
import { NewsItem } from './news/news.component';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent {
  title = 'News Agent';

  // Sample news data to demonstrate search and filters
  sampleNews: NewsItem[] = [
    {
      country: 'ukraine',
      source: 'Kyiv Independent',
      title: 'Ukraine receives new military aid package',
      link: 'https://example.com/news1',
      published: '2024-01-15T10:30:00Z',
      description: 'NATO allies announce comprehensive military aid package including advanced defense systems.'
    },
    {
      country: 'poland',
      source: 'Warsaw Times',
      title: 'Poland strengthens eastern border security',
      link: 'https://example.com/news2',
      published: '2024-01-14T15:45:00Z',
      description: 'Polish government implements enhanced security protocols along its eastern border.'
    },
    {
      country: 'russia',
      source: 'Moscow Herald',
      title: 'Russian economic indicators show mixed results',
      link: 'https://example.com/news3',
      published: '2024-01-13T09:15:00Z',
      description: 'Latest economic data from Russia reveals varying performance across different sectors.'
    },
    {
      country: 'belarus',
      source: 'Minsk Daily',
      title: 'Belarus announces new agricultural initiatives',
      link: 'https://example.com/news4',
      published: '2024-01-12T14:20:00Z',
      description: 'Government unveils comprehensive agricultural development program.'
    },
    {
      country: 'ukraine',
      source: 'Ukrainian Voice',
      title: 'Reconstruction efforts accelerate in liberated territories',
      link: 'https://example.com/news5',
      published: '2024-01-11T11:00:00Z',
      description: 'International cooperation drives rapid reconstruction of infrastructure.'
    }
  ];
}
