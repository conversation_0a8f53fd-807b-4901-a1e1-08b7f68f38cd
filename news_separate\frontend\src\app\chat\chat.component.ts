import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ViewChild, AfterViewChecked } from '@angular/core';
import { AuthService } from '../auth.service'; // Adjust path as needed
import { marked } from 'marked';

interface NewsArticle {
  id?: number;
  country: string;
  source: string;
  title: string;
  link: string;
  published: string;
  description: string;
  fetched_at?: string;
  created_at?: string;
}

interface Message {
  id?: number;
  text: string;
  isUser: boolean;
  timestamp: Date;
  session?: number;
  prompt?: number;
  model?: number;
  news_articles?: NewsArticle[]; // for bot messages that include news
}

// Django ChatMessage structure
interface ChatMessage {
  id: number;
  session: number;
  sender: 'user' | 'bot';
  message: string;
  timestamp: string;
  prompt: number | null;
  model: number | null;
  news_articles?: NewsArticle[] | null;
}

// Django paginated response
interface ChatHistoryResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ChatMessage[];
}

// Django chatbot response
interface ChatbotResponse {
  user_message: ChatMessage;
  bot_message: ChatMessage;
}

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss']
})
export class ChatComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  
  messages: Message[] = [];
  currentMessage: string = '';
  isLoading: boolean = false;
  isAuthenticated: boolean = false;
  currentSessionId: number | null = null;
  
  // Pagination
  currentPage: number = 1;
  hasNextPage: boolean = false;
  isLoadingHistory: boolean = false;
  
  // Scroll management
  private shouldScrollToBottom: boolean = true;
  private lastScrollHeight: number = 0;
  private initialLoadComplete: boolean = false;
  private userHasScrolled: boolean = false;
  private scrollTimeout: any;

  constructor(private authService: AuthService) {}

  ngOnInit() {
    // Ensure user is authenticated before initializing chat
    this.authService.ensureAuthenticated().subscribe({
      next: (token) => {
        console.log('User authenticated successfully');
        this.isAuthenticated = true;
        this.loadChatHistory(1, false);
      },
      error: (error) => {
        console.error('Authentication failed:', error);
        this.isAuthenticated = false;
      }
    });
  }

  ngAfterViewChecked() {
    // Auto-scroll to bottom only for new messages
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  // Listen for scroll events to load more history
  onScroll(event: any) {
    const element = event.target;
    const scrollTop = element.scrollTop;
    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;
    
    // Mark that user has scrolled manually (not programmatic)
    if (this.initialLoadComplete) {
      this.userHasScrolled = true;
    }
    
    // Clear existing timeout
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    
    // Debounce scroll events and check conditions
    this.scrollTimeout = setTimeout(() => {
      // Only load more if:
      // 1. Initial load is complete
      // 2. User has scrolled manually at least once
      // 3. User is near the top (scrollTop < 100)
      // 4. There are more pages to load
      // 5. Not currently loading
      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)
      const isNearTop = scrollTop < 100;
      const isNotAtBottom = scrollTop < (scrollHeight - clientHeight - 50);
      
      if (isNearTop && 
          isNotAtBottom &&
          this.hasNextPage && 
          !this.isLoadingHistory && 
          this.initialLoadComplete &&
          this.userHasScrolled) {
        this.lastScrollHeight = scrollHeight;
        this.loadMoreHistory();
      }
    }, 100); // 100ms debounce
  }

  loadChatHistory(page: number = 1, append: boolean = false) {
    if (!this.isAuthenticated) return;

    this.isLoadingHistory = true;

    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({
      next: (response: ChatHistoryResponse) => {
        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));
        
        if (append) {
          // For pagination - reverse the new messages (since API returns newest first)
          // and prepend older messages to beginning
          const reversedNewMessages = [...newMessages].reverse();
          this.messages = [...reversedNewMessages, ...this.messages];
          this.maintainScrollPosition();
        } else {
          // For initial load - reverse messages to get chronological order (oldest first)
          this.messages = [...newMessages].reverse();
          this.shouldScrollToBottom = true;
          
          // Set initial load complete after scroll positioning is done
          setTimeout(() => {
            this.initialLoadComplete = true;
          }, 200);
        }
        
        // Update pagination info
        this.currentPage = page;
        this.hasNextPage = !!response.next;
        
        this.isLoadingHistory = false;
      },
      error: (error) => {
        console.error('Error loading chat history:', error);
        this.isLoadingHistory = false;
        
        // If this was the initial load, still mark it as complete after delay
        if (!append) {
          setTimeout(() => {
            this.initialLoadComplete = true;
          }, 200);
        }
      }
    });
  }

  // Convert Django ChatMessage to frontend Message format
  private convertChatMessageToMessage(chatMessage: ChatMessage): Message {
    return {
      id: chatMessage.id,
      text: chatMessage.message,
      isUser: chatMessage.sender === 'user',
      timestamp: new Date(chatMessage.timestamp),
      session: chatMessage.session,
      prompt: chatMessage.prompt || undefined,
      model: chatMessage.model || undefined,
      news_articles: chatMessage.news_articles || undefined,
    };
  }

  // Load more chat history (pagination) - triggered by scroll
  loadMoreHistory() {
    if (this.hasNextPage && !this.isLoadingHistory) {
      this.loadChatHistory(this.currentPage + 1, true);
    }
  }

  // Maintain scroll position when loading older messages
  private maintainScrollPosition() {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        const newScrollHeight = element.scrollHeight;
        const scrollDifference = newScrollHeight - this.lastScrollHeight;
        element.scrollTop = scrollDifference;
      }
    }, 50);
  }

  sendMessage() {
    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {
      return;
    }

    // Store the message to send
    const messageToSend = this.currentMessage;
    this.currentMessage = '';

    // Create temporary user message and add it instantly
    const tempUserMessage: Message = {
      text: messageToSend,
      isUser: true,
      timestamp: new Date()
    };

    // Add user message instantly to the chat
    this.messages.push(tempUserMessage);
    this.shouldScrollToBottom = true;

    // Set loading state
    this.isLoading = true;

    // Call the API through auth service
    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({
      next: (response: ChatbotResponse) => {
        // Convert backend messages
        const userMessage = this.convertChatMessageToMessage(response.user_message);
        const botMessage = this.convertChatMessageToMessage(response.bot_message);

        // Replace the temporary user message with the one from backend
        const lastMessageIndex = this.messages.length - 1;
        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {
          this.messages[lastMessageIndex] = userMessage;
        }

        // Add bot message
        this.messages.push(botMessage);

        // Store session ID for future requests
        if (!this.currentSessionId) {
          this.currentSessionId = response.user_message.session;
        }

        this.isLoading = false;
        this.shouldScrollToBottom = true;
      },
      error: (error) => {
        console.error('Error sending message:', error);
        const errorMessage: Message = {
          text: 'Sorry, there was an error processing your message. Please try again.',
          isUser: false,
          timestamp: new Date()
        };
        this.messages.push(errorMessage);
        this.isLoading = false;
        this.shouldScrollToBottom = true;
      }
    });
  }

  clearHistory() {
    this.messages = [];
    this.currentPage = 1;
    this.hasNextPage = false;
    this.initialLoadComplete = false;
    this.userHasScrolled = false;
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
  }

  refreshHistory() {
    this.currentPage = 1;
    this.initialLoadComplete = false;
    this.userHasScrolled = false;
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    this.loadChatHistory();
  }

  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  adjustTextareaHeight(event: any) {
    const textarea = event.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  }

  private scrollToBottom() {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 50);
  }

  // Format message text using marked library for markdown
  formatMessageText(text: string): string {
    if (!text) return '';

    try {
      // Configure marked to be more restrictive for security
      marked.setOptions({
        breaks: true, // Convert line breaks to <br>
        gfm: true // Enable GitHub Flavored Markdown
      });

      // Convert markdown to HTML using marked (synchronous)
      const htmlContent = marked.parse(text) as string;

      // Basic sanitization - remove script tags and dangerous attributes
      let sanitizedHtml = htmlContent
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');

      return sanitizedHtml;
    } catch (error) {
      console.error('Error formatting message text:', error);
      // Fallback to plain text with basic line break conversion
      return text.replace(/\n/g, '<br>');
    }
  }

  ngOnDestroy() {
    // Clean up any pending timeouts
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
  }
}