{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./news/news.component\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'News Agent';\n    // Sample news data to demonstrate search and filters\n    this.sampleNews = [{\n      country: 'ukraine',\n      source: 'Kyiv Independent',\n      title: 'Ukraine receives new military aid package',\n      link: 'https://example.com/news1',\n      published: '2024-01-15T10:30:00Z',\n      description: 'NATO allies announce comprehensive military aid package including advanced defense systems.'\n    }, {\n      country: 'poland',\n      source: 'Warsaw Times',\n      title: 'Poland strengthens eastern border security',\n      link: 'https://example.com/news2',\n      published: '2024-01-14T15:45:00Z',\n      description: 'Polish government implements enhanced security protocols along its eastern border.'\n    }, {\n      country: 'russia',\n      source: 'Moscow Herald',\n      title: 'Russian economic indicators show mixed results',\n      link: 'https://example.com/news3',\n      published: '2024-01-13T09:15:00Z',\n      description: 'Latest economic data from Russia reveals varying performance across different sectors.'\n    }, {\n      country: 'belarus',\n      source: 'Minsk Daily',\n      title: 'Belarus announces new agricultural initiatives',\n      link: 'https://example.com/news4',\n      published: '2024-01-12T14:20:00Z',\n      description: 'Government unveils comprehensive agricultural development program.'\n    }, {\n      country: 'ukraine',\n      source: 'Ukrainian Voice',\n      title: 'Reconstruction efforts accelerate in liberated territories',\n      link: 'https://example.com/news5',\n      published: '2024-01-11T11:00:00Z',\n      description: 'International cooperation drives rapid reconstruction of infrastructure.'\n    }];\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 8,\n    vars: 1,\n    consts: [[1, \"app-container\"], [1, \"app-header\"], [1, \"main-content\"], [1, \"chat-section\"], [1, \"news-section\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"h1\");\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"main\", 2);\n        i0.ɵɵelement(5, \"div\", 3);\n        i0.ɵɵelementStart(6, \"div\", 4);\n        i0.ɵɵelement(7, \"app-news\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.title);\n      }\n    },\n    dependencies: [i1.NewsComponent],\n    styles: [\"\\n\\n.app-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  background: transparent;\\n  overflow: hidden;\\n}\\n\\n.app-header[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding: var(--space-4) 0;\\n  background: transparent;\\n  border-bottom: 1px solid var(--color-gray-200);\\n}\\n.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 50%, #8b5cf6 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  margin: 0;\\n  padding: var(--space-4) 0;\\n  font-size: var(--font-size-2xl);\\n  font-weight: var(--font-weight-bold);\\n  letter-spacing: -0.025em;\\n  position: relative;\\n}\\n.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(90deg, #4f46e5 0%, #8b5cf6 100%);\\n  border-radius: 2px;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  overflow: hidden;\\n  background-color: transparent;\\n  gap: var(--space-4);\\n  padding: var(--space-4);\\n}\\n\\n.chat-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0; \\n\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.news-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0; \\n\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  background: var(--color-white);\\n  border: 1px solid var(--color-gray-200);\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-sm);\\n  \\n\\n}\\n.news-section[_ngcontent-%COMP%]   app-news[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  \\n\\n}\\n.news-section[_ngcontent-%COMP%]   app-news[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.news-section[_ngcontent-%COMP%]   app-news[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-gray-100);\\n  border-radius: var(--radius-md);\\n}\\n.news-section[_ngcontent-%COMP%]   app-news[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-gray-300);\\n  border-radius: var(--radius-md);\\n  -webkit-transition: var(--transition-fast);\\n  transition: var(--transition-fast);\\n}\\n.news-section[_ngcontent-%COMP%]   app-news[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-gray-400);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .app-container[_ngcontent-%COMP%] {\\n    height: 100vh;\\n  }\\n  .app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: var(--font-size-xl);\\n    padding: var(--space-3) 0;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--space-3);\\n    padding: var(--space-3);\\n  }\\n  .chat-section[_ngcontent-%COMP%], .news-section[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 0;\\n  }\\n  \\n\\n  .chat-section[_ngcontent-%COMP%] {\\n    max-height: 50vh;\\n  }\\n  .news-section[_ngcontent-%COMP%] {\\n    max-height: 50vh;\\n  }\\n}\\n\\n\\n.sr-only[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border: 0;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.text-left[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n\\n.text-right[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.mb-0[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.mb-2[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-2);\\n}\\n\\n.mb-4[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n}\\n\\n.mb-6[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-6);\\n}\\n\\n.mt-0[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n}\\n\\n.mt-2[_ngcontent-%COMP%] {\\n  margin-top: var(--space-2);\\n}\\n\\n.mt-4[_ngcontent-%COMP%] {\\n  margin-top: var(--space-4);\\n}\\n\\n.mt-6[_ngcontent-%COMP%] {\\n  margin-top: var(--space-6);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "sampleNews", "country", "source", "link", "published", "description", "_", "_2", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { NewsItem } from './news/news.component';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = 'News Agent';\n\n  // Sample news data to demonstrate search and filters\n  sampleNews: NewsItem[] = [\n    {\n      country: 'ukraine',\n      source: 'Kyiv Independent',\n      title: 'Ukraine receives new military aid package',\n      link: 'https://example.com/news1',\n      published: '2024-01-15T10:30:00Z',\n      description: 'NATO allies announce comprehensive military aid package including advanced defense systems.'\n    },\n    {\n      country: 'poland',\n      source: 'Warsaw Times',\n      title: 'Poland strengthens eastern border security',\n      link: 'https://example.com/news2',\n      published: '2024-01-14T15:45:00Z',\n      description: 'Polish government implements enhanced security protocols along its eastern border.'\n    },\n    {\n      country: 'russia',\n      source: 'Moscow Herald',\n      title: 'Russian economic indicators show mixed results',\n      link: 'https://example.com/news3',\n      published: '2024-01-13T09:15:00Z',\n      description: 'Latest economic data from Russia reveals varying performance across different sectors.'\n    },\n    {\n      country: 'belarus',\n      source: 'Minsk Daily',\n      title: 'Belarus announces new agricultural initiatives',\n      link: 'https://example.com/news4',\n      published: '2024-01-12T14:20:00Z',\n      description: 'Government unveils comprehensive agricultural development program.'\n    },\n    {\n      country: 'ukraine',\n      source: 'Ukrainian Voice',\n      title: 'Reconstruction efforts accelerate in liberated territories',\n      link: 'https://example.com/news5',\n      published: '2024-01-11T11:00:00Z',\n      description: 'International cooperation drives rapid reconstruction of infrastructure.'\n    }\n  ];\n}\n", "<div class=\"app-container\">\n  <header class=\"app-header\">\n    <h1>{{ title }}</h1>\n  </header>\n\n  <main class=\"main-content\">\n    <!-- Left side: Chatbot -->\n    <div class=\"chat-section\">\n      <!-- <app-chat></app-chat> -->\n    </div>\n\n    <!-- Right side: News -->\n    <div class=\"news-section\">\n      <app-news></app-news>\n    </div>\n  </main>\n</div>\n"], "mappings": ";;AAQA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,YAAY;IAEpB;IACA,KAAAC,UAAU,GAAe,CACvB;MACEC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,kBAAkB;MAC1BH,KAAK,EAAE,2CAA2C;MAClDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,cAAc;MACtBH,KAAK,EAAE,4CAA4C;MACnDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,eAAe;MACvBH,KAAK,EAAE,gDAAgD;MACvDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,aAAa;MACrBH,KAAK,EAAE,gDAAgD;MACvDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,iBAAiB;MACzBH,KAAK,EAAE,4DAA4D;MACnEI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,CACF;;EACF,QAAAC,CAAA,G;qBA9CYT,YAAY;EAAA;EAAA,QAAAU,EAAA,G;UAAZV,YAAY;IAAAW,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRzBE,EAAA,CAAAC,cAAA,aAA2B;QAEnBD,EAAA,CAAAE,MAAA,GAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGtBH,EAAA,CAAAC,cAAA,cAA2B;QAEzBD,EAAA,CAAAI,SAAA,aAEM;QAGNJ,EAAA,CAAAC,cAAA,aAA0B;QACxBD,EAAA,CAAAI,SAAA,eAAqB;QACvBJ,EAAA,CAAAG,YAAA,EAAM;;;QAZFH,EAAA,CAAAK,SAAA,GAAW;QAAXL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAAhB,KAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}