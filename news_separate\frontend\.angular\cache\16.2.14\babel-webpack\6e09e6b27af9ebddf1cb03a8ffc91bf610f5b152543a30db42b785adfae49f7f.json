{"ast": null, "code": "import { observeNotification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function dematerialize() {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, notification => observeNotification(notification, subscriber)));\n  });\n}", "map": {"version": 3, "names": ["observeNotification", "operate", "createOperatorSubscriber", "dematerialize", "source", "subscriber", "subscribe", "notification"], "sources": ["C:/Users/<USER>/PycharmProjects/GenAI/Borys-Slavic-News-Chatbot/news_separate/frontend/node_modules/rxjs/dist/esm/internal/operators/dematerialize.js"], "sourcesContent": ["import { observeNotification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function dematerialize() {\n    return operate((source, subscriber) => {\n        source.subscribe(createOperatorSubscriber(subscriber, (notification) => observeNotification(notification, subscriber)));\n    });\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,iBAAiB;AACrD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC5B,OAAOF,OAAO,CAAC,CAACG,MAAM,EAAEC,UAAU,KAAK;IACnCD,MAAM,CAACE,SAAS,CAACJ,wBAAwB,CAACG,UAAU,EAAGE,YAAY,IAAKP,mBAAmB,CAACO,YAAY,EAAEF,UAAU,CAAC,CAAC,CAAC;EAC3H,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}