{"ast": null, "code": "import { marked } from 'marked';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../anonymous-user/anonymous-user.component\";\nimport * as i5 from \"../news/news.component\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatComponent_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_1_div_1_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵelement(1, \"app-news\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"news\", message_r8.news_articles)(\"showTitle\", false)(\"compact\", false);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatComponent_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"div\", 20);\n    i0.ɵɵelementStart(3, \"div\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ChatComponent_div_1_div_1_div_3_div_6_Template, 2, 3, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r8 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c1, message_r8.isUser, !message_r8.isUser));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r6.formatMessageText(message_r8.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 4, message_r8.timestamp, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !message_r8.isUser && message_r8.news_articles && message_r8.news_articles.length);\n  }\n}\nfunction ChatComponent_div_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 19)(2, \"div\", 26);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11, 12);\n    i0.ɵɵlistener(\"scroll\", function ChatComponent_div_1_div_1_Template_div_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onScroll($event));\n    });\n    i0.ɵɵtemplate(2, ChatComponent_div_1_div_1_div_2_Template, 2, 0, \"div\", 13);\n    i0.ɵɵtemplate(3, ChatComponent_div_1_div_1_div_3_Template, 7, 10, \"div\", 14);\n    i0.ɵɵtemplate(4, ChatComponent_div_1_div_1_div_4_Template, 6, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ChatComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, ChatComponent_div_1_div_1_Template, 5, 3, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\", 5)(4, \"textarea\", 6, 7);\n    i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_div_1_Template_textarea_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.currentMessage = $event);\n    })(\"keydown\", function ChatComponent_div_1_Template_textarea_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onKeyPress($event));\n    })(\"input\", function ChatComponent_div_1_Template_textarea_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.adjustTextareaHeight($event));\n    });\n    i0.ɵɵtext(6, \"      \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_1_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.sendMessage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 9);\n    i0.ɵɵelement(9, \"path\", 10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"centered\", ctx_r0.messages.length === 0)(\"bottom\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.currentMessage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.currentMessage.trim() || ctx_r0.isLoading);\n  }\n}\nfunction ChatComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Initializing chat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ChatComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.messages = [];\n    this.currentMessage = '';\n    this.isLoading = false;\n    this.isAuthenticated = false;\n    this.currentSessionId = null;\n    // Pagination\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.isLoadingHistory = false;\n    // Scroll management\n    this.shouldScrollToBottom = true;\n    this.lastScrollHeight = 0;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n  }\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: token => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory(1, false);\n      },\n      error: error => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n  // Listen for scroll events to load more history\n  onScroll(event) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    const clientHeight = element.clientHeight;\n    // Mark that user has scrolled manually (not programmatic)\n    if (this.initialLoadComplete) {\n      this.userHasScrolled = true;\n    }\n    // Clear existing timeout\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    // Debounce scroll events and check conditions\n    this.scrollTimeout = setTimeout(() => {\n      // Only load more if:\n      // 1. Initial load is complete\n      // 2. User has scrolled manually at least once\n      // 3. User is near the top (scrollTop < 100)\n      // 4. There are more pages to load\n      // 5. Not currently loading\n      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n      const isNearTop = scrollTop < 100;\n      const isNotAtBottom = scrollTop < scrollHeight - clientHeight - 50;\n      if (isNearTop && isNotAtBottom && this.hasNextPage && !this.isLoadingHistory && this.initialLoadComplete && this.userHasScrolled) {\n        this.lastScrollHeight = scrollHeight;\n        this.loadMoreHistory();\n      }\n    }, 100); // 100ms debounce\n  }\n\n  loadChatHistory(page = 1, append = false) {\n    if (!this.isAuthenticated) return;\n    this.isLoadingHistory = true;\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        if (append) {\n          // For pagination - reverse the new messages (since API returns newest first)\n          // and prepend older messages to beginning\n          const reversedNewMessages = [...newMessages].reverse();\n          this.messages = [...reversedNewMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - reverse messages to get chronological order (oldest first)\n          this.messages = [...newMessages].reverse();\n          this.shouldScrollToBottom = true;\n          // Set initial load complete after scroll positioning is done\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        this.isLoadingHistory = false;\n      },\n      error: error => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        // If this was the initial load, still mark it as complete after delay\n        if (!append) {\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n      }\n    });\n  }\n  // Convert Django ChatMessage to frontend Message format\n  convertChatMessageToMessage(chatMessage) {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined,\n      news_articles: chatMessage.news_articles || undefined\n    };\n  }\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n  // Maintain scroll position when loading older messages\n  maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    // Create temporary user message and add it instantly\n    const tempUserMessage = {\n      text: messageToSend,\n      isUser: true,\n      timestamp: new Date()\n    };\n    // Add user message instantly to the chat\n    this.messages.push(tempUserMessage);\n    this.shouldScrollToBottom = true;\n    // Set loading state\n    this.isLoading = true;\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        // Convert backend messages\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n        // Replace the temporary user message with the one from backend\n        const lastMessageIndex = this.messages.length - 1;\n        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n          this.messages[lastMessageIndex] = userMessage;\n        }\n        // Add bot message\n        this.messages.push(botMessage);\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        const errorMessage = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n  clearHistory() {\n    this.messages = [];\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n  refreshHistory() {\n    this.currentPage = 1;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.loadChatHistory();\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  adjustTextareaHeight(event) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n  // Format message text using marked library for markdown\n  formatMessageText(text) {\n    if (!text) return '';\n    try {\n      // Configure marked to be more restrictive for security\n      marked.setOptions({\n        breaks: true,\n        gfm: true // Enable GitHub Flavored Markdown\n      });\n      // Convert markdown to HTML using marked (synchronous)\n      const htmlContent = marked.parse(text);\n      // Basic sanitization - remove script tags and dangerous attributes\n      let sanitizedHtml = htmlContent.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '').replace(/javascript:/gi, '').replace(/on\\w+\\s*=/gi, '');\n      return sanitizedHtml;\n    } catch (error) {\n      console.error('Error formatting message text:', error);\n      // Fallback to plain text with basic line break conversion\n      return text.replace(/\\n/g, '<br>');\n    }\n  }\n  ngOnDestroy() {\n    // Clean up any pending timeouts\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function ChatComponent_Factory(t) {\n    return new (t || ChatComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChatComponent,\n    selectors: [[\"app-chat\"]],\n    viewQuery: function ChatComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n      }\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"chat-container\", 4, \"ngIf\"], [\"class\", \"auth-loading\", 4, \"ngIf\"], [1, \"chat-container\"], [\"class\", \"messages-container\", 3, \"scroll\", 4, \"ngIf\"], [1, \"input-container\"], [1, \"input-wrapper\"], [\"placeholder\", \"Ask anything\", \"rows\", \"1\", 1, \"message-input\", 3, \"ngModel\", \"ngModelChange\", \"keydown\", \"input\"], [\"messageTextarea\", \"\"], [1, \"send-button\", 3, \"disabled\", \"click\"], [\"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M2 21L23 12L2 3V10L17 12L2 14V21Z\", \"fill\", \"currentColor\"], [1, \"messages-container\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"pagination-loading\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"message bot-message\", 4, \"ngIf\"], [1, \"pagination-loading\"], [1, \"loading-spinner\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-text\", 3, \"innerHTML\"], [1, \"message-time\"], [\"class\", \"message-news\", 4, \"ngIf\"], [1, \"message-news\"], [3, \"news\", \"showTitle\", \"compact\"], [1, \"message\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"auth-loading\"]],\n    template: function ChatComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-anonymous-user\");\n        i0.ɵɵtemplate(1, ChatComponent_div_1_Template, 10, 7, \"div\", 0);\n        i0.ɵɵtemplate(2, ChatComponent_div_2_Template, 4, 0, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.AnonymousUserComponent, i5.NewsComponent, i2.DatePipe],\n    styles: [\"\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  max-width: 900px;\\n  margin: 0 auto;\\n  background: linear-gradient(to bottom, #ffffff 0%, #fefefe 100%);\\n  position: relative;\\n  overflow: hidden;\\n  font-family: var(--font-family-primary);\\n  border-radius: var(--radius-2xl);\\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);\\n  border: 1px solid rgba(255, 255, 255, 0.8);\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: var(--space-6) var(--space-8) 120px var(--space-8);\\n  background: #f9fafb;\\n  max-height: calc(100vh - 160px);\\n  scroll-behavior: smooth;\\n  position: relative;\\n  z-index: 10;\\n  \\n\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f5f9;\\n  border-radius: var(--radius-md);\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e1;\\n  border-radius: var(--radius-md);\\n  -webkit-transition: var(--transition-fast);\\n  transition: var(--transition-fast);\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #94a3b8;\\n}\\n\\n\\n\\n.pagination-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: var(--space-4) 0;\\n  margin-bottom: var(--space-4);\\n}\\n.pagination-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 2px solid #e2e8f0;\\n  border-top: 2px solid #bdf2bd;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n  display: flex;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.3s ease-out;\\n  position: relative;\\n}\\n.message.user-message[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);\\n  color: #ffffff;\\n  max-width: 75%;\\n  border-radius: 20px 20px 6px 20px;\\n  box-shadow: 0 3px 12px rgba(79, 70, 229, 0.25);\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  border: none;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.3);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.15);\\n  color: #059669;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.05);\\n  border-color: rgba(0, 0, 0, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  color: #047857;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #059669;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #047857;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: #059669;\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(0, 0, 0, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.message.bot-message[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  \\n\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\\n  color: var(--color-gray-800);\\n  max-width: 75%;\\n  border-radius: 20px 20px 20px 6px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  border: 1px solid var(--color-gray-200);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\\n  border-color: var(--color-gray-300);\\n  \\n\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover   .message-news[_ngcontent-%COMP%] {\\n  margin-top: var(--space-3);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message.bot-message[_ngcontent-%COMP%] {\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-news[_ngcontent-%COMP%] {\\n  max-width: 80%;\\n  margin-left: 0;\\n  align-self: flex-start;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: var(--space-4) var(--space-5);\\n  word-wrap: break-word;\\n  position: relative;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-base);\\n  line-height: 1.6;\\n  margin: 0;\\n  font-weight: var(--font-weight-normal);\\n  word-break: break-word;\\n}\\n.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--space-3) 0;\\n}\\n.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: var(--space-4) 0 var(--space-2) 0;\\n  font-weight: var(--font-weight-semibold);\\n  line-height: 1.3;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.1em;\\n}\\n.message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-size: 1em;\\n}\\n.message-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   b[_ngcontent-%COMP%] {\\n  font-weight: var(--font-weight-semibold);\\n}\\n.message-text[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-style: italic;\\n}\\n.message-text[_ngcontent-%COMP%]   del[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   strike[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n}\\n.message-text[_ngcontent-%COMP%]   u[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: var(--space-3) 0;\\n  padding-left: var(--space-6);\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: var(--space-1) 0;\\n  line-height: 1.5;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  margin: var(--space-4) 0;\\n  padding: var(--space-3) var(--space-4);\\n  border-left: 4px solid #e5e7eb;\\n  background: rgba(0, 0, 0, 0.02);\\n  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;\\n  font-style: italic;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.08);\\n  padding: 2px 6px;\\n  border-radius: var(--radius-sm);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 0.9em;\\n  color: #e11d48;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: var(--radius-md);\\n  padding: var(--space-4);\\n  margin: var(--space-4) 0;\\n  overflow-x: auto;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: none;\\n  padding: 0;\\n  color: #334155;\\n  font-size: 0.875em;\\n}\\n.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #2563eb;\\n  text-decoration: underline;\\n  transition: var(--transition-fast);\\n}\\n.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #1d4ed8;\\n  text-decoration: none;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  margin: var(--space-4) 0;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: var(--space-2) var(--space-3);\\n  border: 1px solid #e2e8f0;\\n  text-align: left;\\n}\\n.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  font-weight: var(--font-weight-semibold);\\n}\\n.message-text[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  border-radius: var(--radius-sm);\\n  margin: var(--space-2) 0;\\n}\\n\\n.message-time[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-xs);\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-top: var(--space-2);\\n  text-align: right;\\n  opacity: 0.8;\\n  transition: var(--transition-fast);\\n  font-weight: var(--font-weight-normal);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: left;\\n  color: var(--color-gray-600);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: right;\\n  color: var(--color-gray-600);\\n}\\n\\n.message[_ngcontent-%COMP%]:hover   .message-time[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  padding: var(--space-6) var(--space-8) var(--space-8) var(--space-8);\\n  background: linear-gradient(to top, #ffffff 0%, #fefefe 100%);\\n  position: fixed;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 100%;\\n  max-width: 900px;\\n  z-index: 50;\\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\\n  transition: var(--transition-fast);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.input-container.centered[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 100%;\\n  max-width: 600px;\\n  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);\\n  z-index: 50;\\n  padding: var(--space-8);\\n  bottom: auto;\\n  border-radius: var(--radius-2xl);\\n  border: 1px solid rgba(0, 0, 0, 0.06);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12), 0 8px 24px rgba(0, 0, 0, 0.08);\\n}\\n.input-container.bottom[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\\n  background: linear-gradient(to top, #ffffff 0%, #fefefe 100%);\\n  width: 100%;\\n  max-width: 900px;\\n  z-index: 50;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.input-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: var(--space-4);\\n  border: 2px solid var(--color-gray-200);\\n  border-radius: 28px;\\n  padding: var(--space-4) var(--space-5);\\n  background: var(--color-white);\\n  transition: all var(--transition-normal);\\n  position: relative;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n}\\n.input-wrapper[_ngcontent-%COMP%]:focus-within {\\n  border-color: var(--color-primary);\\n  background: var(--color-white);\\n  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.08), 0 4px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n.input-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-gray-300);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  background: transparent;\\n  resize: none;\\n  font-size: var(--font-size-base);\\n  line-height: 1.6;\\n  padding: var(--space-2) 0;\\n  min-height: 28px;\\n  max-height: 120px;\\n  font-family: var(--font-family-primary);\\n  overflow-y: auto;\\n  transition: var(--transition-fast);\\n  color: var(--color-gray-900);\\n  font-weight: var(--font-weight-normal);\\n  \\n\\n}\\n.message-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--color-gray-500);\\n  opacity: 1;\\n  font-weight: var(--font-weight-normal);\\n}\\n.message-input[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e1;\\n  border-radius: var(--radius-sm);\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #94a3b8;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--color-white);\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: all var(--transition-normal);\\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.send-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);\\n  border-radius: 50%;\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)::before {\\n  opacity: 1;\\n}\\n.send-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(-1px) scale(1.02);\\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled {\\n  background: var(--color-gray-400);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled::before {\\n  display: none;\\n}\\n.send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  transition: var(--transition-fast);\\n  transform: translateX(1px); \\n\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)   svg[_ngcontent-%COMP%] {\\n  transform: translateX(1px) scale(1.1);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-1);\\n  padding: var(--space-1) 0;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  background: #94a3b8;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n\\n\\n.auth-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  color: var(--color-gray-600);\\n  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 50%, #f0f4f8 100%);\\n}\\n.auth-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border: 4px solid var(--color-gray-200);\\n  border-top: 4px solid var(--color-accent);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: var(--space-4);\\n}\\n.auth-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-lg);\\n  color: var(--color-gray-600);\\n  margin: 0;\\n  font-weight: var(--font-weight-medium);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    opacity: 0.3;\\n    transform: scale(0.7) translateY(0);\\n  }\\n  30% {\\n    opacity: 1;\\n    transform: scale(1.3) translateY(-6px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(40px) scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .chat-container[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    height: 100vh;\\n    border-radius: 0;\\n    border: none;\\n  }\\n  .messages-container[_ngcontent-%COMP%] {\\n    padding: var(--space-5) var(--space-5) 140px var(--space-5);\\n  }\\n  .input-container[_ngcontent-%COMP%] {\\n    padding: var(--space-5) var(--space-5) var(--space-6) var(--space-5);\\n    max-width: 100%;\\n  }\\n  .input-container.centered[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    padding: var(--space-5);\\n    margin: 0 var(--space-4);\\n    width: calc(100% - var(--space-8));\\n  }\\n  .message[_ngcontent-%COMP%] {\\n    margin-bottom: var(--space-5);\\n  }\\n  .message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%], .message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n    max-width: 88%;\\n  }\\n  .input-wrapper[_ngcontent-%COMP%] {\\n    padding: var(--space-1) var(--space-2) var(--space-1) var(--space-4);\\n    gap: var(--space-2);\\n  }\\n  .send-button[_ngcontent-%COMP%] {\\n    width: 42px;\\n    height: 42px;\\n  }\\n  .send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n\\n\\n*[_ngcontent-%COMP%] {\\n  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast), transform var(--transition-fast), box-shadow var(--transition-fast);\\n}\\n\\n\\n\\n.send-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--color-primary);\\n  outline-offset: 3px;\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%]::selection {\\n  background: rgba(16, 185, 129, 0.2);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]::selection {\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n  30% {\\n    transform: translateY(-10px);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["marked", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "message_r8", "news_articles", "ɵɵtext", "ɵɵtemplate", "ChatComponent_div_1_div_1_div_3_div_6_Template", "ɵɵpureFunction2", "_c1", "isUser", "ctx_r6", "formatMessageText", "text", "ɵɵsanitizeHtml", "ɵɵtextInterpolate", "ɵɵpipeBind2", "timestamp", "length", "ɵɵlistener", "ChatComponent_div_1_div_1_Template_div_scroll_0_listener", "$event", "ɵɵrestoreView", "_r12", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "onScroll", "ChatComponent_div_1_div_1_div_2_Template", "ChatComponent_div_1_div_1_div_3_Template", "ChatComponent_div_1_div_1_div_4_Template", "ctx_r2", "isLoadingHistory", "messages", "isLoading", "ChatComponent_div_1_div_1_Template", "ChatComponent_div_1_Template_textarea_ngModelChange_4_listener", "_r14", "ctx_r13", "currentMessage", "ChatComponent_div_1_Template_textarea_keydown_4_listener", "ctx_r15", "onKeyPress", "ChatComponent_div_1_Template_textarea_input_4_listener", "ctx_r16", "adjustTextareaHeight", "ChatComponent_div_1_Template_button_click_7_listener", "ctx_r17", "sendMessage", "ɵɵnamespaceSVG", "ctx_r0", "ɵɵclassProp", "trim", "ChatComponent", "constructor", "authService", "isAuthenticated", "currentSessionId", "currentPage", "hasNextPage", "shouldScrollToBottom", "lastScrollHeight", "initialLoadComplete", "userHasScrolled", "ngOnInit", "ensureAuthenticated", "subscribe", "next", "token", "console", "log", "loadChatHistory", "error", "ngAfterViewChecked", "scrollToBottom", "event", "element", "target", "scrollTop", "scrollHeight", "clientHeight", "scrollTimeout", "clearTimeout", "setTimeout", "isNearTop", "isNotAtBottom", "loadMoreHistory", "page", "append", "undefined", "response", "newMessages", "results", "map", "msg", "convertChatMessageToMessage", "reversedNewMessages", "reverse", "maintainScrollPosition", "chatMessage", "id", "message", "sender", "Date", "session", "prompt", "model", "messagesContainer", "nativeElement", "newScrollHeight", "scrollDifference", "messageToSend", "tempUserMessage", "push", "sendMessageToChatbot", "userMessage", "user_message", "botMessage", "bot_message", "lastMessageIndex", "errorMessage", "clearHistory", "refreshHistory", "key", "shift<PERSON>ey", "preventDefault", "textarea", "style", "height", "Math", "min", "setOptions", "breaks", "gfm", "htmlContent", "parse", "sanitizedHtml", "replace", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "viewQuery", "ChatComponent_Query", "rf", "ctx", "ChatComponent_div_1_Template", "ChatComponent_div_2_Template"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\chat\\chat.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\chat\\chat.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ViewChild, AfterViewChecked } from '@angular/core';\nimport { AuthService } from '../auth.service'; // Adjust path as needed\nimport { marked } from 'marked';\n\ninterface NewsArticle {\n  id?: number;\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string;\n  description: string;\n  fetched_at?: string;\n  created_at?: string;\n}\n\ninterface Message {\n  id?: number;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n  session?: number;\n  prompt?: number;\n  model?: number;\n  news_articles?: NewsArticle[]; // for bot messages that include news\n}\n\n// Django ChatMessage structure\ninterface ChatMessage {\n  id: number;\n  session: number;\n  sender: 'user' | 'bot';\n  message: string;\n  timestamp: string;\n  prompt: number | null;\n  model: number | null;\n  news_articles?: NewsArticle[] | null;\n}\n\n// Django paginated response\ninterface ChatHistoryResponse {\n  count: number;\n  next: string | null;\n  previous: string | null;\n  results: ChatMessage[];\n}\n\n// Django chatbot response\ninterface ChatbotResponse {\n  user_message: ChatMessage;\n  bot_message: ChatMessage;\n}\n\n@Component({\n  selector: 'app-chat',\n  templateUrl: './chat.component.html',\n  styleUrls: ['./chat.component.scss']\n})\nexport class ChatComponent implements OnInit, OnDestroy, AfterViewChecked {\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  \n  messages: Message[] = [];\n  currentMessage: string = '';\n  isLoading: boolean = false;\n  isAuthenticated: boolean = false;\n  currentSessionId: number | null = null;\n  \n  // Pagination\n  currentPage: number = 1;\n  hasNextPage: boolean = false;\n  isLoadingHistory: boolean = false;\n  \n  // Scroll management\n  private shouldScrollToBottom: boolean = true;\n  private lastScrollHeight: number = 0;\n  private initialLoadComplete: boolean = false;\n  private userHasScrolled: boolean = false;\n  private scrollTimeout: any;\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: (token) => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory(1, false);\n      },\n      error: (error) => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n\n  // Listen for scroll events to load more history\n  onScroll(event: any) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    const clientHeight = element.clientHeight;\n    \n    // Mark that user has scrolled manually (not programmatic)\n    if (this.initialLoadComplete) {\n      this.userHasScrolled = true;\n    }\n    \n    // Clear existing timeout\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    \n    // Debounce scroll events and check conditions\n    this.scrollTimeout = setTimeout(() => {\n      // Only load more if:\n      // 1. Initial load is complete\n      // 2. User has scrolled manually at least once\n      // 3. User is near the top (scrollTop < 100)\n      // 4. There are more pages to load\n      // 5. Not currently loading\n      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n      const isNearTop = scrollTop < 100;\n      const isNotAtBottom = scrollTop < (scrollHeight - clientHeight - 50);\n      \n      if (isNearTop && \n          isNotAtBottom &&\n          this.hasNextPage && \n          !this.isLoadingHistory && \n          this.initialLoadComplete &&\n          this.userHasScrolled) {\n        this.lastScrollHeight = scrollHeight;\n        this.loadMoreHistory();\n      }\n    }, 100); // 100ms debounce\n  }\n\n  loadChatHistory(page: number = 1, append: boolean = false) {\n    if (!this.isAuthenticated) return;\n\n    this.isLoadingHistory = true;\n\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatHistoryResponse) => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        \n        if (append) {\n          // For pagination - reverse the new messages (since API returns newest first)\n          // and prepend older messages to beginning\n          const reversedNewMessages = [...newMessages].reverse();\n          this.messages = [...reversedNewMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - reverse messages to get chronological order (oldest first)\n          this.messages = [...newMessages].reverse();\n          this.shouldScrollToBottom = true;\n          \n          // Set initial load complete after scroll positioning is done\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n        \n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        \n        this.isLoadingHistory = false;\n      },\n      error: (error) => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        \n        // If this was the initial load, still mark it as complete after delay\n        if (!append) {\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n      }\n    });\n  }\n\n  // Convert Django ChatMessage to frontend Message format\n  private convertChatMessageToMessage(chatMessage: ChatMessage): Message {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined,\n      news_articles: chatMessage.news_articles || undefined,\n    };\n  }\n\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n\n  // Maintain scroll position when loading older messages\n  private maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n\n    // Create temporary user message and add it instantly\n    const tempUserMessage: Message = {\n      text: messageToSend,\n      isUser: true,\n      timestamp: new Date()\n    };\n\n    // Add user message instantly to the chat\n    this.messages.push(tempUserMessage);\n    this.shouldScrollToBottom = true;\n\n    // Set loading state\n    this.isLoading = true;\n\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatbotResponse) => {\n        // Convert backend messages\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n\n        // Replace the temporary user message with the one from backend\n        const lastMessageIndex = this.messages.length - 1;\n        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n          this.messages[lastMessageIndex] = userMessage;\n        }\n\n        // Add bot message\n        this.messages.push(botMessage);\n\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        const errorMessage: Message = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n\n  clearHistory() {\n    this.messages = [];\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n\n  refreshHistory() {\n    this.currentPage = 1;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.loadChatHistory();\n  }\n\n  onKeyPress(event: KeyboardEvent) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  adjustTextareaHeight(event: any) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n\n  private scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n\n  // Format message text using marked library for markdown\n  formatMessageText(text: string): string {\n    if (!text) return '';\n\n    try {\n      // Configure marked to be more restrictive for security\n      marked.setOptions({\n        breaks: true, // Convert line breaks to <br>\n        gfm: true // Enable GitHub Flavored Markdown\n      });\n\n      // Convert markdown to HTML using marked (synchronous)\n      const htmlContent = marked.parse(text) as string;\n\n      // Basic sanitization - remove script tags and dangerous attributes\n      let sanitizedHtml = htmlContent\n        .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n        .replace(/javascript:/gi, '')\n        .replace(/on\\w+\\s*=/gi, '');\n\n      return sanitizedHtml;\n    } catch (error) {\n      console.error('Error formatting message text:', error);\n      // Fallback to plain text with basic line break conversion\n      return text.replace(/\\n/g, '<br>');\n    }\n  }\n\n  ngOnDestroy() {\n    // Clean up any pending timeouts\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n}", "<!-- chat.component.html -->\n\n<!-- Anonymous user component runs in background -->\n<app-anonymous-user></app-anonymous-user>\n\n<div class=\"chat-container\" *ngIf=\"isAuthenticated\">\n  <!-- Messages container - only show when there are messages -->\n  <div \n    #messagesContainer\n    class=\"messages-container\" \n    (scroll)=\"onScroll($event)\"\n    *ngIf=\"messages.length > 0\">\n    \n    <!-- Loading indicator for pagination at the top -->\n    <div class=\"pagination-loading\" *ngIf=\"isLoadingHistory\">\n      <div class=\"loading-spinner\"></div>\n    </div>\n    \n    <div class=\"message\"\n         *ngFor=\"let message of messages\"\n         [ngClass]=\"{'user-message': message.isUser, 'bot-message': !message.isUser}\">\n      <div class=\"message-content\">\n        <div class=\"message-text\" [innerHTML]=\"formatMessageText(message.text)\"></div>\n        <div class=\"message-time\">{{ message.timestamp | date:'short' }}</div>\n      </div>\n\n      <!-- Render news articles UNDER the bot message bubble when present -->\n      <div class=\"message-news\"\n           *ngIf=\"!message.isUser && message.news_articles && message.news_articles.length\">\n        <app-news\n          [news]=\"message.news_articles\"\n          [showTitle]=\"false\"\n          [compact]=\"false\"></app-news>\n      </div>\n    </div>\n\n    <!-- Loading indicator for current message -->\n    <div class=\"message bot-message\" *ngIf=\"isLoading\">\n      <div class=\"message-content\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input container - always visible -->\n  <div class=\"input-container\" [class.centered]=\"messages.length === 0\" [class.bottom]=\"messages.length > 0\">\n    <div class=\"input-wrapper\">\n      <textarea\n        #messageTextarea\n        [(ngModel)]=\"currentMessage\"\n        (keydown)=\"onKeyPress($event)\"\n        (input)=\"adjustTextareaHeight($event)\"\n        placeholder=\"Ask anything\"\n        class=\"message-input\"\n        rows=\"1\">\n      </textarea>\n      <button\n        (click)=\"sendMessage()\"\n        class=\"send-button\"\n        [disabled]=\"!currentMessage.trim() || isLoading\">\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"currentColor\"/>\n        </svg>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Show loading message while authenticating -->\n<div *ngIf=\"!isAuthenticated\" class=\"auth-loading\">\n  <div class=\"loading-spinner\"></div>\n  <p>Initializing chat...</p>\n</div>"], "mappings": "AAEA,SAASA,MAAM,QAAQ,QAAQ;;;;;;;;;;ICY3BC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,cAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWJH,EAAA,CAAAC,cAAA,cACsF;IACpFD,EAAA,CAAAE,SAAA,mBAG+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;;;;IAHFH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,UAAA,SAAAC,UAAA,CAAAC,aAAA,CAA8B;;;;;;;;;;;IAZpCP,EAAA,CAAAC,cAAA,cAEkF;IAE9ED,EAAA,CAAAE,SAAA,cAA8E;IAC9EF,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAQ,MAAA,GAAsC;;IAAAR,EAAA,CAAAG,YAAA,EAAM;IAIxEH,EAAA,CAAAS,UAAA,IAAAC,8CAAA,kBAMM;IACRV,EAAA,CAAAG,YAAA,EAAM;;;;;IAdDH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,IAAAC,GAAA,EAAAN,UAAA,CAAAO,MAAA,GAAAP,UAAA,CAAAO,MAAA,EAA4E;IAEnDb,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,UAAA,cAAAS,MAAA,CAAAC,iBAAA,CAAAT,UAAA,CAAAU,IAAA,GAAAhB,EAAA,CAAAiB,cAAA,CAA6C;IAC7CjB,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAmB,WAAA,OAAAb,UAAA,CAAAc,SAAA,WAAsC;IAK5DpB,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAK,UAAA,UAAAC,UAAA,CAAAO,MAAA,IAAAP,UAAA,CAAAC,aAAA,IAAAD,UAAA,CAAAC,aAAA,CAAAc,MAAA,CAA8E;;;;;IAStFrB,EAAA,CAAAC,cAAA,cAAmD;IAG7CD,EAAA,CAAAE,SAAA,WAAa;IAGfF,EAAA,CAAAG,YAAA,EAAM;;;;;;IApCZH,EAAA,CAAAC,cAAA,kBAI8B;IAD5BD,EAAA,CAAAsB,UAAA,oBAAAC,yDAAAC,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAU5B,EAAA,CAAA6B,WAAA,CAAAF,OAAA,CAAAG,QAAA,CAAAN,MAAA,CAAgB;IAAA,EAAC;IAI3BxB,EAAA,CAAAS,UAAA,IAAAsB,wCAAA,kBAEM;IAEN/B,EAAA,CAAAS,UAAA,IAAAuB,wCAAA,mBAgBM;IAGNhC,EAAA,CAAAS,UAAA,IAAAwB,wCAAA,kBAQM;IACRjC,EAAA,CAAAG,YAAA,EAAM;;;;IAhC6BH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,UAAA,SAAA6B,MAAA,CAAAC,gBAAA,CAAsB;IAK9BnC,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,UAAA,YAAA6B,MAAA,CAAAE,QAAA,CAAW;IAkBFpC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,UAAA,SAAA6B,MAAA,CAAAG,SAAA,CAAe;;;;;;IAhCrDrC,EAAA,CAAAC,cAAA,aAAoD;IAElDD,EAAA,CAAAS,UAAA,IAAA6B,kCAAA,iBAuCM;IAGNtC,EAAA,CAAAC,cAAA,aAA2G;IAIrGD,EAAA,CAAAsB,UAAA,2BAAAiB,+DAAAf,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAAY,OAAA,CAAAC,cAAA,GAAAlB,MAAA;IAAA,EAA4B,qBAAAmB,yDAAAnB,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAI,OAAA,GAAA5C,EAAA,CAAA4B,aAAA;MAAA,OACjB5B,EAAA,CAAA6B,WAAA,CAAAe,OAAA,CAAAC,UAAA,CAAArB,MAAA,CAAkB;IAAA,EADD,mBAAAsB,uDAAAtB,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAO,OAAA,GAAA/C,EAAA,CAAA4B,aAAA;MAAA,OAEnB5B,EAAA,CAAA6B,WAAA,CAAAkB,OAAA,CAAAC,oBAAA,CAAAxB,MAAA,CAA4B;IAAA,EAFT;IAM9BxB,EAAA,CAAAQ,MAAA;IAAAR,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,gBAGmD;IAFjDD,EAAA,CAAAsB,UAAA,mBAAA2B,qDAAA;MAAAjD,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAU,OAAA,GAAAlD,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAqB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvBnD,EAAA,CAAAoD,cAAA,EAA+F;IAA/FpD,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAE,SAAA,eAAiE;IACnEF,EAAA,CAAAG,YAAA,EAAM;;;;IAvDTH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,UAAA,SAAAgD,MAAA,CAAAjB,QAAA,CAAAf,MAAA,KAAyB;IAsCCrB,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAsD,WAAA,aAAAD,MAAA,CAAAjB,QAAA,CAAAf,MAAA,OAAwC,WAAAgC,MAAA,CAAAjB,QAAA,CAAAf,MAAA;IAI/DrB,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,UAAA,YAAAgD,MAAA,CAAAX,cAAA,CAA4B;IAU5B1C,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,UAAA,cAAAgD,MAAA,CAAAX,cAAA,CAAAa,IAAA,MAAAF,MAAA,CAAAhB,SAAA,CAAgD;;;;;IAUxDrC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAQ,MAAA,2BAAoB;IAAAR,EAAA,CAAAG,YAAA,EAAI;;;ADjB7B,OAAM,MAAOqD,aAAa;EAqBxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAlB/B,KAAAtB,QAAQ,GAAc,EAAE;IACxB,KAAAM,cAAc,GAAW,EAAE;IAC3B,KAAAL,SAAS,GAAY,KAAK;IAC1B,KAAAsB,eAAe,GAAY,KAAK;IAChC,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAA3B,gBAAgB,GAAY,KAAK;IAEjC;IACQ,KAAA4B,oBAAoB,GAAY,IAAI;IACpC,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,eAAe,GAAY,KAAK;EAGO;EAE/CC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,WAAW,CAACU,mBAAmB,EAAE,CAACC,SAAS,CAAC;MAC/CC,IAAI,EAAGC,KAAK,IAAI;QACdC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,IAAI,CAACd,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACe,eAAe,CAAC,CAAC,EAAE,KAAK,CAAC;MAChC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChB,eAAe,GAAG,KAAK;MAC9B;KACD,CAAC;EACJ;EAEAiB,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACb,oBAAoB,EAAE;MAC7B,IAAI,CAACc,cAAc,EAAE;MACrB,IAAI,CAACd,oBAAoB,GAAG,KAAK;;EAErC;EAEA;EACAjC,QAAQA,CAACgD,KAAU;IACjB,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM;IAC5B,MAAMC,SAAS,GAAGF,OAAO,CAACE,SAAS;IACnC,MAAMC,YAAY,GAAGH,OAAO,CAACG,YAAY;IACzC,MAAMC,YAAY,GAAGJ,OAAO,CAACI,YAAY;IAEzC;IACA,IAAI,IAAI,CAAClB,mBAAmB,EAAE;MAC5B,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B;IACA,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC;IACA,IAAI,CAACA,aAAa,GAAGE,UAAU,CAAC,MAAK;MACnC;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMC,SAAS,GAAGN,SAAS,GAAG,GAAG;MACjC,MAAMO,aAAa,GAAGP,SAAS,GAAIC,YAAY,GAAGC,YAAY,GAAG,EAAG;MAEpE,IAAII,SAAS,IACTC,aAAa,IACb,IAAI,CAAC1B,WAAW,IAChB,CAAC,IAAI,CAAC3B,gBAAgB,IACtB,IAAI,CAAC8B,mBAAmB,IACxB,IAAI,CAACC,eAAe,EAAE;QACxB,IAAI,CAACF,gBAAgB,GAAGkB,YAAY;QACpC,IAAI,CAACO,eAAe,EAAE;;IAE1B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;;EAEAf,eAAeA,CAACgB,IAAA,GAAe,CAAC,EAAEC,MAAA,GAAkB,KAAK;IACvD,IAAI,CAAC,IAAI,CAAChC,eAAe,EAAE;IAE3B,IAAI,CAACxB,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAACuB,WAAW,CAACgB,eAAe,CAACgB,IAAI,EAAE,IAAI,CAAC9B,gBAAgB,IAAIgC,SAAS,CAAC,CAACvB,SAAS,CAAC;MACnFC,IAAI,EAAGuB,QAA6B,IAAI;QACtC,MAAMC,WAAW,GAAGD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,GAAG,IAAI,IAAI,CAACC,2BAA2B,CAACD,GAAG,CAAC,CAAC;QAEtF,IAAIN,MAAM,EAAE;UACV;UACA;UACA,MAAMQ,mBAAmB,GAAG,CAAC,GAAGL,WAAW,CAAC,CAACM,OAAO,EAAE;UACtD,IAAI,CAAChE,QAAQ,GAAG,CAAC,GAAG+D,mBAAmB,EAAE,GAAG,IAAI,CAAC/D,QAAQ,CAAC;UAC1D,IAAI,CAACiE,sBAAsB,EAAE;SAC9B,MAAM;UACL;UACA,IAAI,CAACjE,QAAQ,GAAG,CAAC,GAAG0D,WAAW,CAAC,CAACM,OAAO,EAAE;UAC1C,IAAI,CAACrC,oBAAoB,GAAG,IAAI;UAEhC;UACAuB,UAAU,CAAC,MAAK;YACd,IAAI,CAACrB,mBAAmB,GAAG,IAAI;UACjC,CAAC,EAAE,GAAG,CAAC;;QAGT;QACA,IAAI,CAACJ,WAAW,GAAG6B,IAAI;QACvB,IAAI,CAAC5B,WAAW,GAAG,CAAC,CAAC+B,QAAQ,CAACvB,IAAI;QAElC,IAAI,CAACnC,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACxC,gBAAgB,GAAG,KAAK;QAE7B;QACA,IAAI,CAACwD,MAAM,EAAE;UACXL,UAAU,CAAC,MAAK;YACd,IAAI,CAACrB,mBAAmB,GAAG,IAAI;UACjC,CAAC,EAAE,GAAG,CAAC;;MAEX;KACD,CAAC;EACJ;EAEA;EACQiC,2BAA2BA,CAACI,WAAwB;IAC1D,OAAO;MACLC,EAAE,EAAED,WAAW,CAACC,EAAE;MAClBvF,IAAI,EAAEsF,WAAW,CAACE,OAAO;MACzB3F,MAAM,EAAEyF,WAAW,CAACG,MAAM,KAAK,MAAM;MACrCrF,SAAS,EAAE,IAAIsF,IAAI,CAACJ,WAAW,CAAClF,SAAS,CAAC;MAC1CuF,OAAO,EAAEL,WAAW,CAACK,OAAO;MAC5BC,MAAM,EAAEN,WAAW,CAACM,MAAM,IAAIhB,SAAS;MACvCiB,KAAK,EAAEP,WAAW,CAACO,KAAK,IAAIjB,SAAS;MACrCrF,aAAa,EAAE+F,WAAW,CAAC/F,aAAa,IAAIqF;KAC7C;EACH;EAEA;EACAH,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3B,WAAW,IAAI,CAAC,IAAI,CAAC3B,gBAAgB,EAAE;MAC9C,IAAI,CAACuC,eAAe,CAAC,IAAI,CAACb,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;;EAEpD;EAEA;EACQwC,sBAAsBA,CAAA;IAC5Bf,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACwB,iBAAiB,EAAE;QAC1B,MAAM/B,OAAO,GAAG,IAAI,CAAC+B,iBAAiB,CAACC,aAAa;QACpD,MAAMC,eAAe,GAAGjC,OAAO,CAACG,YAAY;QAC5C,MAAM+B,gBAAgB,GAAGD,eAAe,GAAG,IAAI,CAAChD,gBAAgB;QAChEe,OAAO,CAACE,SAAS,GAAGgC,gBAAgB;;IAExC,CAAC,EAAE,EAAE,CAAC;EACR;EAEA9D,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACT,cAAc,CAACa,IAAI,EAAE,IAAI,IAAI,CAAClB,SAAS,IAAI,CAAC,IAAI,CAACsB,eAAe,EAAE;MAC1E;;IAGF;IACA,MAAMuD,aAAa,GAAG,IAAI,CAACxE,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IAExB;IACA,MAAMyE,eAAe,GAAY;MAC/BnG,IAAI,EAAEkG,aAAa;MACnBrG,MAAM,EAAE,IAAI;MACZO,SAAS,EAAE,IAAIsF,IAAI;KACpB;IAED;IACA,IAAI,CAACtE,QAAQ,CAACgF,IAAI,CAACD,eAAe,CAAC;IACnC,IAAI,CAACpD,oBAAoB,GAAG,IAAI;IAEhC;IACA,IAAI,CAAC1B,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACqB,WAAW,CAAC2D,oBAAoB,CAACH,aAAa,EAAE,IAAI,CAACtD,gBAAgB,IAAIgC,SAAS,CAAC,CAACvB,SAAS,CAAC;MACjGC,IAAI,EAAGuB,QAAyB,IAAI;QAClC;QACA,MAAMyB,WAAW,GAAG,IAAI,CAACpB,2BAA2B,CAACL,QAAQ,CAAC0B,YAAY,CAAC;QAC3E,MAAMC,UAAU,GAAG,IAAI,CAACtB,2BAA2B,CAACL,QAAQ,CAAC4B,WAAW,CAAC;QAEzE;QACA,MAAMC,gBAAgB,GAAG,IAAI,CAACtF,QAAQ,CAACf,MAAM,GAAG,CAAC;QACjD,IAAIqG,gBAAgB,IAAI,CAAC,IAAI,IAAI,CAACtF,QAAQ,CAACsF,gBAAgB,CAAC,CAAC7G,MAAM,EAAE;UACnE,IAAI,CAACuB,QAAQ,CAACsF,gBAAgB,CAAC,GAAGJ,WAAW;;QAG/C;QACA,IAAI,CAAClF,QAAQ,CAACgF,IAAI,CAACI,UAAU,CAAC;QAE9B;QACA,IAAI,CAAC,IAAI,CAAC5D,gBAAgB,EAAE;UAC1B,IAAI,CAACA,gBAAgB,GAAGiC,QAAQ,CAAC0B,YAAY,CAACZ,OAAO;;QAGvD,IAAI,CAACtE,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC0B,oBAAoB,GAAG,IAAI;MAClC,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMgD,YAAY,GAAY;UAC5B3G,IAAI,EAAE,sEAAsE;UAC5EH,MAAM,EAAE,KAAK;UACbO,SAAS,EAAE,IAAIsF,IAAI;SACpB;QACD,IAAI,CAACtE,QAAQ,CAACgF,IAAI,CAACO,YAAY,CAAC;QAChC,IAAI,CAACtF,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC0B,oBAAoB,GAAG,IAAI;MAClC;KACD,CAAC;EACJ;EAEA6D,YAAYA,CAAA;IACV,IAAI,CAACxF,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACyB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEAyC,cAAcA,CAAA;IACZ,IAAI,CAAChE,WAAW,GAAG,CAAC;IACpB,IAAI,CAACI,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAElC,IAAI,CAACV,eAAe,EAAE;EACxB;EAEA7B,UAAUA,CAACiC,KAAoB;IAC7B,IAAIA,KAAK,CAACgD,GAAG,KAAK,OAAO,IAAI,CAAChD,KAAK,CAACiD,QAAQ,EAAE;MAC5CjD,KAAK,CAACkD,cAAc,EAAE;MACtB,IAAI,CAAC7E,WAAW,EAAE;;EAEtB;EAEAH,oBAAoBA,CAAC8B,KAAU;IAC7B,MAAMmD,QAAQ,GAAGnD,KAAK,CAACE,MAAM;IAC7BiD,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;IAC9BF,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC/C,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;EACrE;EAEQL,cAAcA,CAAA;IACpBS,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACwB,iBAAiB,EAAE;QAC1B,MAAM/B,OAAO,GAAG,IAAI,CAAC+B,iBAAiB,CAACC,aAAa;QACpDhC,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,EAAE,CAAC;EACR;EAEA;EACAnE,iBAAiBA,CAACC,IAAY;IAC5B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,IAAI;MACF;MACAjB,MAAM,CAACuI,UAAU,CAAC;QAChBC,MAAM,EAAE,IAAI;QACZC,GAAG,EAAE,IAAI,CAAC;OACX,CAAC;MAEF;MACA,MAAMC,WAAW,GAAG1I,MAAM,CAAC2I,KAAK,CAAC1H,IAAI,CAAW;MAEhD;MACA,IAAI2H,aAAa,GAAGF,WAAW,CAC5BG,OAAO,CAAC,qDAAqD,EAAE,EAAE,CAAC,CAClEA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;MAE7B,OAAOD,aAAa;KACrB,CAAC,OAAOhE,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACA,OAAO3D,IAAI,CAAC4H,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;;EAEtC;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACzD,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAAC,QAAA0D,CAAA,G;qBA/SUtF,aAAa,EAAAxD,EAAA,CAAA+I,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAb1F,aAAa;IAAA2F,SAAA;IAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCvD1BtJ,EAAA,CAAAE,SAAA,yBAAyC;QAEzCF,EAAA,CAAAS,UAAA,IAAA+I,4BAAA,kBAiEM;QAGNxJ,EAAA,CAAAS,UAAA,IAAAgJ,4BAAA,iBAGM;;;QAvEuBzJ,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAAK,UAAA,SAAAkJ,GAAA,CAAA5F,eAAA,CAAqB;QAoE5C3D,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAK,UAAA,UAAAkJ,GAAA,CAAA5F,eAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}