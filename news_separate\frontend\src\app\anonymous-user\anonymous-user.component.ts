import { Component, OnInit } from '@angular/core';
import { AuthService } from '../auth.service'; // Adjust path as needed

@Component({
  selector: 'app-anonymous-user',
  templateUrl: './anonymous-user.component.html',
  styleUrls: ['./anonymous-user.component.scss']
})
export class AnonymousUserComponent implements OnInit {

  constructor(private authService: AuthService) { }

  ngOnInit(): void {
    this.initializeUser();
  }

  private initializeUser(): void {
    this.authService.ensureAuthenticated().subscribe(
      token => {
        console.log('User authenticated successfully');
      },
      error => {
        console.error('Failed to authenticate user:', error);
      }
    );
  }
}