{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./chat/chat.component\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'News Agent';\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 5,\n    vars: 1,\n    consts: [[1, \"app-container\"], [1, \"main-content\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"main\", 1)(2, \"h1\");\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(4, \"app-chat\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.title);\n      }\n    },\n    dependencies: [i1.ChatComponent],\n    styles: [\"\\n\\n.app-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  background: transparent;\\n  overflow: hidden;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  background-color: transparent;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.main-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: var(--gradient-primary);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  margin: 0;\\n  padding: var(--space-8) 0;\\n  font-size: var(--font-size-3xl);\\n  font-weight: var(--font-weight-bold);\\n  flex-shrink: 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .app-container[_ngcontent-%COMP%] {\\n    height: 100vh;\\n  }\\n}\\n\\n\\n.sr-only[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border: 0;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.text-left[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n\\n.text-right[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.mb-0[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.mb-2[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-2);\\n}\\n\\n.mb-4[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n}\\n\\n.mb-6[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-6);\\n}\\n\\n.mt-0[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n}\\n\\n.mt-2[_ngcontent-%COMP%] {\\n  margin-top: var(--space-2);\\n}\\n\\n.mt-4[_ngcontent-%COMP%] {\\n  margin-top: var(--space-4);\\n}\\n\\n.mt-6[_ngcontent-%COMP%] {\\n  margin-top: var(--space-6);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "_", "_2", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = 'News Agent';\n}\n", "<div class=\"app-container\">\n  <main class=\"main-content\">\n    <h1>{{ title }}</h1>\n    <app-chat></app-chat>\n     <!-- <app-news></app-news> -->\n  </main>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,YAAY;;EACrB,QAAAC,CAAA,G;qBAFYH,YAAY;EAAA;EAAA,QAAAI,EAAA,G;UAAZJ,YAAY;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPzBE,EAAA,CAAAC,cAAA,aAA2B;QAEnBD,EAAA,CAAAE,MAAA,GAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpBH,EAAA,CAAAI,SAAA,eAAqB;QAEvBJ,EAAA,CAAAG,YAAA,EAAO;;;QAHDH,EAAA,CAAAK,SAAA,GAAW;QAAXL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAAV,KAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}