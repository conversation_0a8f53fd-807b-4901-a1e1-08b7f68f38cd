{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { ChatComponent } from './chat/chat.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AnonymousUserComponent } from './anonymous-user/anonymous-user.component';\nimport { NewsComponent } from './news/news.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static #_ = this.ɵfac = function AppModule_Factory(t) {\n    return new (t || AppModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppModule,\n    bootstrap: [AppComponent]\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [BrowserModule, AppRoutingModule, FormsModule, HttpClientModule, ReactiveFormsModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, ChatComponent, AnonymousUserComponent, NewsComponent],\n    imports: [BrowserModule, AppRoutingModule, FormsModule, HttpClientModule, ReactiveFormsModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "AppRoutingModule", "AppComponent", "ChatComponent", "FormsModule", "ReactiveFormsModule", "HttpClientModule", "AnonymousUserComponent", "NewsComponent", "AppModule", "_", "_2", "bootstrap", "_3", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { ChatComponent } from './chat/chat.component';\n\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AnonymousUserComponent } from './anonymous-user/anonymous-user.component';\nimport { NewsComponent } from './news/news.component';\n\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    ChatComponent,\n    AnonymousUserComponent,\n    NewsComponent,\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    HttpClientModule,\n    ReactiveFormsModule,\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AAEzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,uBAAuB;AAErD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,aAAa,QAAQ,uBAAuB;;AAoBrD,OAAM,MAAOC,SAAS;EAAA,QAAAC,CAAA,G;qBAATD,SAAS;EAAA;EAAA,QAAAE,EAAA,G;UAATF,SAAS;IAAAG,SAAA,GAFRV,YAAY;EAAA;EAAA,QAAAW,EAAA,G;cAPtBb,aAAa,EACbC,gBAAgB,EAChBG,WAAW,EACXE,gBAAgB,EAChBD,mBAAmB;EAAA;;;2EAKVI,SAAS;IAAAK,YAAA,GAflBZ,YAAY,EACZC,aAAa,EACbI,sBAAsB,EACtBC,aAAa;IAAAO,OAAA,GAGbf,aAAa,EACbC,gBAAgB,EAChBG,WAAW,EACXE,gBAAgB,EAChBD,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}