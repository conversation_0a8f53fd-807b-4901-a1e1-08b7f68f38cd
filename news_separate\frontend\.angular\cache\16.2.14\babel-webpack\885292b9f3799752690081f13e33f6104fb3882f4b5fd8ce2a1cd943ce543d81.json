{"ast": null, "code": "import { AnimationFrameAction } from './AnimationFrameAction';\nimport { AnimationFrameScheduler } from './AnimationFrameScheduler';\nexport const animationFrameScheduler = new AnimationFrameScheduler(AnimationFrameAction);\nexport const animationFrame = animationFrameScheduler;", "map": {"version": 3, "names": ["AnimationFrameAction", "AnimationFrameScheduler", "animationFrameScheduler", "animationFrame"], "sources": ["C:/Users/<USER>/PycharmProjects/GenAI/Borys-Slavic-News-Chatbot/news_separate/frontend/node_modules/rxjs/dist/esm/internal/scheduler/animationFrame.js"], "sourcesContent": ["import { AnimationFrameAction } from './AnimationFrameAction';\nimport { AnimationFrameScheduler } from './AnimationFrameScheduler';\nexport const animationFrameScheduler = new AnimationFrameScheduler(AnimationFrameAction);\nexport const animationFrame = animationFrameScheduler;\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,OAAO,MAAMC,uBAAuB,GAAG,IAAID,uBAAuB,CAACD,oBAAoB,CAAC;AACxF,OAAO,MAAMG,cAAc,GAAGD,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}