{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [\n// { path: '', redirectTo: '/login', pathMatch: 'full' },\n{\n  path: '',\n  redirectTo: '/news'\n}];\nexport class AppRoutingModule {\n  static #_ = this.ɵfac = function AppRoutingModule_Factory(t) {\n    return new (t || AppRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forRoot(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "routes", "path", "redirectTo", "AppRoutingModule", "_", "_2", "_3", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { Routes, RouterModule } from '@angular/router';\n\nconst routes: Routes = [\n  // { path: '', redirectTo: '/login', pathMatch: 'full' },\n  { path: '', redirectTo: '/news' },\n \n\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAAiBA,YAAY,QAAQ,iBAAiB;;;AAEtD,MAAMC,MAAM,GAAW;AACrB;AACA;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE;AAAO,CAAE,CAGlC;AAMD,OAAM,MAAOC,gBAAgB;EAAA,QAAAC,CAAA,G;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;cAHjBP,YAAY,CAACQ,OAAO,CAACP,MAAM,CAAC,EAC5BD,YAAY;EAAA;;;2EAEXI,gBAAgB;IAAAK,OAAA,GAAAC,EAAA,CAAAV,YAAA;IAAAW,OAAA,GAFjBX,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}