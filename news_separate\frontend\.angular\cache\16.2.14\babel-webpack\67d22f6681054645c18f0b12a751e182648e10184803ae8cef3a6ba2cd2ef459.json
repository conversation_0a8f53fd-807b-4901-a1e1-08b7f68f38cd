{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nexport class AnonymousUserComponent {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  ngOnInit() {\n    this.initializeUser();\n  }\n  initializeUser() {\n    this.authService.ensureAuthenticated().subscribe(token => {\n      console.log('User authenticated successfully');\n    }, error => {\n      console.error('Failed to authenticate user:', error);\n    });\n  }\n  static #_ = this.ɵfac = function AnonymousUserComponent_Factory(t) {\n    return new (t || AnonymousUserComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AnonymousUserComponent,\n    selectors: [[\"app-anonymous-user\"]],\n    decls: 0,\n    vars: 0,\n    template: function AnonymousUserComponent_Template(rf, ctx) {},\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["AnonymousUserComponent", "constructor", "authService", "ngOnInit", "initializeUser", "ensureAuthenticated", "subscribe", "token", "console", "log", "error", "_", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "decls", "vars", "template", "AnonymousUserComponent_Template", "rf", "ctx", "styles"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\anonymous-user\\anonymous-user.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../auth.service'; // Adjust path as needed\n\n@Component({\n  selector: 'app-anonymous-user',\n  templateUrl: './anonymous-user.component.html',\n  styleUrls: ['./anonymous-user.component.scss']\n})\nexport class AnonymousUserComponent implements OnInit {\n\n  constructor(private authService: AuthService) { }\n\n  ngOnInit(): void {\n    this.initializeUser();\n  }\n\n  private initializeUser(): void {\n    this.authService.ensureAuthenticated().subscribe(\n      token => {\n        console.log('User authenticated successfully');\n      },\n      error => {\n        console.error('Failed to authenticate user:', error);\n      }\n    );\n  }\n}"], "mappings": ";;AAQA,OAAM,MAAOA,sBAAsB;EAEjCC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAiB;EAEhDC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQA,cAAcA,CAAA;IACpB,IAAI,CAACF,WAAW,CAACG,mBAAmB,EAAE,CAACC,SAAS,CAC9CC,KAAK,IAAG;MACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAChD,CAAC,EACDC,KAAK,IAAG;MACNF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,CACF;EACH;EAAC,QAAAC,CAAA,G;qBAjBUX,sBAAsB,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBhB,sBAAsB;IAAAiB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}