{"ast": null, "code": "/**\n * marked v16.1.1 - a markdown parser\n * Copyright (c) 2011-2025, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\nfunction L() {\n  return {\n    async: !1,\n    breaks: !1,\n    extensions: null,\n    gfm: !0,\n    hooks: null,\n    pedantic: !1,\n    renderer: null,\n    silent: !1,\n    tokenizer: null,\n    walkTokens: null\n  };\n}\nvar O = L();\nfunction H(l) {\n  O = l;\n}\nvar E = {\n  exec: () => null\n};\nfunction h(l, e = \"\") {\n  let t = typeof l == \"string\" ? l : l.source,\n    n = {\n      replace: (r, i) => {\n        let s = typeof i == \"string\" ? i : i.source;\n        return s = s.replace(m.caret, \"$1\"), t = t.replace(r, s), n;\n      },\n      getRegex: () => new RegExp(t, e)\n    };\n  return n;\n}\nvar m = {\n    codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n    outputLinkReplace: /\\\\([\\[\\]])/g,\n    indentCodeCompensation: /^(\\s+)(?:```)/,\n    beginningSpace: /^\\s+/,\n    endingHash: /#$/,\n    startingSpaceChar: /^ /,\n    endingSpaceChar: / $/,\n    nonSpaceChar: /[^ ]/,\n    newLineCharGlobal: /\\n/g,\n    tabCharGlobal: /\\t/g,\n    multipleSpaceGlobal: /\\s+/g,\n    blankLine: /^[ \\t]*$/,\n    doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n    blockquoteStart: /^ {0,3}>/,\n    blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n    blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n    listReplaceTabs: /^\\t+/,\n    listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n    listIsTask: /^\\[[ xX]\\] /,\n    listReplaceTask: /^\\[[ xX]\\] +/,\n    anyLine: /\\n.*\\n/,\n    hrefBrackets: /^<(.*)>$/,\n    tableDelimiter: /[:|]/,\n    tableAlignChars: /^\\||\\| *$/g,\n    tableRowBlankLine: /\\n[ \\t]*$/,\n    tableAlignRight: /^ *-+: *$/,\n    tableAlignCenter: /^ *:-+: *$/,\n    tableAlignLeft: /^ *:-+ *$/,\n    startATag: /^<a /i,\n    endATag: /^<\\/a>/i,\n    startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n    endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n    startAngleBracket: /^</,\n    endAngleBracket: />$/,\n    pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n    unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n    escapeTest: /[&<>\"']/,\n    escapeReplace: /[&<>\"']/g,\n    escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n    escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n    unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n    caret: /(^|[^\\[])\\^/g,\n    percentDecode: /%25/g,\n    findPipe: /\\|/g,\n    splitPipe: / \\|/,\n    slashPipe: /\\\\\\|/g,\n    carriageReturn: /\\r\\n|\\r/g,\n    spaceLine: /^ +$/gm,\n    notSpaceStart: /^\\S*/,\n    endingNewline: /\\n$/,\n    listItemRegex: l => new RegExp(`^( {0,3}${l})((?:[\t ][^\\\\n]*)?(?:\\\\n|$))`),\n    nextBulletRegex: l => new RegExp(`^ {0,${Math.min(3, l - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \t][^\\\\n]*)?(?:\\\\n|$))`),\n    hrRegex: l => new RegExp(`^ {0,${Math.min(3, l - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n    fencesBeginRegex: l => new RegExp(`^ {0,${Math.min(3, l - 1)}}(?:\\`\\`\\`|~~~)`),\n    headingBeginRegex: l => new RegExp(`^ {0,${Math.min(3, l - 1)}}#`),\n    htmlBeginRegex: l => new RegExp(`^ {0,${Math.min(3, l - 1)}}<(?:[a-z].*>|!--)`, \"i\")\n  },\n  xe = /^(?:[ \\t]*(?:\\n|$))+/,\n  be = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/,\n  Re = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/,\n  C = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/,\n  Oe = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/,\n  j = /(?:[*+-]|\\d{1,9}[.)])/,\n  se = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  ie = h(se).replace(/bull/g, j).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/\\|table/g, \"\").getRegex(),\n  Te = h(se).replace(/bull/g, j).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/).getRegex(),\n  F = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/,\n  we = /^[^\\n]+/,\n  Q = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/,\n  ye = h(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/).replace(\"label\", Q).replace(\"title\", /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/).getRegex(),\n  Pe = h(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/).replace(/bull/g, j).getRegex(),\n  v = \"address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul\",\n  U = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/,\n  Se = h(\"^ {0,3}(?:<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)|comment[^\\\\n]*(\\\\n+|$)|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$))\", \"i\").replace(\"comment\", U).replace(\"tag\", v).replace(\"attribute\", / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/).getRegex(),\n  oe = h(F).replace(\"hr\", C).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", v).getRegex(),\n  $e = h(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/).replace(\"paragraph\", oe).getRegex(),\n  K = {\n    blockquote: $e,\n    code: be,\n    def: ye,\n    fences: Re,\n    heading: Oe,\n    hr: C,\n    html: Se,\n    lheading: ie,\n    list: Pe,\n    newline: xe,\n    paragraph: oe,\n    table: E,\n    text: we\n  },\n  re = h(\"^ *([^\\\\n ].*)\\\\n {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)\").replace(\"hr\", C).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"blockquote\", \" {0,3}>\").replace(\"code\", \"(?: {4}| {0,3}\t)[^\\\\n]\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", v).getRegex(),\n  _e = {\n    ...K,\n    lheading: Te,\n    table: re,\n    paragraph: h(F).replace(\"hr\", C).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"table\", re).replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", v).getRegex()\n  },\n  Le = {\n    ...K,\n    html: h(`^ *(?:comment *(?:\\\\n|\\\\s*$)|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\\\s[^'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))`).replace(\"comment\", U).replace(/tag/g, \"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b\").getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: E,\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: h(F).replace(\"hr\", C).replace(\"heading\", ` *#{1,6} *[^\n]`).replace(\"lheading\", ie).replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"|fences\", \"\").replace(\"|list\", \"\").replace(\"|html\", \"\").replace(\"|tag\", \"\").getRegex()\n  },\n  Me = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/,\n  ze = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/,\n  ae = /^( {2,}|\\\\)\\n(?!\\s*$)/,\n  Ae = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/,\n  D = /[\\p{P}\\p{S}]/u,\n  X = /[\\s\\p{P}\\p{S}]/u,\n  le = /[^\\s\\p{P}\\p{S}]/u,\n  Ee = h(/^((?![*_])punctSpace)/, \"u\").replace(/punctSpace/g, X).getRegex(),\n  ue = /(?!~)[\\p{P}\\p{S}]/u,\n  Ce = /(?!~)[\\s\\p{P}\\p{S}]/u,\n  Ie = /(?:[^\\s\\p{P}\\p{S}]|~)/u,\n  Be = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<(?! )[^<>]*?>/g,\n  pe = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/,\n  qe = h(pe, \"u\").replace(/punct/g, D).getRegex(),\n  ve = h(pe, \"u\").replace(/punct/g, ue).getRegex(),\n  ce = \"^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)|notPunctSpace(\\\\*+)(?=notPunctSpace)\",\n  De = h(ce, \"gu\").replace(/notPunctSpace/g, le).replace(/punctSpace/g, X).replace(/punct/g, D).getRegex(),\n  Ze = h(ce, \"gu\").replace(/notPunctSpace/g, Ie).replace(/punctSpace/g, Ce).replace(/punct/g, ue).getRegex(),\n  Ge = h(\"^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)\", \"gu\").replace(/notPunctSpace/g, le).replace(/punctSpace/g, X).replace(/punct/g, D).getRegex(),\n  He = h(/\\\\(punct)/, \"gu\").replace(/punct/g, D).getRegex(),\n  Ne = h(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/).replace(\"scheme\", /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace(\"email\", /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),\n  je = h(U).replace(\"(?:-->|$)\", \"-->\").getRegex(),\n  Fe = h(\"^comment|^</[a-zA-Z][\\\\w:-]*\\\\s*>|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>|^<\\\\?[\\\\s\\\\S]*?\\\\?>|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>\").replace(\"comment\", je).replace(\"attribute\", /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/).getRegex(),\n  q = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/,\n  Qe = h(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/).replace(\"label\", q).replace(\"href\", /<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/).replace(\"title\", /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/).getRegex(),\n  he = h(/^!?\\[(label)\\]\\[(ref)\\]/).replace(\"label\", q).replace(\"ref\", Q).getRegex(),\n  de = h(/^!?\\[(ref)\\](?:\\[\\])?/).replace(\"ref\", Q).getRegex(),\n  Ue = h(\"reflink|nolink(?!\\\\()\", \"g\").replace(\"reflink\", he).replace(\"nolink\", de).getRegex(),\n  W = {\n    _backpedal: E,\n    anyPunctuation: He,\n    autolink: Ne,\n    blockSkip: Be,\n    br: ae,\n    code: ze,\n    del: E,\n    emStrongLDelim: qe,\n    emStrongRDelimAst: De,\n    emStrongRDelimUnd: Ge,\n    escape: Me,\n    link: Qe,\n    nolink: de,\n    punctuation: Ee,\n    reflink: he,\n    reflinkSearch: Ue,\n    tag: Fe,\n    text: Ae,\n    url: E\n  },\n  Ke = {\n    ...W,\n    link: h(/^!?\\[(label)\\]\\((.*?)\\)/).replace(\"label\", q).getRegex(),\n    reflink: h(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/).replace(\"label\", q).getRegex()\n  },\n  N = {\n    ...W,\n    emStrongRDelimAst: Ze,\n    emStrongLDelim: ve,\n    url: h(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, \"i\").replace(\"email\", /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n  },\n  Xe = {\n    ...N,\n    br: h(ae).replace(\"{2,}\", \"*\").getRegex(),\n    text: h(N.text).replace(\"\\\\b_\", \"\\\\b_| {2,}\\\\n\").replace(/\\{2,\\}/g, \"*\").getRegex()\n  },\n  I = {\n    normal: K,\n    gfm: _e,\n    pedantic: Le\n  },\n  M = {\n    normal: W,\n    gfm: N,\n    breaks: Xe,\n    pedantic: Ke\n  };\nvar We = {\n    \"&\": \"&amp;\",\n    \"<\": \"&lt;\",\n    \">\": \"&gt;\",\n    '\"': \"&quot;\",\n    \"'\": \"&#39;\"\n  },\n  ke = l => We[l];\nfunction w(l, e) {\n  if (e) {\n    if (m.escapeTest.test(l)) return l.replace(m.escapeReplace, ke);\n  } else if (m.escapeTestNoEncode.test(l)) return l.replace(m.escapeReplaceNoEncode, ke);\n  return l;\n}\nfunction J(l) {\n  try {\n    l = encodeURI(l).replace(m.percentDecode, \"%\");\n  } catch {\n    return null;\n  }\n  return l;\n}\nfunction V(l, e) {\n  let t = l.replace(m.findPipe, (i, s, o) => {\n      let a = !1,\n        u = s;\n      for (; --u >= 0 && o[u] === \"\\\\\";) a = !a;\n      return a ? \"|\" : \" |\";\n    }),\n    n = t.split(m.splitPipe),\n    r = 0;\n  if (n[0].trim() || n.shift(), n.length > 0 && !n.at(-1)?.trim() && n.pop(), e) if (n.length > e) n.splice(e);else for (; n.length < e;) n.push(\"\");\n  for (; r < n.length; r++) n[r] = n[r].trim().replace(m.slashPipe, \"|\");\n  return n;\n}\nfunction z(l, e, t) {\n  let n = l.length;\n  if (n === 0) return \"\";\n  let r = 0;\n  for (; r < n;) {\n    let i = l.charAt(n - r - 1);\n    if (i === e && !t) r++;else if (i !== e && t) r++;else break;\n  }\n  return l.slice(0, n - r);\n}\nfunction ge(l, e) {\n  if (l.indexOf(e[1]) === -1) return -1;\n  let t = 0;\n  for (let n = 0; n < l.length; n++) if (l[n] === \"\\\\\") n++;else if (l[n] === e[0]) t++;else if (l[n] === e[1] && (t--, t < 0)) return n;\n  return t > 0 ? -2 : -1;\n}\nfunction fe(l, e, t, n, r) {\n  let i = e.href,\n    s = e.title || null,\n    o = l[1].replace(r.other.outputLinkReplace, \"$1\");\n  n.state.inLink = !0;\n  let a = {\n    type: l[0].charAt(0) === \"!\" ? \"image\" : \"link\",\n    raw: t,\n    href: i,\n    title: s,\n    text: o,\n    tokens: n.inlineTokens(o)\n  };\n  return n.state.inLink = !1, a;\n}\nfunction Je(l, e, t) {\n  let n = l.match(t.other.indentCodeCompensation);\n  if (n === null) return e;\n  let r = n[1];\n  return e.split(`\n`).map(i => {\n    let s = i.match(t.other.beginningSpace);\n    if (s === null) return i;\n    let [o] = s;\n    return o.length >= r.length ? i.slice(r.length) : i;\n  }).join(`\n`);\n}\nvar y = class {\n  options;\n  rules;\n  lexer;\n  constructor(e) {\n    this.options = e || O;\n  }\n  space(e) {\n    let t = this.rules.block.newline.exec(e);\n    if (t && t[0].length > 0) return {\n      type: \"space\",\n      raw: t[0]\n    };\n  }\n  code(e) {\n    let t = this.rules.block.code.exec(e);\n    if (t) {\n      let n = t[0].replace(this.rules.other.codeRemoveIndent, \"\");\n      return {\n        type: \"code\",\n        raw: t[0],\n        codeBlockStyle: \"indented\",\n        text: this.options.pedantic ? n : z(n, `\n`)\n      };\n    }\n  }\n  fences(e) {\n    let t = this.rules.block.fences.exec(e);\n    if (t) {\n      let n = t[0],\n        r = Je(n, t[3] || \"\", this.rules);\n      return {\n        type: \"code\",\n        raw: n,\n        lang: t[2] ? t[2].trim().replace(this.rules.inline.anyPunctuation, \"$1\") : t[2],\n        text: r\n      };\n    }\n  }\n  heading(e) {\n    let t = this.rules.block.heading.exec(e);\n    if (t) {\n      let n = t[2].trim();\n      if (this.rules.other.endingHash.test(n)) {\n        let r = z(n, \"#\");\n        (this.options.pedantic || !r || this.rules.other.endingSpaceChar.test(r)) && (n = r.trim());\n      }\n      return {\n        type: \"heading\",\n        raw: t[0],\n        depth: t[1].length,\n        text: n,\n        tokens: this.lexer.inline(n)\n      };\n    }\n  }\n  hr(e) {\n    let t = this.rules.block.hr.exec(e);\n    if (t) return {\n      type: \"hr\",\n      raw: z(t[0], `\n`)\n    };\n  }\n  blockquote(e) {\n    let t = this.rules.block.blockquote.exec(e);\n    if (t) {\n      let n = z(t[0], `\n`).split(`\n`),\n        r = \"\",\n        i = \"\",\n        s = [];\n      for (; n.length > 0;) {\n        let o = !1,\n          a = [],\n          u;\n        for (u = 0; u < n.length; u++) if (this.rules.other.blockquoteStart.test(n[u])) a.push(n[u]), o = !0;else if (!o) a.push(n[u]);else break;\n        n = n.slice(u);\n        let p = a.join(`\n`),\n          c = p.replace(this.rules.other.blockquoteSetextReplace, `\n    $1`).replace(this.rules.other.blockquoteSetextReplace2, \"\");\n        r = r ? `${r}\n${p}` : p, i = i ? `${i}\n${c}` : c;\n        let f = this.lexer.state.top;\n        if (this.lexer.state.top = !0, this.lexer.blockTokens(c, s, !0), this.lexer.state.top = f, n.length === 0) break;\n        let k = s.at(-1);\n        if (k?.type === \"code\") break;\n        if (k?.type === \"blockquote\") {\n          let x = k,\n            g = x.raw + `\n` + n.join(`\n`),\n            T = this.blockquote(g);\n          s[s.length - 1] = T, r = r.substring(0, r.length - x.raw.length) + T.raw, i = i.substring(0, i.length - x.text.length) + T.text;\n          break;\n        } else if (k?.type === \"list\") {\n          let x = k,\n            g = x.raw + `\n` + n.join(`\n`),\n            T = this.list(g);\n          s[s.length - 1] = T, r = r.substring(0, r.length - k.raw.length) + T.raw, i = i.substring(0, i.length - x.raw.length) + T.raw, n = g.substring(s.at(-1).raw.length).split(`\n`);\n          continue;\n        }\n      }\n      return {\n        type: \"blockquote\",\n        raw: r,\n        tokens: s,\n        text: i\n      };\n    }\n  }\n  list(e) {\n    let t = this.rules.block.list.exec(e);\n    if (t) {\n      let n = t[1].trim(),\n        r = n.length > 1,\n        i = {\n          type: \"list\",\n          raw: \"\",\n          ordered: r,\n          start: r ? +n.slice(0, -1) : \"\",\n          loose: !1,\n          items: []\n        };\n      n = r ? `\\\\d{1,9}\\\\${n.slice(-1)}` : `\\\\${n}`, this.options.pedantic && (n = r ? n : \"[*+-]\");\n      let s = this.rules.other.listItemRegex(n),\n        o = !1;\n      for (; e;) {\n        let u = !1,\n          p = \"\",\n          c = \"\";\n        if (!(t = s.exec(e)) || this.rules.block.hr.test(e)) break;\n        p = t[0], e = e.substring(p.length);\n        let f = t[2].split(`\n`, 1)[0].replace(this.rules.other.listReplaceTabs, Z => \" \".repeat(3 * Z.length)),\n          k = e.split(`\n`, 1)[0],\n          x = !f.trim(),\n          g = 0;\n        if (this.options.pedantic ? (g = 2, c = f.trimStart()) : x ? g = t[1].length + 1 : (g = t[2].search(this.rules.other.nonSpaceChar), g = g > 4 ? 1 : g, c = f.slice(g), g += t[1].length), x && this.rules.other.blankLine.test(k) && (p += k + `\n`, e = e.substring(k.length + 1), u = !0), !u) {\n          let Z = this.rules.other.nextBulletRegex(g),\n            ee = this.rules.other.hrRegex(g),\n            te = this.rules.other.fencesBeginRegex(g),\n            ne = this.rules.other.headingBeginRegex(g),\n            me = this.rules.other.htmlBeginRegex(g);\n          for (; e;) {\n            let G = e.split(`\n`, 1)[0],\n              A;\n            if (k = G, this.options.pedantic ? (k = k.replace(this.rules.other.listReplaceNesting, \"  \"), A = k) : A = k.replace(this.rules.other.tabCharGlobal, \"    \"), te.test(k) || ne.test(k) || me.test(k) || Z.test(k) || ee.test(k)) break;\n            if (A.search(this.rules.other.nonSpaceChar) >= g || !k.trim()) c += `\n` + A.slice(g);else {\n              if (x || f.replace(this.rules.other.tabCharGlobal, \"    \").search(this.rules.other.nonSpaceChar) >= 4 || te.test(f) || ne.test(f) || ee.test(f)) break;\n              c += `\n` + k;\n            }\n            !x && !k.trim() && (x = !0), p += G + `\n`, e = e.substring(G.length + 1), f = A.slice(g);\n          }\n        }\n        i.loose || (o ? i.loose = !0 : this.rules.other.doubleBlankLine.test(p) && (o = !0));\n        let T = null,\n          Y;\n        this.options.gfm && (T = this.rules.other.listIsTask.exec(c), T && (Y = T[0] !== \"[ ] \", c = c.replace(this.rules.other.listReplaceTask, \"\"))), i.items.push({\n          type: \"list_item\",\n          raw: p,\n          task: !!T,\n          checked: Y,\n          loose: !1,\n          text: c,\n          tokens: []\n        }), i.raw += p;\n      }\n      let a = i.items.at(-1);\n      if (a) a.raw = a.raw.trimEnd(), a.text = a.text.trimEnd();else return;\n      i.raw = i.raw.trimEnd();\n      for (let u = 0; u < i.items.length; u++) if (this.lexer.state.top = !1, i.items[u].tokens = this.lexer.blockTokens(i.items[u].text, []), !i.loose) {\n        let p = i.items[u].tokens.filter(f => f.type === \"space\"),\n          c = p.length > 0 && p.some(f => this.rules.other.anyLine.test(f.raw));\n        i.loose = c;\n      }\n      if (i.loose) for (let u = 0; u < i.items.length; u++) i.items[u].loose = !0;\n      return i;\n    }\n  }\n  html(e) {\n    let t = this.rules.block.html.exec(e);\n    if (t) return {\n      type: \"html\",\n      block: !0,\n      raw: t[0],\n      pre: t[1] === \"pre\" || t[1] === \"script\" || t[1] === \"style\",\n      text: t[0]\n    };\n  }\n  def(e) {\n    let t = this.rules.block.def.exec(e);\n    if (t) {\n      let n = t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, \" \"),\n        r = t[2] ? t[2].replace(this.rules.other.hrefBrackets, \"$1\").replace(this.rules.inline.anyPunctuation, \"$1\") : \"\",\n        i = t[3] ? t[3].substring(1, t[3].length - 1).replace(this.rules.inline.anyPunctuation, \"$1\") : t[3];\n      return {\n        type: \"def\",\n        tag: n,\n        raw: t[0],\n        href: r,\n        title: i\n      };\n    }\n  }\n  table(e) {\n    let t = this.rules.block.table.exec(e);\n    if (!t || !this.rules.other.tableDelimiter.test(t[2])) return;\n    let n = V(t[1]),\n      r = t[2].replace(this.rules.other.tableAlignChars, \"\").split(\"|\"),\n      i = t[3]?.trim() ? t[3].replace(this.rules.other.tableRowBlankLine, \"\").split(`\n`) : [],\n      s = {\n        type: \"table\",\n        raw: t[0],\n        header: [],\n        align: [],\n        rows: []\n      };\n    if (n.length === r.length) {\n      for (let o of r) this.rules.other.tableAlignRight.test(o) ? s.align.push(\"right\") : this.rules.other.tableAlignCenter.test(o) ? s.align.push(\"center\") : this.rules.other.tableAlignLeft.test(o) ? s.align.push(\"left\") : s.align.push(null);\n      for (let o = 0; o < n.length; o++) s.header.push({\n        text: n[o],\n        tokens: this.lexer.inline(n[o]),\n        header: !0,\n        align: s.align[o]\n      });\n      for (let o of i) s.rows.push(V(o, s.header.length).map((a, u) => ({\n        text: a,\n        tokens: this.lexer.inline(a),\n        header: !1,\n        align: s.align[u]\n      })));\n      return s;\n    }\n  }\n  lheading(e) {\n    let t = this.rules.block.lheading.exec(e);\n    if (t) return {\n      type: \"heading\",\n      raw: t[0],\n      depth: t[2].charAt(0) === \"=\" ? 1 : 2,\n      text: t[1],\n      tokens: this.lexer.inline(t[1])\n    };\n  }\n  paragraph(e) {\n    let t = this.rules.block.paragraph.exec(e);\n    if (t) {\n      let n = t[1].charAt(t[1].length - 1) === `\n` ? t[1].slice(0, -1) : t[1];\n      return {\n        type: \"paragraph\",\n        raw: t[0],\n        text: n,\n        tokens: this.lexer.inline(n)\n      };\n    }\n  }\n  text(e) {\n    let t = this.rules.block.text.exec(e);\n    if (t) return {\n      type: \"text\",\n      raw: t[0],\n      text: t[0],\n      tokens: this.lexer.inline(t[0])\n    };\n  }\n  escape(e) {\n    let t = this.rules.inline.escape.exec(e);\n    if (t) return {\n      type: \"escape\",\n      raw: t[0],\n      text: t[1]\n    };\n  }\n  tag(e) {\n    let t = this.rules.inline.tag.exec(e);\n    if (t) return !this.lexer.state.inLink && this.rules.other.startATag.test(t[0]) ? this.lexer.state.inLink = !0 : this.lexer.state.inLink && this.rules.other.endATag.test(t[0]) && (this.lexer.state.inLink = !1), !this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(t[0]) ? this.lexer.state.inRawBlock = !0 : this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(t[0]) && (this.lexer.state.inRawBlock = !1), {\n      type: \"html\",\n      raw: t[0],\n      inLink: this.lexer.state.inLink,\n      inRawBlock: this.lexer.state.inRawBlock,\n      block: !1,\n      text: t[0]\n    };\n  }\n  link(e) {\n    let t = this.rules.inline.link.exec(e);\n    if (t) {\n      let n = t[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(n)) {\n        if (!this.rules.other.endAngleBracket.test(n)) return;\n        let s = z(n.slice(0, -1), \"\\\\\");\n        if ((n.length - s.length) % 2 === 0) return;\n      } else {\n        let s = ge(t[2], \"()\");\n        if (s === -2) return;\n        if (s > -1) {\n          let a = (t[0].indexOf(\"!\") === 0 ? 5 : 4) + t[1].length + s;\n          t[2] = t[2].substring(0, s), t[0] = t[0].substring(0, a).trim(), t[3] = \"\";\n        }\n      }\n      let r = t[2],\n        i = \"\";\n      if (this.options.pedantic) {\n        let s = this.rules.other.pedanticHrefTitle.exec(r);\n        s && (r = s[1], i = s[3]);\n      } else i = t[3] ? t[3].slice(1, -1) : \"\";\n      return r = r.trim(), this.rules.other.startAngleBracket.test(r) && (this.options.pedantic && !this.rules.other.endAngleBracket.test(n) ? r = r.slice(1) : r = r.slice(1, -1)), fe(t, {\n        href: r && r.replace(this.rules.inline.anyPunctuation, \"$1\"),\n        title: i && i.replace(this.rules.inline.anyPunctuation, \"$1\")\n      }, t[0], this.lexer, this.rules);\n    }\n  }\n  reflink(e, t) {\n    let n;\n    if ((n = this.rules.inline.reflink.exec(e)) || (n = this.rules.inline.nolink.exec(e))) {\n      let r = (n[2] || n[1]).replace(this.rules.other.multipleSpaceGlobal, \" \"),\n        i = t[r.toLowerCase()];\n      if (!i) {\n        let s = n[0].charAt(0);\n        return {\n          type: \"text\",\n          raw: s,\n          text: s\n        };\n      }\n      return fe(n, i, n[0], this.lexer, this.rules);\n    }\n  }\n  emStrong(e, t, n = \"\") {\n    let r = this.rules.inline.emStrongLDelim.exec(e);\n    if (!r || r[3] && n.match(this.rules.other.unicodeAlphaNumeric)) return;\n    if (!(r[1] || r[2] || \"\") || !n || this.rules.inline.punctuation.exec(n)) {\n      let s = [...r[0]].length - 1,\n        o,\n        a,\n        u = s,\n        p = 0,\n        c = r[0][0] === \"*\" ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      for (c.lastIndex = 0, t = t.slice(-1 * e.length + s); (r = c.exec(t)) != null;) {\n        if (o = r[1] || r[2] || r[3] || r[4] || r[5] || r[6], !o) continue;\n        if (a = [...o].length, r[3] || r[4]) {\n          u += a;\n          continue;\n        } else if ((r[5] || r[6]) && s % 3 && !((s + a) % 3)) {\n          p += a;\n          continue;\n        }\n        if (u -= a, u > 0) continue;\n        a = Math.min(a, a + u + p);\n        let f = [...r[0]][0].length,\n          k = e.slice(0, s + r.index + f + a);\n        if (Math.min(s, a) % 2) {\n          let g = k.slice(1, -1);\n          return {\n            type: \"em\",\n            raw: k,\n            text: g,\n            tokens: this.lexer.inlineTokens(g)\n          };\n        }\n        let x = k.slice(2, -2);\n        return {\n          type: \"strong\",\n          raw: k,\n          text: x,\n          tokens: this.lexer.inlineTokens(x)\n        };\n      }\n    }\n  }\n  codespan(e) {\n    let t = this.rules.inline.code.exec(e);\n    if (t) {\n      let n = t[2].replace(this.rules.other.newLineCharGlobal, \" \"),\n        r = this.rules.other.nonSpaceChar.test(n),\n        i = this.rules.other.startingSpaceChar.test(n) && this.rules.other.endingSpaceChar.test(n);\n      return r && i && (n = n.substring(1, n.length - 1)), {\n        type: \"codespan\",\n        raw: t[0],\n        text: n\n      };\n    }\n  }\n  br(e) {\n    let t = this.rules.inline.br.exec(e);\n    if (t) return {\n      type: \"br\",\n      raw: t[0]\n    };\n  }\n  del(e) {\n    let t = this.rules.inline.del.exec(e);\n    if (t) return {\n      type: \"del\",\n      raw: t[0],\n      text: t[2],\n      tokens: this.lexer.inlineTokens(t[2])\n    };\n  }\n  autolink(e) {\n    let t = this.rules.inline.autolink.exec(e);\n    if (t) {\n      let n, r;\n      return t[2] === \"@\" ? (n = t[1], r = \"mailto:\" + n) : (n = t[1], r = n), {\n        type: \"link\",\n        raw: t[0],\n        text: n,\n        href: r,\n        tokens: [{\n          type: \"text\",\n          raw: n,\n          text: n\n        }]\n      };\n    }\n  }\n  url(e) {\n    let t;\n    if (t = this.rules.inline.url.exec(e)) {\n      let n, r;\n      if (t[2] === \"@\") n = t[0], r = \"mailto:\" + n;else {\n        let i;\n        do i = t[0], t[0] = this.rules.inline._backpedal.exec(t[0])?.[0] ?? \"\"; while (i !== t[0]);\n        n = t[0], t[1] === \"www.\" ? r = \"http://\" + t[0] : r = t[0];\n      }\n      return {\n        type: \"link\",\n        raw: t[0],\n        text: n,\n        href: r,\n        tokens: [{\n          type: \"text\",\n          raw: n,\n          text: n\n        }]\n      };\n    }\n  }\n  inlineText(e) {\n    let t = this.rules.inline.text.exec(e);\n    if (t) {\n      let n = this.lexer.state.inRawBlock;\n      return {\n        type: \"text\",\n        raw: t[0],\n        text: t[0],\n        escaped: n\n      };\n    }\n  }\n};\nvar b = class l {\n  tokens;\n  options;\n  state;\n  tokenizer;\n  inlineQueue;\n  constructor(e) {\n    this.tokens = [], this.tokens.links = Object.create(null), this.options = e || O, this.options.tokenizer = this.options.tokenizer || new y(), this.tokenizer = this.options.tokenizer, this.tokenizer.options = this.options, this.tokenizer.lexer = this, this.inlineQueue = [], this.state = {\n      inLink: !1,\n      inRawBlock: !1,\n      top: !0\n    };\n    let t = {\n      other: m,\n      block: I.normal,\n      inline: M.normal\n    };\n    this.options.pedantic ? (t.block = I.pedantic, t.inline = M.pedantic) : this.options.gfm && (t.block = I.gfm, this.options.breaks ? t.inline = M.breaks : t.inline = M.gfm), this.tokenizer.rules = t;\n  }\n  static get rules() {\n    return {\n      block: I,\n      inline: M\n    };\n  }\n  static lex(e, t) {\n    return new l(t).lex(e);\n  }\n  static lexInline(e, t) {\n    return new l(t).inlineTokens(e);\n  }\n  lex(e) {\n    e = e.replace(m.carriageReturn, `\n`), this.blockTokens(e, this.tokens);\n    for (let t = 0; t < this.inlineQueue.length; t++) {\n      let n = this.inlineQueue[t];\n      this.inlineTokens(n.src, n.tokens);\n    }\n    return this.inlineQueue = [], this.tokens;\n  }\n  blockTokens(e, t = [], n = !1) {\n    for (this.options.pedantic && (e = e.replace(m.tabCharGlobal, \"    \").replace(m.spaceLine, \"\")); e;) {\n      let r;\n      if (this.options.extensions?.block?.some(s => (r = s.call({\n        lexer: this\n      }, e, t)) ? (e = e.substring(r.raw.length), t.push(r), !0) : !1)) continue;\n      if (r = this.tokenizer.space(e)) {\n        e = e.substring(r.raw.length);\n        let s = t.at(-1);\n        r.raw.length === 1 && s !== void 0 ? s.raw += `\n` : t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.code(e)) {\n        e = e.substring(r.raw.length);\n        let s = t.at(-1);\n        s?.type === \"paragraph\" || s?.type === \"text\" ? (s.raw += `\n` + r.raw, s.text += `\n` + r.text, this.inlineQueue.at(-1).src = s.text) : t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.fences(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.heading(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.hr(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.blockquote(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.list(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.html(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.def(e)) {\n        e = e.substring(r.raw.length);\n        let s = t.at(-1);\n        s?.type === \"paragraph\" || s?.type === \"text\" ? (s.raw += `\n` + r.raw, s.text += `\n` + r.raw, this.inlineQueue.at(-1).src = s.text) : this.tokens.links[r.tag] || (this.tokens.links[r.tag] = {\n          href: r.href,\n          title: r.title\n        });\n        continue;\n      }\n      if (r = this.tokenizer.table(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.lheading(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      let i = e;\n      if (this.options.extensions?.startBlock) {\n        let s = 1 / 0,\n          o = e.slice(1),\n          a;\n        this.options.extensions.startBlock.forEach(u => {\n          a = u.call({\n            lexer: this\n          }, o), typeof a == \"number\" && a >= 0 && (s = Math.min(s, a));\n        }), s < 1 / 0 && s >= 0 && (i = e.substring(0, s + 1));\n      }\n      if (this.state.top && (r = this.tokenizer.paragraph(i))) {\n        let s = t.at(-1);\n        n && s?.type === \"paragraph\" ? (s.raw += `\n` + r.raw, s.text += `\n` + r.text, this.inlineQueue.pop(), this.inlineQueue.at(-1).src = s.text) : t.push(r), n = i.length !== e.length, e = e.substring(r.raw.length);\n        continue;\n      }\n      if (r = this.tokenizer.text(e)) {\n        e = e.substring(r.raw.length);\n        let s = t.at(-1);\n        s?.type === \"text\" ? (s.raw += `\n` + r.raw, s.text += `\n` + r.text, this.inlineQueue.pop(), this.inlineQueue.at(-1).src = s.text) : t.push(r);\n        continue;\n      }\n      if (e) {\n        let s = \"Infinite loop on byte: \" + e.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(s);\n          break;\n        } else throw new Error(s);\n      }\n    }\n    return this.state.top = !0, t;\n  }\n  inline(e, t = []) {\n    return this.inlineQueue.push({\n      src: e,\n      tokens: t\n    }), t;\n  }\n  inlineTokens(e, t = []) {\n    let n = e,\n      r = null;\n    if (this.tokens.links) {\n      let o = Object.keys(this.tokens.links);\n      if (o.length > 0) for (; (r = this.tokenizer.rules.inline.reflinkSearch.exec(n)) != null;) o.includes(r[0].slice(r[0].lastIndexOf(\"[\") + 1, -1)) && (n = n.slice(0, r.index) + \"[\" + \"a\".repeat(r[0].length - 2) + \"]\" + n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex));\n    }\n    for (; (r = this.tokenizer.rules.inline.anyPunctuation.exec(n)) != null;) n = n.slice(0, r.index) + \"++\" + n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    for (; (r = this.tokenizer.rules.inline.blockSkip.exec(n)) != null;) n = n.slice(0, r.index) + \"[\" + \"a\".repeat(r[0].length - 2) + \"]\" + n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    let i = !1,\n      s = \"\";\n    for (; e;) {\n      i || (s = \"\"), i = !1;\n      let o;\n      if (this.options.extensions?.inline?.some(u => (o = u.call({\n        lexer: this\n      }, e, t)) ? (e = e.substring(o.raw.length), t.push(o), !0) : !1)) continue;\n      if (o = this.tokenizer.escape(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.tag(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.link(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.reflink(e, this.tokens.links)) {\n        e = e.substring(o.raw.length);\n        let u = t.at(-1);\n        o.type === \"text\" && u?.type === \"text\" ? (u.raw += o.raw, u.text += o.text) : t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.emStrong(e, n, s)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.codespan(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.br(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.del(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.autolink(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (!this.state.inLink && (o = this.tokenizer.url(e))) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      let a = e;\n      if (this.options.extensions?.startInline) {\n        let u = 1 / 0,\n          p = e.slice(1),\n          c;\n        this.options.extensions.startInline.forEach(f => {\n          c = f.call({\n            lexer: this\n          }, p), typeof c == \"number\" && c >= 0 && (u = Math.min(u, c));\n        }), u < 1 / 0 && u >= 0 && (a = e.substring(0, u + 1));\n      }\n      if (o = this.tokenizer.inlineText(a)) {\n        e = e.substring(o.raw.length), o.raw.slice(-1) !== \"_\" && (s = o.raw.slice(-1)), i = !0;\n        let u = t.at(-1);\n        u?.type === \"text\" ? (u.raw += o.raw, u.text += o.text) : t.push(o);\n        continue;\n      }\n      if (e) {\n        let u = \"Infinite loop on byte: \" + e.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(u);\n          break;\n        } else throw new Error(u);\n      }\n    }\n    return t;\n  }\n};\nvar P = class {\n  options;\n  parser;\n  constructor(e) {\n    this.options = e || O;\n  }\n  space(e) {\n    return \"\";\n  }\n  code({\n    text: e,\n    lang: t,\n    escaped: n\n  }) {\n    let r = (t || \"\").match(m.notSpaceStart)?.[0],\n      i = e.replace(m.endingNewline, \"\") + `\n`;\n    return r ? '<pre><code class=\"language-' + w(r) + '\">' + (n ? i : w(i, !0)) + `</code></pre>\n` : \"<pre><code>\" + (n ? i : w(i, !0)) + `</code></pre>\n`;\n  }\n  blockquote({\n    tokens: e\n  }) {\n    return `<blockquote>\n${this.parser.parse(e)}</blockquote>\n`;\n  }\n  html({\n    text: e\n  }) {\n    return e;\n  }\n  heading({\n    tokens: e,\n    depth: t\n  }) {\n    return `<h${t}>${this.parser.parseInline(e)}</h${t}>\n`;\n  }\n  hr(e) {\n    return `<hr>\n`;\n  }\n  list(e) {\n    let t = e.ordered,\n      n = e.start,\n      r = \"\";\n    for (let o = 0; o < e.items.length; o++) {\n      let a = e.items[o];\n      r += this.listitem(a);\n    }\n    let i = t ? \"ol\" : \"ul\",\n      s = t && n !== 1 ? ' start=\"' + n + '\"' : \"\";\n    return \"<\" + i + s + `>\n` + r + \"</\" + i + `>\n`;\n  }\n  listitem(e) {\n    let t = \"\";\n    if (e.task) {\n      let n = this.checkbox({\n        checked: !!e.checked\n      });\n      e.loose ? e.tokens[0]?.type === \"paragraph\" ? (e.tokens[0].text = n + \" \" + e.tokens[0].text, e.tokens[0].tokens && e.tokens[0].tokens.length > 0 && e.tokens[0].tokens[0].type === \"text\" && (e.tokens[0].tokens[0].text = n + \" \" + w(e.tokens[0].tokens[0].text), e.tokens[0].tokens[0].escaped = !0)) : e.tokens.unshift({\n        type: \"text\",\n        raw: n + \" \",\n        text: n + \" \",\n        escaped: !0\n      }) : t += n + \" \";\n    }\n    return t += this.parser.parse(e.tokens, !!e.loose), `<li>${t}</li>\n`;\n  }\n  checkbox({\n    checked: e\n  }) {\n    return \"<input \" + (e ? 'checked=\"\" ' : \"\") + 'disabled=\"\" type=\"checkbox\">';\n  }\n  paragraph({\n    tokens: e\n  }) {\n    return `<p>${this.parser.parseInline(e)}</p>\n`;\n  }\n  table(e) {\n    let t = \"\",\n      n = \"\";\n    for (let i = 0; i < e.header.length; i++) n += this.tablecell(e.header[i]);\n    t += this.tablerow({\n      text: n\n    });\n    let r = \"\";\n    for (let i = 0; i < e.rows.length; i++) {\n      let s = e.rows[i];\n      n = \"\";\n      for (let o = 0; o < s.length; o++) n += this.tablecell(s[o]);\n      r += this.tablerow({\n        text: n\n      });\n    }\n    return r && (r = `<tbody>${r}</tbody>`), `<table>\n<thead>\n` + t + `</thead>\n` + r + `</table>\n`;\n  }\n  tablerow({\n    text: e\n  }) {\n    return `<tr>\n${e}</tr>\n`;\n  }\n  tablecell(e) {\n    let t = this.parser.parseInline(e.tokens),\n      n = e.header ? \"th\" : \"td\";\n    return (e.align ? `<${n} align=\"${e.align}\">` : `<${n}>`) + t + `</${n}>\n`;\n  }\n  strong({\n    tokens: e\n  }) {\n    return `<strong>${this.parser.parseInline(e)}</strong>`;\n  }\n  em({\n    tokens: e\n  }) {\n    return `<em>${this.parser.parseInline(e)}</em>`;\n  }\n  codespan({\n    text: e\n  }) {\n    return `<code>${w(e, !0)}</code>`;\n  }\n  br(e) {\n    return \"<br>\";\n  }\n  del({\n    tokens: e\n  }) {\n    return `<del>${this.parser.parseInline(e)}</del>`;\n  }\n  link({\n    href: e,\n    title: t,\n    tokens: n\n  }) {\n    let r = this.parser.parseInline(n),\n      i = J(e);\n    if (i === null) return r;\n    e = i;\n    let s = '<a href=\"' + e + '\"';\n    return t && (s += ' title=\"' + w(t) + '\"'), s += \">\" + r + \"</a>\", s;\n  }\n  image({\n    href: e,\n    title: t,\n    text: n,\n    tokens: r\n  }) {\n    r && (n = this.parser.parseInline(r, this.parser.textRenderer));\n    let i = J(e);\n    if (i === null) return w(n);\n    e = i;\n    let s = `<img src=\"${e}\" alt=\"${n}\"`;\n    return t && (s += ` title=\"${w(t)}\"`), s += \">\", s;\n  }\n  text(e) {\n    return \"tokens\" in e && e.tokens ? this.parser.parseInline(e.tokens) : \"escaped\" in e && e.escaped ? e.text : w(e.text);\n  }\n};\nvar S = class {\n  strong({\n    text: e\n  }) {\n    return e;\n  }\n  em({\n    text: e\n  }) {\n    return e;\n  }\n  codespan({\n    text: e\n  }) {\n    return e;\n  }\n  del({\n    text: e\n  }) {\n    return e;\n  }\n  html({\n    text: e\n  }) {\n    return e;\n  }\n  text({\n    text: e\n  }) {\n    return e;\n  }\n  link({\n    text: e\n  }) {\n    return \"\" + e;\n  }\n  image({\n    text: e\n  }) {\n    return \"\" + e;\n  }\n  br() {\n    return \"\";\n  }\n};\nvar R = class l {\n  options;\n  renderer;\n  textRenderer;\n  constructor(e) {\n    this.options = e || O, this.options.renderer = this.options.renderer || new P(), this.renderer = this.options.renderer, this.renderer.options = this.options, this.renderer.parser = this, this.textRenderer = new S();\n  }\n  static parse(e, t) {\n    return new l(t).parse(e);\n  }\n  static parseInline(e, t) {\n    return new l(t).parseInline(e);\n  }\n  parse(e, t = !0) {\n    let n = \"\";\n    for (let r = 0; r < e.length; r++) {\n      let i = e[r];\n      if (this.options.extensions?.renderers?.[i.type]) {\n        let o = i,\n          a = this.options.extensions.renderers[o.type].call({\n            parser: this\n          }, o);\n        if (a !== !1 || ![\"space\", \"hr\", \"heading\", \"code\", \"table\", \"blockquote\", \"list\", \"html\", \"paragraph\", \"text\"].includes(o.type)) {\n          n += a || \"\";\n          continue;\n        }\n      }\n      let s = i;\n      switch (s.type) {\n        case \"space\":\n          {\n            n += this.renderer.space(s);\n            continue;\n          }\n        case \"hr\":\n          {\n            n += this.renderer.hr(s);\n            continue;\n          }\n        case \"heading\":\n          {\n            n += this.renderer.heading(s);\n            continue;\n          }\n        case \"code\":\n          {\n            n += this.renderer.code(s);\n            continue;\n          }\n        case \"table\":\n          {\n            n += this.renderer.table(s);\n            continue;\n          }\n        case \"blockquote\":\n          {\n            n += this.renderer.blockquote(s);\n            continue;\n          }\n        case \"list\":\n          {\n            n += this.renderer.list(s);\n            continue;\n          }\n        case \"html\":\n          {\n            n += this.renderer.html(s);\n            continue;\n          }\n        case \"paragraph\":\n          {\n            n += this.renderer.paragraph(s);\n            continue;\n          }\n        case \"text\":\n          {\n            let o = s,\n              a = this.renderer.text(o);\n            for (; r + 1 < e.length && e[r + 1].type === \"text\";) o = e[++r], a += `\n` + this.renderer.text(o);\n            t ? n += this.renderer.paragraph({\n              type: \"paragraph\",\n              raw: a,\n              text: a,\n              tokens: [{\n                type: \"text\",\n                raw: a,\n                text: a,\n                escaped: !0\n              }]\n            }) : n += a;\n            continue;\n          }\n        default:\n          {\n            let o = 'Token with \"' + s.type + '\" type was not found.';\n            if (this.options.silent) return console.error(o), \"\";\n            throw new Error(o);\n          }\n      }\n    }\n    return n;\n  }\n  parseInline(e, t = this.renderer) {\n    let n = \"\";\n    for (let r = 0; r < e.length; r++) {\n      let i = e[r];\n      if (this.options.extensions?.renderers?.[i.type]) {\n        let o = this.options.extensions.renderers[i.type].call({\n          parser: this\n        }, i);\n        if (o !== !1 || ![\"escape\", \"html\", \"link\", \"image\", \"strong\", \"em\", \"codespan\", \"br\", \"del\", \"text\"].includes(i.type)) {\n          n += o || \"\";\n          continue;\n        }\n      }\n      let s = i;\n      switch (s.type) {\n        case \"escape\":\n          {\n            n += t.text(s);\n            break;\n          }\n        case \"html\":\n          {\n            n += t.html(s);\n            break;\n          }\n        case \"link\":\n          {\n            n += t.link(s);\n            break;\n          }\n        case \"image\":\n          {\n            n += t.image(s);\n            break;\n          }\n        case \"strong\":\n          {\n            n += t.strong(s);\n            break;\n          }\n        case \"em\":\n          {\n            n += t.em(s);\n            break;\n          }\n        case \"codespan\":\n          {\n            n += t.codespan(s);\n            break;\n          }\n        case \"br\":\n          {\n            n += t.br(s);\n            break;\n          }\n        case \"del\":\n          {\n            n += t.del(s);\n            break;\n          }\n        case \"text\":\n          {\n            n += t.text(s);\n            break;\n          }\n        default:\n          {\n            let o = 'Token with \"' + s.type + '\" type was not found.';\n            if (this.options.silent) return console.error(o), \"\";\n            throw new Error(o);\n          }\n      }\n    }\n    return n;\n  }\n};\nvar $ = class {\n  options;\n  block;\n  constructor(e) {\n    this.options = e || O;\n  }\n  static passThroughHooks = new Set([\"preprocess\", \"postprocess\", \"processAllTokens\"]);\n  preprocess(e) {\n    return e;\n  }\n  postprocess(e) {\n    return e;\n  }\n  processAllTokens(e) {\n    return e;\n  }\n  provideLexer() {\n    return this.block ? b.lex : b.lexInline;\n  }\n  provideParser() {\n    return this.block ? R.parse : R.parseInline;\n  }\n};\nvar B = class {\n  defaults = L();\n  options = this.setOptions;\n  parse = this.parseMarkdown(!0);\n  parseInline = this.parseMarkdown(!1);\n  Parser = R;\n  Renderer = P;\n  TextRenderer = S;\n  Lexer = b;\n  Tokenizer = y;\n  Hooks = $;\n  constructor(...e) {\n    this.use(...e);\n  }\n  walkTokens(e, t) {\n    let n = [];\n    for (let r of e) switch (n = n.concat(t.call(this, r)), r.type) {\n      case \"table\":\n        {\n          let i = r;\n          for (let s of i.header) n = n.concat(this.walkTokens(s.tokens, t));\n          for (let s of i.rows) for (let o of s) n = n.concat(this.walkTokens(o.tokens, t));\n          break;\n        }\n      case \"list\":\n        {\n          let i = r;\n          n = n.concat(this.walkTokens(i.items, t));\n          break;\n        }\n      default:\n        {\n          let i = r;\n          this.defaults.extensions?.childTokens?.[i.type] ? this.defaults.extensions.childTokens[i.type].forEach(s => {\n            let o = i[s].flat(1 / 0);\n            n = n.concat(this.walkTokens(o, t));\n          }) : i.tokens && (n = n.concat(this.walkTokens(i.tokens, t)));\n        }\n    }\n    return n;\n  }\n  use(...e) {\n    let t = this.defaults.extensions || {\n      renderers: {},\n      childTokens: {}\n    };\n    return e.forEach(n => {\n      let r = {\n        ...n\n      };\n      if (r.async = this.defaults.async || r.async || !1, n.extensions && (n.extensions.forEach(i => {\n        if (!i.name) throw new Error(\"extension name required\");\n        if (\"renderer\" in i) {\n          let s = t.renderers[i.name];\n          s ? t.renderers[i.name] = function (...o) {\n            let a = i.renderer.apply(this, o);\n            return a === !1 && (a = s.apply(this, o)), a;\n          } : t.renderers[i.name] = i.renderer;\n        }\n        if (\"tokenizer\" in i) {\n          if (!i.level || i.level !== \"block\" && i.level !== \"inline\") throw new Error(\"extension level must be 'block' or 'inline'\");\n          let s = t[i.level];\n          s ? s.unshift(i.tokenizer) : t[i.level] = [i.tokenizer], i.start && (i.level === \"block\" ? t.startBlock ? t.startBlock.push(i.start) : t.startBlock = [i.start] : i.level === \"inline\" && (t.startInline ? t.startInline.push(i.start) : t.startInline = [i.start]));\n        }\n        \"childTokens\" in i && i.childTokens && (t.childTokens[i.name] = i.childTokens);\n      }), r.extensions = t), n.renderer) {\n        let i = this.defaults.renderer || new P(this.defaults);\n        for (let s in n.renderer) {\n          if (!(s in i)) throw new Error(`renderer '${s}' does not exist`);\n          if ([\"options\", \"parser\"].includes(s)) continue;\n          let o = s,\n            a = n.renderer[o],\n            u = i[o];\n          i[o] = (...p) => {\n            let c = a.apply(i, p);\n            return c === !1 && (c = u.apply(i, p)), c || \"\";\n          };\n        }\n        r.renderer = i;\n      }\n      if (n.tokenizer) {\n        let i = this.defaults.tokenizer || new y(this.defaults);\n        for (let s in n.tokenizer) {\n          if (!(s in i)) throw new Error(`tokenizer '${s}' does not exist`);\n          if ([\"options\", \"rules\", \"lexer\"].includes(s)) continue;\n          let o = s,\n            a = n.tokenizer[o],\n            u = i[o];\n          i[o] = (...p) => {\n            let c = a.apply(i, p);\n            return c === !1 && (c = u.apply(i, p)), c;\n          };\n        }\n        r.tokenizer = i;\n      }\n      if (n.hooks) {\n        let i = this.defaults.hooks || new $();\n        for (let s in n.hooks) {\n          if (!(s in i)) throw new Error(`hook '${s}' does not exist`);\n          if ([\"options\", \"block\"].includes(s)) continue;\n          let o = s,\n            a = n.hooks[o],\n            u = i[o];\n          $.passThroughHooks.has(s) ? i[o] = p => {\n            if (this.defaults.async) return Promise.resolve(a.call(i, p)).then(f => u.call(i, f));\n            let c = a.call(i, p);\n            return u.call(i, c);\n          } : i[o] = (...p) => {\n            let c = a.apply(i, p);\n            return c === !1 && (c = u.apply(i, p)), c;\n          };\n        }\n        r.hooks = i;\n      }\n      if (n.walkTokens) {\n        let i = this.defaults.walkTokens,\n          s = n.walkTokens;\n        r.walkTokens = function (o) {\n          let a = [];\n          return a.push(s.call(this, o)), i && (a = a.concat(i.call(this, o))), a;\n        };\n      }\n      this.defaults = {\n        ...this.defaults,\n        ...r\n      };\n    }), this;\n  }\n  setOptions(e) {\n    return this.defaults = {\n      ...this.defaults,\n      ...e\n    }, this;\n  }\n  lexer(e, t) {\n    return b.lex(e, t ?? this.defaults);\n  }\n  parser(e, t) {\n    return R.parse(e, t ?? this.defaults);\n  }\n  parseMarkdown(e) {\n    return (n, r) => {\n      let i = {\n          ...r\n        },\n        s = {\n          ...this.defaults,\n          ...i\n        },\n        o = this.onError(!!s.silent, !!s.async);\n      if (this.defaults.async === !0 && i.async === !1) return o(new Error(\"marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.\"));\n      if (typeof n > \"u\" || n === null) return o(new Error(\"marked(): input parameter is undefined or null\"));\n      if (typeof n != \"string\") return o(new Error(\"marked(): input parameter is of type \" + Object.prototype.toString.call(n) + \", string expected\"));\n      s.hooks && (s.hooks.options = s, s.hooks.block = e);\n      let a = s.hooks ? s.hooks.provideLexer() : e ? b.lex : b.lexInline,\n        u = s.hooks ? s.hooks.provideParser() : e ? R.parse : R.parseInline;\n      if (s.async) return Promise.resolve(s.hooks ? s.hooks.preprocess(n) : n).then(p => a(p, s)).then(p => s.hooks ? s.hooks.processAllTokens(p) : p).then(p => s.walkTokens ? Promise.all(this.walkTokens(p, s.walkTokens)).then(() => p) : p).then(p => u(p, s)).then(p => s.hooks ? s.hooks.postprocess(p) : p).catch(o);\n      try {\n        s.hooks && (n = s.hooks.preprocess(n));\n        let p = a(n, s);\n        s.hooks && (p = s.hooks.processAllTokens(p)), s.walkTokens && this.walkTokens(p, s.walkTokens);\n        let c = u(p, s);\n        return s.hooks && (c = s.hooks.postprocess(c)), c;\n      } catch (p) {\n        return o(p);\n      }\n    };\n  }\n  onError(e, t) {\n    return n => {\n      if (n.message += `\nPlease report this to https://github.com/markedjs/marked.`, e) {\n        let r = \"<p>An error occurred:</p><pre>\" + w(n.message + \"\", !0) + \"</pre>\";\n        return t ? Promise.resolve(r) : r;\n      }\n      if (t) return Promise.reject(n);\n      throw n;\n    };\n  }\n};\nvar _ = new B();\nfunction d(l, e) {\n  return _.parse(l, e);\n}\nd.options = d.setOptions = function (l) {\n  return _.setOptions(l), d.defaults = _.defaults, H(d.defaults), d;\n};\nd.getDefaults = L;\nd.defaults = O;\nd.use = function (...l) {\n  return _.use(...l), d.defaults = _.defaults, H(d.defaults), d;\n};\nd.walkTokens = function (l, e) {\n  return _.walkTokens(l, e);\n};\nd.parseInline = _.parseInline;\nd.Parser = R;\nd.parser = R.parse;\nd.Renderer = P;\nd.TextRenderer = S;\nd.Lexer = b;\nd.lexer = b.lex;\nd.Tokenizer = y;\nd.Hooks = $;\nd.parse = d;\nvar Dt = d.options,\n  Zt = d.setOptions,\n  Gt = d.use,\n  Ht = d.walkTokens,\n  Nt = d.parseInline,\n  jt = d,\n  Ft = R.parse,\n  Qt = b.lex;\nexport { $ as Hooks, b as Lexer, B as Marked, R as Parser, P as Renderer, S as TextRenderer, y as Tokenizer, O as defaults, L as getDefaults, Qt as lexer, d as marked, Dt as options, jt as parse, Nt as parseInline, Ft as parser, Zt as setOptions, Gt as use, Ht as walkTokens };", "map": {"version": 3, "names": ["L", "async", "breaks", "extensions", "gfm", "hooks", "pedantic", "renderer", "silent", "tokenizer", "walkTokens", "O", "H", "l", "E", "exec", "h", "e", "t", "source", "n", "replace", "r", "i", "s", "m", "caret", "getRegex", "RegExp", "codeRemoveIndent", "outputLinkReplace", "indentCodeCompensation", "beginningSpace", "endingHash", "startingSpaceChar", "endingSpaceChar", "nonSpaceChar", "newLineCharGlobal", "tabCharGlobal", "multipleSpaceGlobal", "blankLine", "doubleBlankLine", "blockquoteStart", "blockquoteSetextReplace", "blockquoteSetextReplace2", "listReplaceTabs", "listReplaceNesting", "listIsTask", "listReplaceTask", "anyLine", "hrefBrackets", "tableDelimiter", "tableAlignChars", "tableRowBlankLine", "tableAlignRight", "tableAlignCenter", "tableAlignLeft", "startATag", "endATag", "startPreScriptTag", "endPreScriptTag", "startAngleBracket", "endAngleBracket", "pedanticHrefTitle", "unicodeAlphaNumeric", "escapeTest", "escapeReplace", "escapeTestNoEncode", "escapeReplaceNoEncode", "unescapeTest", "percentDecode", "find<PERSON>ipe", "splitPipe", "slashPipe", "carriageReturn", "spaceLine", "notSpaceStart", "endingNewline", "listItemRegex", "nextBulletRegex", "Math", "min", "hrRegex", "fencesBeginRegex", "headingBeginRegex", "htmlBeginRegex", "xe", "be", "Re", "C", "Oe", "j", "se", "ie", "Te", "F", "we", "Q", "ye", "Pe", "v", "U", "Se", "oe", "$e", "K", "blockquote", "code", "def", "fences", "heading", "hr", "html", "lheading", "list", "newline", "paragraph", "table", "text", "re", "_e", "Le", "Me", "ze", "ae", "Ae", "D", "X", "le", "Ee", "ue", "Ce", "Ie", "Be", "pe", "qe", "ve", "ce", "De", "Ze", "Ge", "He", "Ne", "je", "Fe", "q", "Qe", "he", "de", "Ue", "W", "_backpedal", "anyPunctuation", "autolink", "blockSkip", "br", "del", "em<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "emStrongRDelim<PERSON>t", "emStrongRDelimUnd", "escape", "link", "nolink", "punctuation", "reflink", "reflinkSearch", "tag", "url", "<PERSON>", "N", "Xe", "I", "normal", "M", "We", "ke", "w", "test", "J", "encodeURI", "V", "o", "a", "u", "split", "trim", "shift", "length", "at", "pop", "splice", "push", "z", "char<PERSON>t", "slice", "ge", "indexOf", "fe", "href", "title", "other", "state", "inLink", "type", "raw", "tokens", "inlineTokens", "Je", "match", "map", "join", "y", "options", "rules", "lexer", "constructor", "space", "block", "codeBlockStyle", "lang", "inline", "depth", "p", "c", "f", "top", "blockTokens", "k", "x", "g", "T", "substring", "ordered", "start", "loose", "items", "Z", "repeat", "trimStart", "search", "ee", "te", "ne", "me", "G", "A", "Y", "task", "checked", "trimEnd", "filter", "some", "pre", "toLowerCase", "header", "align", "rows", "inRawBlock", "emStrong", "lastIndex", "index", "codespan", "inlineText", "escaped", "b", "inlineQueue", "links", "Object", "create", "lex", "lexInline", "src", "call", "startBlock", "for<PERSON>ach", "charCodeAt", "console", "error", "Error", "keys", "includes", "lastIndexOf", "startInline", "P", "parser", "parse", "parseInline", "listitem", "checkbox", "unshift", "tablecell", "tablerow", "strong", "em", "image", "<PERSON><PERSON><PERSON><PERSON>", "S", "R", "renderers", "$", "passThroughHooks", "Set", "preprocess", "postprocess", "processAllTokens", "provideLexer", "<PERSON><PERSON><PERSON><PERSON>", "B", "defaults", "setOptions", "parseMarkdown", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tokenizer", "<PERSON>s", "use", "concat", "childTokens", "flat", "name", "apply", "level", "has", "Promise", "resolve", "then", "onError", "prototype", "toString", "all", "catch", "message", "reject", "_", "d", "getDefaults", "Dt", "Zt", "Gt", "Ht", "Nt", "jt", "Ft", "Qt", "Marked", "marked"], "sources": ["C:/Users/<USER>/PycharmProjects/GenAI/Borys-Slavic-News-Chatbot/frontend/node_modules/marked/lib/marked.esm.js"], "sourcesContent": ["/**\n * marked v16.1.1 - a markdown parser\n * Copyright (c) 2011-2025, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\nfunction L(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var O=L();function H(l){O=l}var E={exec:()=>null};function h(l,e=\"\"){let t=typeof l==\"string\"?l:l.source,n={replace:(r,i)=>{let s=typeof i==\"string\"?i:i.source;return s=s.replace(m.caret,\"$1\"),t=t.replace(r,s),n},getRegex:()=>new RegExp(t,e)};return n}var m={codeRemoveIndent:/^(?: {1,4}| {0,3}\\t)/gm,outputLinkReplace:/\\\\([\\[\\]])/g,indentCodeCompensation:/^(\\s+)(?:```)/,beginningSpace:/^\\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\\n/g,tabCharGlobal:/\\t/g,multipleSpaceGlobal:/\\s+/g,blankLine:/^[ \\t]*$/,doubleBlankLine:/\\n[ \\t]*\\n[ \\t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \\t]?/gm,listReplaceTabs:/^\\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\\[[ xX]\\] /,listReplaceTask:/^\\[[ xX]\\] +/,anyLine:/\\n.*\\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\\||\\| *$/g,tableRowBlankLine:/\\n[ \\t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\\s|>)/i,endPreScriptTag:/^<\\/(pre|code|kbd|script)(\\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,unicodeAlphaNumeric:/[\\p{L}\\p{N}]/u,escapeTest:/[&<>\"']/,escapeReplace:/[&<>\"']/g,escapeTestNoEncode:/[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,escapeReplaceNoEncode:/[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,unescapeTest:/&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,caret:/(^|[^\\[])\\^/g,percentDecode:/%25/g,findPipe:/\\|/g,splitPipe:/ \\|/,slashPipe:/\\\\\\|/g,carriageReturn:/\\r\\n|\\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\\S*/,endingNewline:/\\n$/,listItemRegex:l=>new RegExp(`^( {0,3}${l})((?:[\t ][^\\\\n]*)?(?:\\\\n|$))`),nextBulletRegex:l=>new RegExp(`^ {0,${Math.min(3,l-1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \t][^\\\\n]*)?(?:\\\\n|$))`),hrRegex:l=>new RegExp(`^ {0,${Math.min(3,l-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),fencesBeginRegex:l=>new RegExp(`^ {0,${Math.min(3,l-1)}}(?:\\`\\`\\`|~~~)`),headingBeginRegex:l=>new RegExp(`^ {0,${Math.min(3,l-1)}}#`),htmlBeginRegex:l=>new RegExp(`^ {0,${Math.min(3,l-1)}}<(?:[a-z].*>|!--)`,\"i\")},xe=/^(?:[ \\t]*(?:\\n|$))+/,be=/^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/,Re=/^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/,C=/^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/,Oe=/^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/,j=/(?:[*+-]|\\d{1,9}[.)])/,se=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,ie=h(se).replace(/bull/g,j).replace(/blockCode/g,/(?: {4}| {0,3}\\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\\n>]+>\\n/).replace(/\\|table/g,\"\").getRegex(),Te=h(se).replace(/bull/g,j).replace(/blockCode/g,/(?: {4}| {0,3}\\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\\n>]+>\\n/).replace(/table/g,/ {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/).getRegex(),F=/^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/,we=/^[^\\n]+/,Q=/(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/,ye=h(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/).replace(\"label\",Q).replace(\"title\",/(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/).getRegex(),Pe=h(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/).replace(/bull/g,j).getRegex(),v=\"address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul\",U=/<!--(?:-?>|[\\s\\S]*?(?:-->|$))/,Se=h(\"^ {0,3}(?:<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)|comment[^\\\\n]*(\\\\n+|$)|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$))\",\"i\").replace(\"comment\",U).replace(\"tag\",v).replace(\"attribute\",/ +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/).getRegex(),oe=h(F).replace(\"hr\",C).replace(\"heading\",\" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\",\"\").replace(\"|table\",\"\").replace(\"blockquote\",\" {0,3}>\").replace(\"fences\",\" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\",\" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\",\"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\",v).getRegex(),$e=h(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/).replace(\"paragraph\",oe).getRegex(),K={blockquote:$e,code:be,def:ye,fences:Re,heading:Oe,hr:C,html:Se,lheading:ie,list:Pe,newline:xe,paragraph:oe,table:E,text:we},re=h(\"^ *([^\\\\n ].*)\\\\n {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)\").replace(\"hr\",C).replace(\"heading\",\" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"blockquote\",\" {0,3}>\").replace(\"code\",\"(?: {4}| {0,3}\t)[^\\\\n]\").replace(\"fences\",\" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\",\" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\",\"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\",v).getRegex(),_e={...K,lheading:Te,table:re,paragraph:h(F).replace(\"hr\",C).replace(\"heading\",\" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\",\"\").replace(\"table\",re).replace(\"blockquote\",\" {0,3}>\").replace(\"fences\",\" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\",\" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\",\"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\",v).getRegex()},Le={...K,html:h(`^ *(?:comment *(?:\\\\n|\\\\s*$)|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\\\s[^'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))`).replace(\"comment\",U).replace(/tag/g,\"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b\").getRegex(),def:/^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,heading:/^(#{1,6})(.*)(?:\\n+|$)/,fences:E,lheading:/^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,paragraph:h(F).replace(\"hr\",C).replace(\"heading\",` *#{1,6} *[^\n]`).replace(\"lheading\",ie).replace(\"|table\",\"\").replace(\"blockquote\",\" {0,3}>\").replace(\"|fences\",\"\").replace(\"|list\",\"\").replace(\"|html\",\"\").replace(\"|tag\",\"\").getRegex()},Me=/^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/,ze=/^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/,ae=/^( {2,}|\\\\)\\n(?!\\s*$)/,Ae=/^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/,D=/[\\p{P}\\p{S}]/u,X=/[\\s\\p{P}\\p{S}]/u,le=/[^\\s\\p{P}\\p{S}]/u,Ee=h(/^((?![*_])punctSpace)/,\"u\").replace(/punctSpace/g,X).getRegex(),ue=/(?!~)[\\p{P}\\p{S}]/u,Ce=/(?!~)[\\s\\p{P}\\p{S}]/u,Ie=/(?:[^\\s\\p{P}\\p{S}]|~)/u,Be=/\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<(?! )[^<>]*?>/g,pe=/^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/,qe=h(pe,\"u\").replace(/punct/g,D).getRegex(),ve=h(pe,\"u\").replace(/punct/g,ue).getRegex(),ce=\"^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)|notPunctSpace(\\\\*+)(?=notPunctSpace)\",De=h(ce,\"gu\").replace(/notPunctSpace/g,le).replace(/punctSpace/g,X).replace(/punct/g,D).getRegex(),Ze=h(ce,\"gu\").replace(/notPunctSpace/g,Ie).replace(/punctSpace/g,Ce).replace(/punct/g,ue).getRegex(),Ge=h(\"^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)\",\"gu\").replace(/notPunctSpace/g,le).replace(/punctSpace/g,X).replace(/punct/g,D).getRegex(),He=h(/\\\\(punct)/,\"gu\").replace(/punct/g,D).getRegex(),Ne=h(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/).replace(\"scheme\",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace(\"email\",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),je=h(U).replace(\"(?:-->|$)\",\"-->\").getRegex(),Fe=h(\"^comment|^</[a-zA-Z][\\\\w:-]*\\\\s*>|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>|^<\\\\?[\\\\s\\\\S]*?\\\\?>|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>\").replace(\"comment\",je).replace(\"attribute\",/\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/).getRegex(),q=/(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/,Qe=h(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/).replace(\"label\",q).replace(\"href\",/<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/).replace(\"title\",/\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/).getRegex(),he=h(/^!?\\[(label)\\]\\[(ref)\\]/).replace(\"label\",q).replace(\"ref\",Q).getRegex(),de=h(/^!?\\[(ref)\\](?:\\[\\])?/).replace(\"ref\",Q).getRegex(),Ue=h(\"reflink|nolink(?!\\\\()\",\"g\").replace(\"reflink\",he).replace(\"nolink\",de).getRegex(),W={_backpedal:E,anyPunctuation:He,autolink:Ne,blockSkip:Be,br:ae,code:ze,del:E,emStrongLDelim:qe,emStrongRDelimAst:De,emStrongRDelimUnd:Ge,escape:Me,link:Qe,nolink:de,punctuation:Ee,reflink:he,reflinkSearch:Ue,tag:Fe,text:Ae,url:E},Ke={...W,link:h(/^!?\\[(label)\\]\\((.*?)\\)/).replace(\"label\",q).getRegex(),reflink:h(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/).replace(\"label\",q).getRegex()},N={...W,emStrongRDelimAst:Ze,emStrongLDelim:ve,url:h(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/,\"i\").replace(\"email\",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,del:/^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/},Xe={...N,br:h(ae).replace(\"{2,}\",\"*\").getRegex(),text:h(N.text).replace(\"\\\\b_\",\"\\\\b_| {2,}\\\\n\").replace(/\\{2,\\}/g,\"*\").getRegex()},I={normal:K,gfm:_e,pedantic:Le},M={normal:W,gfm:N,breaks:Xe,pedantic:Ke};var We={\"&\":\"&amp;\",\"<\":\"&lt;\",\">\":\"&gt;\",'\"':\"&quot;\",\"'\":\"&#39;\"},ke=l=>We[l];function w(l,e){if(e){if(m.escapeTest.test(l))return l.replace(m.escapeReplace,ke)}else if(m.escapeTestNoEncode.test(l))return l.replace(m.escapeReplaceNoEncode,ke);return l}function J(l){try{l=encodeURI(l).replace(m.percentDecode,\"%\")}catch{return null}return l}function V(l,e){let t=l.replace(m.findPipe,(i,s,o)=>{let a=!1,u=s;for(;--u>=0&&o[u]===\"\\\\\";)a=!a;return a?\"|\":\" |\"}),n=t.split(m.splitPipe),r=0;if(n[0].trim()||n.shift(),n.length>0&&!n.at(-1)?.trim()&&n.pop(),e)if(n.length>e)n.splice(e);else for(;n.length<e;)n.push(\"\");for(;r<n.length;r++)n[r]=n[r].trim().replace(m.slashPipe,\"|\");return n}function z(l,e,t){let n=l.length;if(n===0)return\"\";let r=0;for(;r<n;){let i=l.charAt(n-r-1);if(i===e&&!t)r++;else if(i!==e&&t)r++;else break}return l.slice(0,n-r)}function ge(l,e){if(l.indexOf(e[1])===-1)return-1;let t=0;for(let n=0;n<l.length;n++)if(l[n]===\"\\\\\")n++;else if(l[n]===e[0])t++;else if(l[n]===e[1]&&(t--,t<0))return n;return t>0?-2:-1}function fe(l,e,t,n,r){let i=e.href,s=e.title||null,o=l[1].replace(r.other.outputLinkReplace,\"$1\");n.state.inLink=!0;let a={type:l[0].charAt(0)===\"!\"?\"image\":\"link\",raw:t,href:i,title:s,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,a}function Je(l,e,t){let n=l.match(t.other.indentCodeCompensation);if(n===null)return e;let r=n[1];return e.split(`\n`).map(i=>{let s=i.match(t.other.beginningSpace);if(s===null)return i;let[o]=s;return o.length>=r.length?i.slice(r.length):i}).join(`\n`)}var y=class{options;rules;lexer;constructor(e){this.options=e||O}space(e){let t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:\"space\",raw:t[0]}}code(e){let t=this.rules.block.code.exec(e);if(t){let n=t[0].replace(this.rules.other.codeRemoveIndent,\"\");return{type:\"code\",raw:t[0],codeBlockStyle:\"indented\",text:this.options.pedantic?n:z(n,`\n`)}}}fences(e){let t=this.rules.block.fences.exec(e);if(t){let n=t[0],r=Je(n,t[3]||\"\",this.rules);return{type:\"code\",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,\"$1\"):t[2],text:r}}}heading(e){let t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(this.rules.other.endingHash.test(n)){let r=z(n,\"#\");(this.options.pedantic||!r||this.rules.other.endingSpaceChar.test(r))&&(n=r.trim())}return{type:\"heading\",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){let t=this.rules.block.hr.exec(e);if(t)return{type:\"hr\",raw:z(t[0],`\n`)}}blockquote(e){let t=this.rules.block.blockquote.exec(e);if(t){let n=z(t[0],`\n`).split(`\n`),r=\"\",i=\"\",s=[];for(;n.length>0;){let o=!1,a=[],u;for(u=0;u<n.length;u++)if(this.rules.other.blockquoteStart.test(n[u]))a.push(n[u]),o=!0;else if(!o)a.push(n[u]);else break;n=n.slice(u);let p=a.join(`\n`),c=p.replace(this.rules.other.blockquoteSetextReplace,`\n    $1`).replace(this.rules.other.blockquoteSetextReplace2,\"\");r=r?`${r}\n${p}`:p,i=i?`${i}\n${c}`:c;let f=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(c,s,!0),this.lexer.state.top=f,n.length===0)break;let k=s.at(-1);if(k?.type===\"code\")break;if(k?.type===\"blockquote\"){let x=k,g=x.raw+`\n`+n.join(`\n`),T=this.blockquote(g);s[s.length-1]=T,r=r.substring(0,r.length-x.raw.length)+T.raw,i=i.substring(0,i.length-x.text.length)+T.text;break}else if(k?.type===\"list\"){let x=k,g=x.raw+`\n`+n.join(`\n`),T=this.list(g);s[s.length-1]=T,r=r.substring(0,r.length-k.raw.length)+T.raw,i=i.substring(0,i.length-x.raw.length)+T.raw,n=g.substring(s.at(-1).raw.length).split(`\n`);continue}}return{type:\"blockquote\",raw:r,tokens:s,text:i}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim(),r=n.length>1,i={type:\"list\",raw:\"\",ordered:r,start:r?+n.slice(0,-1):\"\",loose:!1,items:[]};n=r?`\\\\d{1,9}\\\\${n.slice(-1)}`:`\\\\${n}`,this.options.pedantic&&(n=r?n:\"[*+-]\");let s=this.rules.other.listItemRegex(n),o=!1;for(;e;){let u=!1,p=\"\",c=\"\";if(!(t=s.exec(e))||this.rules.block.hr.test(e))break;p=t[0],e=e.substring(p.length);let f=t[2].split(`\n`,1)[0].replace(this.rules.other.listReplaceTabs,Z=>\" \".repeat(3*Z.length)),k=e.split(`\n`,1)[0],x=!f.trim(),g=0;if(this.options.pedantic?(g=2,c=f.trimStart()):x?g=t[1].length+1:(g=t[2].search(this.rules.other.nonSpaceChar),g=g>4?1:g,c=f.slice(g),g+=t[1].length),x&&this.rules.other.blankLine.test(k)&&(p+=k+`\n`,e=e.substring(k.length+1),u=!0),!u){let Z=this.rules.other.nextBulletRegex(g),ee=this.rules.other.hrRegex(g),te=this.rules.other.fencesBeginRegex(g),ne=this.rules.other.headingBeginRegex(g),me=this.rules.other.htmlBeginRegex(g);for(;e;){let G=e.split(`\n`,1)[0],A;if(k=G,this.options.pedantic?(k=k.replace(this.rules.other.listReplaceNesting,\"  \"),A=k):A=k.replace(this.rules.other.tabCharGlobal,\"    \"),te.test(k)||ne.test(k)||me.test(k)||Z.test(k)||ee.test(k))break;if(A.search(this.rules.other.nonSpaceChar)>=g||!k.trim())c+=`\n`+A.slice(g);else{if(x||f.replace(this.rules.other.tabCharGlobal,\"    \").search(this.rules.other.nonSpaceChar)>=4||te.test(f)||ne.test(f)||ee.test(f))break;c+=`\n`+k}!x&&!k.trim()&&(x=!0),p+=G+`\n`,e=e.substring(G.length+1),f=A.slice(g)}}i.loose||(o?i.loose=!0:this.rules.other.doubleBlankLine.test(p)&&(o=!0));let T=null,Y;this.options.gfm&&(T=this.rules.other.listIsTask.exec(c),T&&(Y=T[0]!==\"[ ] \",c=c.replace(this.rules.other.listReplaceTask,\"\"))),i.items.push({type:\"list_item\",raw:p,task:!!T,checked:Y,loose:!1,text:c,tokens:[]}),i.raw+=p}let a=i.items.at(-1);if(a)a.raw=a.raw.trimEnd(),a.text=a.text.trimEnd();else return;i.raw=i.raw.trimEnd();for(let u=0;u<i.items.length;u++)if(this.lexer.state.top=!1,i.items[u].tokens=this.lexer.blockTokens(i.items[u].text,[]),!i.loose){let p=i.items[u].tokens.filter(f=>f.type===\"space\"),c=p.length>0&&p.some(f=>this.rules.other.anyLine.test(f.raw));i.loose=c}if(i.loose)for(let u=0;u<i.items.length;u++)i.items[u].loose=!0;return i}}html(e){let t=this.rules.block.html.exec(e);if(t)return{type:\"html\",block:!0,raw:t[0],pre:t[1]===\"pre\"||t[1]===\"script\"||t[1]===\"style\",text:t[0]}}def(e){let t=this.rules.block.def.exec(e);if(t){let n=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal,\" \"),r=t[2]?t[2].replace(this.rules.other.hrefBrackets,\"$1\").replace(this.rules.inline.anyPunctuation,\"$1\"):\"\",i=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,\"$1\"):t[3];return{type:\"def\",tag:n,raw:t[0],href:r,title:i}}}table(e){let t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;let n=V(t[1]),r=t[2].replace(this.rules.other.tableAlignChars,\"\").split(\"|\"),i=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,\"\").split(`\n`):[],s={type:\"table\",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(let o of r)this.rules.other.tableAlignRight.test(o)?s.align.push(\"right\"):this.rules.other.tableAlignCenter.test(o)?s.align.push(\"center\"):this.rules.other.tableAlignLeft.test(o)?s.align.push(\"left\"):s.align.push(null);for(let o=0;o<n.length;o++)s.header.push({text:n[o],tokens:this.lexer.inline(n[o]),header:!0,align:s.align[o]});for(let o of i)s.rows.push(V(o,s.header.length).map((a,u)=>({text:a,tokens:this.lexer.inline(a),header:!1,align:s.align[u]})));return s}}lheading(e){let t=this.rules.block.lheading.exec(e);if(t)return{type:\"heading\",raw:t[0],depth:t[2].charAt(0)===\"=\"?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){let t=this.rules.block.paragraph.exec(e);if(t){let n=t[1].charAt(t[1].length-1)===`\n`?t[1].slice(0,-1):t[1];return{type:\"paragraph\",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){let t=this.rules.block.text.exec(e);if(t)return{type:\"text\",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){let t=this.rules.inline.escape.exec(e);if(t)return{type:\"escape\",raw:t[0],text:t[1]}}tag(e){let t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:\"html\",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){let t=this.rules.inline.link.exec(e);if(t){let n=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(n)){if(!this.rules.other.endAngleBracket.test(n))return;let s=z(n.slice(0,-1),\"\\\\\");if((n.length-s.length)%2===0)return}else{let s=ge(t[2],\"()\");if(s===-2)return;if(s>-1){let a=(t[0].indexOf(\"!\")===0?5:4)+t[1].length+s;t[2]=t[2].substring(0,s),t[0]=t[0].substring(0,a).trim(),t[3]=\"\"}}let r=t[2],i=\"\";if(this.options.pedantic){let s=this.rules.other.pedanticHrefTitle.exec(r);s&&(r=s[1],i=s[3])}else i=t[3]?t[3].slice(1,-1):\"\";return r=r.trim(),this.rules.other.startAngleBracket.test(r)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(n)?r=r.slice(1):r=r.slice(1,-1)),fe(t,{href:r&&r.replace(this.rules.inline.anyPunctuation,\"$1\"),title:i&&i.replace(this.rules.inline.anyPunctuation,\"$1\")},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let r=(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal,\" \"),i=t[r.toLowerCase()];if(!i){let s=n[0].charAt(0);return{type:\"text\",raw:s,text:s}}return fe(n,i,n[0],this.lexer,this.rules)}}emStrong(e,t,n=\"\"){let r=this.rules.inline.emStrongLDelim.exec(e);if(!r||r[3]&&n.match(this.rules.other.unicodeAlphaNumeric))return;if(!(r[1]||r[2]||\"\")||!n||this.rules.inline.punctuation.exec(n)){let s=[...r[0]].length-1,o,a,u=s,p=0,c=r[0][0]===\"*\"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+s);(r=c.exec(t))!=null;){if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!o)continue;if(a=[...o].length,r[3]||r[4]){u+=a;continue}else if((r[5]||r[6])&&s%3&&!((s+a)%3)){p+=a;continue}if(u-=a,u>0)continue;a=Math.min(a,a+u+p);let f=[...r[0]][0].length,k=e.slice(0,s+r.index+f+a);if(Math.min(s,a)%2){let g=k.slice(1,-1);return{type:\"em\",raw:k,text:g,tokens:this.lexer.inlineTokens(g)}}let x=k.slice(2,-2);return{type:\"strong\",raw:k,text:x,tokens:this.lexer.inlineTokens(x)}}}}codespan(e){let t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(this.rules.other.newLineCharGlobal,\" \"),r=this.rules.other.nonSpaceChar.test(n),i=this.rules.other.startingSpaceChar.test(n)&&this.rules.other.endingSpaceChar.test(n);return r&&i&&(n=n.substring(1,n.length-1)),{type:\"codespan\",raw:t[0],text:n}}}br(e){let t=this.rules.inline.br.exec(e);if(t)return{type:\"br\",raw:t[0]}}del(e){let t=this.rules.inline.del.exec(e);if(t)return{type:\"del\",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){let t=this.rules.inline.autolink.exec(e);if(t){let n,r;return t[2]===\"@\"?(n=t[1],r=\"mailto:\"+n):(n=t[1],r=n),{type:\"link\",raw:t[0],text:n,href:r,tokens:[{type:\"text\",raw:n,text:n}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let n,r;if(t[2]===\"@\")n=t[0],r=\"mailto:\"+n;else{let i;do i=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??\"\";while(i!==t[0]);n=t[0],t[1]===\"www.\"?r=\"http://\"+t[0]:r=t[0]}return{type:\"link\",raw:t[0],text:n,href:r,tokens:[{type:\"text\",raw:n,text:n}]}}}inlineText(e){let t=this.rules.inline.text.exec(e);if(t){let n=this.lexer.state.inRawBlock;return{type:\"text\",raw:t[0],text:t[0],escaped:n}}}};var b=class l{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||O,this.options.tokenizer=this.options.tokenizer||new y,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let t={other:m,block:I.normal,inline:M.normal};this.options.pedantic?(t.block=I.pedantic,t.inline=M.pedantic):this.options.gfm&&(t.block=I.gfm,this.options.breaks?t.inline=M.breaks:t.inline=M.gfm),this.tokenizer.rules=t}static get rules(){return{block:I,inline:M}}static lex(e,t){return new l(t).lex(e)}static lexInline(e,t){return new l(t).inlineTokens(e)}lex(e){e=e.replace(m.carriageReturn,`\n`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){let n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){for(this.options.pedantic&&(e=e.replace(m.tabCharGlobal,\"    \").replace(m.spaceLine,\"\"));e;){let r;if(this.options.extensions?.block?.some(s=>(r=s.call({lexer:this},e,t))?(e=e.substring(r.raw.length),t.push(r),!0):!1))continue;if(r=this.tokenizer.space(e)){e=e.substring(r.raw.length);let s=t.at(-1);r.raw.length===1&&s!==void 0?s.raw+=`\n`:t.push(r);continue}if(r=this.tokenizer.code(e)){e=e.substring(r.raw.length);let s=t.at(-1);s?.type===\"paragraph\"||s?.type===\"text\"?(s.raw+=`\n`+r.raw,s.text+=`\n`+r.text,this.inlineQueue.at(-1).src=s.text):t.push(r);continue}if(r=this.tokenizer.fences(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.heading(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.hr(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.blockquote(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.list(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.html(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.def(e)){e=e.substring(r.raw.length);let s=t.at(-1);s?.type===\"paragraph\"||s?.type===\"text\"?(s.raw+=`\n`+r.raw,s.text+=`\n`+r.raw,this.inlineQueue.at(-1).src=s.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.lheading(e)){e=e.substring(r.raw.length),t.push(r);continue}let i=e;if(this.options.extensions?.startBlock){let s=1/0,o=e.slice(1),a;this.options.extensions.startBlock.forEach(u=>{a=u.call({lexer:this},o),typeof a==\"number\"&&a>=0&&(s=Math.min(s,a))}),s<1/0&&s>=0&&(i=e.substring(0,s+1))}if(this.state.top&&(r=this.tokenizer.paragraph(i))){let s=t.at(-1);n&&s?.type===\"paragraph\"?(s.raw+=`\n`+r.raw,s.text+=`\n`+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=s.text):t.push(r),n=i.length!==e.length,e=e.substring(r.raw.length);continue}if(r=this.tokenizer.text(e)){e=e.substring(r.raw.length);let s=t.at(-1);s?.type===\"text\"?(s.raw+=`\n`+r.raw,s.text+=`\n`+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=s.text):t.push(r);continue}if(e){let s=\"Infinite loop on byte: \"+e.charCodeAt(0);if(this.options.silent){console.error(s);break}else throw new Error(s)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n=e,r=null;if(this.tokens.links){let o=Object.keys(this.tokens.links);if(o.length>0)for(;(r=this.tokenizer.rules.inline.reflinkSearch.exec(n))!=null;)o.includes(r[0].slice(r[0].lastIndexOf(\"[\")+1,-1))&&(n=n.slice(0,r.index)+\"[\"+\"a\".repeat(r[0].length-2)+\"]\"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(r=this.tokenizer.rules.inline.anyPunctuation.exec(n))!=null;)n=n.slice(0,r.index)+\"++\"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(r=this.tokenizer.rules.inline.blockSkip.exec(n))!=null;)n=n.slice(0,r.index)+\"[\"+\"a\".repeat(r[0].length-2)+\"]\"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let i=!1,s=\"\";for(;e;){i||(s=\"\"),i=!1;let o;if(this.options.extensions?.inline?.some(u=>(o=u.call({lexer:this},e,t))?(e=e.substring(o.raw.length),t.push(o),!0):!1))continue;if(o=this.tokenizer.escape(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.tag(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.link(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(o.raw.length);let u=t.at(-1);o.type===\"text\"&&u?.type===\"text\"?(u.raw+=o.raw,u.text+=o.text):t.push(o);continue}if(o=this.tokenizer.emStrong(e,n,s)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.codespan(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.br(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.del(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.autolink(e)){e=e.substring(o.raw.length),t.push(o);continue}if(!this.state.inLink&&(o=this.tokenizer.url(e))){e=e.substring(o.raw.length),t.push(o);continue}let a=e;if(this.options.extensions?.startInline){let u=1/0,p=e.slice(1),c;this.options.extensions.startInline.forEach(f=>{c=f.call({lexer:this},p),typeof c==\"number\"&&c>=0&&(u=Math.min(u,c))}),u<1/0&&u>=0&&(a=e.substring(0,u+1))}if(o=this.tokenizer.inlineText(a)){e=e.substring(o.raw.length),o.raw.slice(-1)!==\"_\"&&(s=o.raw.slice(-1)),i=!0;let u=t.at(-1);u?.type===\"text\"?(u.raw+=o.raw,u.text+=o.text):t.push(o);continue}if(e){let u=\"Infinite loop on byte: \"+e.charCodeAt(0);if(this.options.silent){console.error(u);break}else throw new Error(u)}}return t}};var P=class{options;parser;constructor(e){this.options=e||O}space(e){return\"\"}code({text:e,lang:t,escaped:n}){let r=(t||\"\").match(m.notSpaceStart)?.[0],i=e.replace(m.endingNewline,\"\")+`\n`;return r?'<pre><code class=\"language-'+w(r)+'\">'+(n?i:w(i,!0))+`</code></pre>\n`:\"<pre><code>\"+(n?i:w(i,!0))+`</code></pre>\n`}blockquote({tokens:e}){return`<blockquote>\n${this.parser.parse(e)}</blockquote>\n`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>\n`}hr(e){return`<hr>\n`}list(e){let t=e.ordered,n=e.start,r=\"\";for(let o=0;o<e.items.length;o++){let a=e.items[o];r+=this.listitem(a)}let i=t?\"ol\":\"ul\",s=t&&n!==1?' start=\"'+n+'\"':\"\";return\"<\"+i+s+`>\n`+r+\"</\"+i+`>\n`}listitem(e){let t=\"\";if(e.task){let n=this.checkbox({checked:!!e.checked});e.loose?e.tokens[0]?.type===\"paragraph\"?(e.tokens[0].text=n+\" \"+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type===\"text\"&&(e.tokens[0].tokens[0].text=n+\" \"+w(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:\"text\",raw:n+\" \",text:n+\" \",escaped:!0}):t+=n+\" \"}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>\n`}checkbox({checked:e}){return\"<input \"+(e?'checked=\"\" ':\"\")+'disabled=\"\" type=\"checkbox\">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>\n`}table(e){let t=\"\",n=\"\";for(let i=0;i<e.header.length;i++)n+=this.tablecell(e.header[i]);t+=this.tablerow({text:n});let r=\"\";for(let i=0;i<e.rows.length;i++){let s=e.rows[i];n=\"\";for(let o=0;o<s.length;o++)n+=this.tablecell(s[o]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),`<table>\n<thead>\n`+t+`</thead>\n`+r+`</table>\n`}tablerow({text:e}){return`<tr>\n${e}</tr>\n`}tablecell(e){let t=this.parser.parseInline(e.tokens),n=e.header?\"th\":\"td\";return(e.align?`<${n} align=\"${e.align}\">`:`<${n}>`)+t+`</${n}>\n`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${w(e,!0)}</code>`}br(e){return\"<br>\"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){let r=this.parser.parseInline(n),i=J(e);if(i===null)return r;e=i;let s='<a href=\"'+e+'\"';return t&&(s+=' title=\"'+w(t)+'\"'),s+=\">\"+r+\"</a>\",s}image({href:e,title:t,text:n,tokens:r}){r&&(n=this.parser.parseInline(r,this.parser.textRenderer));let i=J(e);if(i===null)return w(n);e=i;let s=`<img src=\"${e}\" alt=\"${n}\"`;return t&&(s+=` title=\"${w(t)}\"`),s+=\">\",s}text(e){return\"tokens\"in e&&e.tokens?this.parser.parseInline(e.tokens):\"escaped\"in e&&e.escaped?e.text:w(e.text)}};var S=class{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return\"\"+e}image({text:e}){return\"\"+e}br(){return\"\"}};var R=class l{options;renderer;textRenderer;constructor(e){this.options=e||O,this.options.renderer=this.options.renderer||new P,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new S}static parse(e,t){return new l(t).parse(e)}static parseInline(e,t){return new l(t).parseInline(e)}parse(e,t=!0){let n=\"\";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let o=i,a=this.options.extensions.renderers[o.type].call({parser:this},o);if(a!==!1||![\"space\",\"hr\",\"heading\",\"code\",\"table\",\"blockquote\",\"list\",\"html\",\"paragraph\",\"text\"].includes(o.type)){n+=a||\"\";continue}}let s=i;switch(s.type){case\"space\":{n+=this.renderer.space(s);continue}case\"hr\":{n+=this.renderer.hr(s);continue}case\"heading\":{n+=this.renderer.heading(s);continue}case\"code\":{n+=this.renderer.code(s);continue}case\"table\":{n+=this.renderer.table(s);continue}case\"blockquote\":{n+=this.renderer.blockquote(s);continue}case\"list\":{n+=this.renderer.list(s);continue}case\"html\":{n+=this.renderer.html(s);continue}case\"paragraph\":{n+=this.renderer.paragraph(s);continue}case\"text\":{let o=s,a=this.renderer.text(o);for(;r+1<e.length&&e[r+1].type===\"text\";)o=e[++r],a+=`\n`+this.renderer.text(o);t?n+=this.renderer.paragraph({type:\"paragraph\",raw:a,text:a,tokens:[{type:\"text\",raw:a,text:a,escaped:!0}]}):n+=a;continue}default:{let o='Token with \"'+s.type+'\" type was not found.';if(this.options.silent)return console.error(o),\"\";throw new Error(o)}}}return n}parseInline(e,t=this.renderer){let n=\"\";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let o=this.options.extensions.renderers[i.type].call({parser:this},i);if(o!==!1||![\"escape\",\"html\",\"link\",\"image\",\"strong\",\"em\",\"codespan\",\"br\",\"del\",\"text\"].includes(i.type)){n+=o||\"\";continue}}let s=i;switch(s.type){case\"escape\":{n+=t.text(s);break}case\"html\":{n+=t.html(s);break}case\"link\":{n+=t.link(s);break}case\"image\":{n+=t.image(s);break}case\"strong\":{n+=t.strong(s);break}case\"em\":{n+=t.em(s);break}case\"codespan\":{n+=t.codespan(s);break}case\"br\":{n+=t.br(s);break}case\"del\":{n+=t.del(s);break}case\"text\":{n+=t.text(s);break}default:{let o='Token with \"'+s.type+'\" type was not found.';if(this.options.silent)return console.error(o),\"\";throw new Error(o)}}}return n}};var $=class{options;block;constructor(e){this.options=e||O}static passThroughHooks=new Set([\"preprocess\",\"postprocess\",\"processAllTokens\"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?b.lex:b.lexInline}provideParser(){return this.block?R.parse:R.parseInline}};var B=class{defaults=L();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=R;Renderer=P;TextRenderer=S;Lexer=b;Tokenizer=y;Hooks=$;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(let r of e)switch(n=n.concat(t.call(this,r)),r.type){case\"table\":{let i=r;for(let s of i.header)n=n.concat(this.walkTokens(s.tokens,t));for(let s of i.rows)for(let o of s)n=n.concat(this.walkTokens(o.tokens,t));break}case\"list\":{let i=r;n=n.concat(this.walkTokens(i.items,t));break}default:{let i=r;this.defaults.extensions?.childTokens?.[i.type]?this.defaults.extensions.childTokens[i.type].forEach(s=>{let o=i[s].flat(1/0);n=n.concat(this.walkTokens(o,t))}):i.tokens&&(n=n.concat(this.walkTokens(i.tokens,t)))}}return n}use(...e){let t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(n=>{let r={...n};if(r.async=this.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(i=>{if(!i.name)throw new Error(\"extension name required\");if(\"renderer\"in i){let s=t.renderers[i.name];s?t.renderers[i.name]=function(...o){let a=i.renderer.apply(this,o);return a===!1&&(a=s.apply(this,o)),a}:t.renderers[i.name]=i.renderer}if(\"tokenizer\"in i){if(!i.level||i.level!==\"block\"&&i.level!==\"inline\")throw new Error(\"extension level must be 'block' or 'inline'\");let s=t[i.level];s?s.unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level===\"block\"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level===\"inline\"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}\"childTokens\"in i&&i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),r.extensions=t),n.renderer){let i=this.defaults.renderer||new P(this.defaults);for(let s in n.renderer){if(!(s in i))throw new Error(`renderer '${s}' does not exist`);if([\"options\",\"parser\"].includes(s))continue;let o=s,a=n.renderer[o],u=i[o];i[o]=(...p)=>{let c=a.apply(i,p);return c===!1&&(c=u.apply(i,p)),c||\"\"}}r.renderer=i}if(n.tokenizer){let i=this.defaults.tokenizer||new y(this.defaults);for(let s in n.tokenizer){if(!(s in i))throw new Error(`tokenizer '${s}' does not exist`);if([\"options\",\"rules\",\"lexer\"].includes(s))continue;let o=s,a=n.tokenizer[o],u=i[o];i[o]=(...p)=>{let c=a.apply(i,p);return c===!1&&(c=u.apply(i,p)),c}}r.tokenizer=i}if(n.hooks){let i=this.defaults.hooks||new $;for(let s in n.hooks){if(!(s in i))throw new Error(`hook '${s}' does not exist`);if([\"options\",\"block\"].includes(s))continue;let o=s,a=n.hooks[o],u=i[o];$.passThroughHooks.has(s)?i[o]=p=>{if(this.defaults.async)return Promise.resolve(a.call(i,p)).then(f=>u.call(i,f));let c=a.call(i,p);return u.call(i,c)}:i[o]=(...p)=>{let c=a.apply(i,p);return c===!1&&(c=u.apply(i,p)),c}}r.hooks=i}if(n.walkTokens){let i=this.defaults.walkTokens,s=n.walkTokens;r.walkTokens=function(o){let a=[];return a.push(s.call(this,o)),i&&(a=a.concat(i.call(this,o))),a}}this.defaults={...this.defaults,...r}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return b.lex(e,t??this.defaults)}parser(e,t){return R.parse(e,t??this.defaults)}parseMarkdown(e){return(n,r)=>{let i={...r},s={...this.defaults,...i},o=this.onError(!!s.silent,!!s.async);if(this.defaults.async===!0&&i.async===!1)return o(new Error(\"marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.\"));if(typeof n>\"u\"||n===null)return o(new Error(\"marked(): input parameter is undefined or null\"));if(typeof n!=\"string\")return o(new Error(\"marked(): input parameter is of type \"+Object.prototype.toString.call(n)+\", string expected\"));s.hooks&&(s.hooks.options=s,s.hooks.block=e);let a=s.hooks?s.hooks.provideLexer():e?b.lex:b.lexInline,u=s.hooks?s.hooks.provideParser():e?R.parse:R.parseInline;if(s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(n):n).then(p=>a(p,s)).then(p=>s.hooks?s.hooks.processAllTokens(p):p).then(p=>s.walkTokens?Promise.all(this.walkTokens(p,s.walkTokens)).then(()=>p):p).then(p=>u(p,s)).then(p=>s.hooks?s.hooks.postprocess(p):p).catch(o);try{s.hooks&&(n=s.hooks.preprocess(n));let p=a(n,s);s.hooks&&(p=s.hooks.processAllTokens(p)),s.walkTokens&&this.walkTokens(p,s.walkTokens);let c=u(p,s);return s.hooks&&(c=s.hooks.postprocess(c)),c}catch(p){return o(p)}}}onError(e,t){return n=>{if(n.message+=`\nPlease report this to https://github.com/markedjs/marked.`,e){let r=\"<p>An error occurred:</p><pre>\"+w(n.message+\"\",!0)+\"</pre>\";return t?Promise.resolve(r):r}if(t)return Promise.reject(n);throw n}}};var _=new B;function d(l,e){return _.parse(l,e)}d.options=d.setOptions=function(l){return _.setOptions(l),d.defaults=_.defaults,H(d.defaults),d};d.getDefaults=L;d.defaults=O;d.use=function(...l){return _.use(...l),d.defaults=_.defaults,H(d.defaults),d};d.walkTokens=function(l,e){return _.walkTokens(l,e)};d.parseInline=_.parseInline;d.Parser=R;d.parser=R.parse;d.Renderer=P;d.TextRenderer=S;d.Lexer=b;d.lexer=b.lex;d.Tokenizer=y;d.Hooks=$;d.parse=d;var Dt=d.options,Zt=d.setOptions,Gt=d.use,Ht=d.walkTokens,Nt=d.parseInline,jt=d,Ft=R.parse,Qt=b.lex;export{$ as Hooks,b as Lexer,B as Marked,R as Parser,P as Renderer,S as TextRenderer,y as Tokenizer,O as defaults,L as getDefaults,Qt as lexer,d as marked,Dt as options,jt as parse,Nt as parseInline,Ft as parser,Zt as setOptions,Gt as use,Ht as walkTokens};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAM;IAACC,KAAK,EAAC,CAAC,CAAC;IAACC,MAAM,EAAC,CAAC,CAAC;IAACC,UAAU,EAAC,IAAI;IAACC,GAAG,EAAC,CAAC,CAAC;IAACC,KAAK,EAAC,IAAI;IAACC,QAAQ,EAAC,CAAC,CAAC;IAACC,QAAQ,EAAC,IAAI;IAACC,MAAM,EAAC,CAAC,CAAC;IAACC,SAAS,EAAC,IAAI;IAACC,UAAU,EAAC;EAAI,CAAC;AAAA;AAAC,IAAIC,CAAC,GAACX,CAAC,CAAC,CAAC;AAAC,SAASY,CAACA,CAACC,CAAC,EAAC;EAACF,CAAC,GAACE,CAAC;AAAA;AAAC,IAAIC,CAAC,GAAC;EAACC,IAAI,EAACA,CAAA,KAAI;AAAI,CAAC;AAAC,SAASC,CAACA,CAACH,CAAC,EAACI,CAAC,GAAC,EAAE,EAAC;EAAC,IAAIC,CAAC,GAAC,OAAOL,CAAC,IAAE,QAAQ,GAACA,CAAC,GAACA,CAAC,CAACM,MAAM;IAACC,CAAC,GAAC;MAACC,OAAO,EAACA,CAACC,CAAC,EAACC,CAAC,KAAG;QAAC,IAAIC,CAAC,GAAC,OAAOD,CAAC,IAAE,QAAQ,GAACA,CAAC,GAACA,CAAC,CAACJ,MAAM;QAAC,OAAOK,CAAC,GAACA,CAAC,CAACH,OAAO,CAACI,CAAC,CAACC,KAAK,EAAC,IAAI,CAAC,EAACR,CAAC,GAACA,CAAC,CAACG,OAAO,CAACC,CAAC,EAACE,CAAC,CAAC,EAACJ,CAAC;MAAA,CAAC;MAACO,QAAQ,EAACA,CAAA,KAAI,IAAIC,MAAM,CAACV,CAAC,EAACD,CAAC;IAAC,CAAC;EAAC,OAAOG,CAAC;AAAA;AAAC,IAAIK,CAAC,GAAC;IAACI,gBAAgB,EAAC,wBAAwB;IAACC,iBAAiB,EAAC,aAAa;IAACC,sBAAsB,EAAC,eAAe;IAACC,cAAc,EAAC,MAAM;IAACC,UAAU,EAAC,IAAI;IAACC,iBAAiB,EAAC,IAAI;IAACC,eAAe,EAAC,IAAI;IAACC,YAAY,EAAC,MAAM;IAACC,iBAAiB,EAAC,KAAK;IAACC,aAAa,EAAC,KAAK;IAACC,mBAAmB,EAAC,MAAM;IAACC,SAAS,EAAC,UAAU;IAACC,eAAe,EAAC,mBAAmB;IAACC,eAAe,EAAC,UAAU;IAACC,uBAAuB,EAAC,gCAAgC;IAACC,wBAAwB,EAAC,kBAAkB;IAACC,eAAe,EAAC,MAAM;IAACC,kBAAkB,EAAC,yBAAyB;IAACC,UAAU,EAAC,aAAa;IAACC,eAAe,EAAC,cAAc;IAACC,OAAO,EAAC,QAAQ;IAACC,YAAY,EAAC,UAAU;IAACC,cAAc,EAAC,MAAM;IAACC,eAAe,EAAC,YAAY;IAACC,iBAAiB,EAAC,WAAW;IAACC,eAAe,EAAC,WAAW;IAACC,gBAAgB,EAAC,YAAY;IAACC,cAAc,EAAC,WAAW;IAACC,SAAS,EAAC,OAAO;IAACC,OAAO,EAAC,SAAS;IAACC,iBAAiB,EAAC,gCAAgC;IAACC,eAAe,EAAC,kCAAkC;IAACC,iBAAiB,EAAC,IAAI;IAACC,eAAe,EAAC,IAAI;IAACC,iBAAiB,EAAC,+BAA+B;IAACC,mBAAmB,EAAC,eAAe;IAACC,UAAU,EAAC,SAAS;IAACC,aAAa,EAAC,UAAU;IAACC,kBAAkB,EAAC,mDAAmD;IAACC,qBAAqB,EAAC,oDAAoD;IAACC,YAAY,EAAC,4CAA4C;IAAC3C,KAAK,EAAC,cAAc;IAAC4C,aAAa,EAAC,MAAM;IAACC,QAAQ,EAAC,KAAK;IAACC,SAAS,EAAC,KAAK;IAACC,SAAS,EAAC,OAAO;IAACC,cAAc,EAAC,UAAU;IAACC,SAAS,EAAC,QAAQ;IAACC,aAAa,EAAC,MAAM;IAACC,aAAa,EAAC,KAAK;IAACC,aAAa,EAACjE,CAAC,IAAE,IAAIe,MAAM,CAAE,WAAUf,CAAE,8BAA6B,CAAC;IAACkE,eAAe,EAAClE,CAAC,IAAE,IAAIe,MAAM,CAAE,QAAOoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAACpE,CAAC,GAAC,CAAC,CAAE,oDAAmD,CAAC;IAACqE,OAAO,EAACrE,CAAC,IAAE,IAAIe,MAAM,CAAE,QAAOoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAACpE,CAAC,GAAC,CAAC,CAAE,oDAAmD,CAAC;IAACsE,gBAAgB,EAACtE,CAAC,IAAE,IAAIe,MAAM,CAAE,QAAOoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAACpE,CAAC,GAAC,CAAC,CAAE,iBAAgB,CAAC;IAACuE,iBAAiB,EAACvE,CAAC,IAAE,IAAIe,MAAM,CAAE,QAAOoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAACpE,CAAC,GAAC,CAAC,CAAE,IAAG,CAAC;IAACwE,cAAc,EAACxE,CAAC,IAAE,IAAIe,MAAM,CAAE,QAAOoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAACpE,CAAC,GAAC,CAAC,CAAE,oBAAmB,EAAC,GAAG;EAAC,CAAC;EAACyE,EAAE,GAAC,sBAAsB;EAACC,EAAE,GAAC,uDAAuD;EAACC,EAAE,GAAC,6GAA6G;EAACC,CAAC,GAAC,oEAAoE;EAACC,EAAE,GAAC,sCAAsC;EAACC,CAAC,GAAC,uBAAuB;EAACC,EAAE,GAAC,gKAAgK;EAACC,EAAE,GAAC7E,CAAC,CAAC4E,EAAE,CAAC,CAACvE,OAAO,CAAC,OAAO,EAACsE,CAAC,CAAC,CAACtE,OAAO,CAAC,YAAY,EAAC,mBAAmB,CAAC,CAACA,OAAO,CAAC,SAAS,EAAC,uBAAuB,CAAC,CAACA,OAAO,CAAC,aAAa,EAAC,SAAS,CAAC,CAACA,OAAO,CAAC,UAAU,EAAC,cAAc,CAAC,CAACA,OAAO,CAAC,OAAO,EAAC,mBAAmB,CAAC,CAACA,OAAO,CAAC,UAAU,EAAC,EAAE,CAAC,CAACM,QAAQ,CAAC,CAAC;EAACmE,EAAE,GAAC9E,CAAC,CAAC4E,EAAE,CAAC,CAACvE,OAAO,CAAC,OAAO,EAACsE,CAAC,CAAC,CAACtE,OAAO,CAAC,YAAY,EAAC,mBAAmB,CAAC,CAACA,OAAO,CAAC,SAAS,EAAC,uBAAuB,CAAC,CAACA,OAAO,CAAC,aAAa,EAAC,SAAS,CAAC,CAACA,OAAO,CAAC,UAAU,EAAC,cAAc,CAAC,CAACA,OAAO,CAAC,OAAO,EAAC,mBAAmB,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAC,mCAAmC,CAAC,CAACM,QAAQ,CAAC,CAAC;EAACoE,CAAC,GAAC,sFAAsF;EAACC,EAAE,GAAC,SAAS;EAACC,CAAC,GAAC,6BAA6B;EAACC,EAAE,GAAClF,CAAC,CAAC,6GAA6G,CAAC,CAACK,OAAO,CAAC,OAAO,EAAC4E,CAAC,CAAC,CAAC5E,OAAO,CAAC,OAAO,EAAC,8DAA8D,CAAC,CAACM,QAAQ,CAAC,CAAC;EAACwE,EAAE,GAACnF,CAAC,CAAC,sCAAsC,CAAC,CAACK,OAAO,CAAC,OAAO,EAACsE,CAAC,CAAC,CAAChE,QAAQ,CAAC,CAAC;EAACyE,CAAC,GAAC,+VAA+V;EAACC,CAAC,GAAC,+BAA+B;EAACC,EAAE,GAACtF,CAAC,CAAC,2dAA2d,EAAC,GAAG,CAAC,CAACK,OAAO,CAAC,SAAS,EAACgF,CAAC,CAAC,CAAChF,OAAO,CAAC,KAAK,EAAC+E,CAAC,CAAC,CAAC/E,OAAO,CAAC,WAAW,EAAC,0EAA0E,CAAC,CAACM,QAAQ,CAAC,CAAC;EAAC4E,EAAE,GAACvF,CAAC,CAAC+E,CAAC,CAAC,CAAC1E,OAAO,CAAC,IAAI,EAACoE,CAAC,CAAC,CAACpE,OAAO,CAAC,SAAS,EAAC,uBAAuB,CAAC,CAACA,OAAO,CAAC,WAAW,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,YAAY,EAAC,SAAS,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAC,gDAAgD,CAAC,CAACA,OAAO,CAAC,MAAM,EAAC,wBAAwB,CAAC,CAACA,OAAO,CAAC,MAAM,EAAC,6DAA6D,CAAC,CAACA,OAAO,CAAC,KAAK,EAAC+E,CAAC,CAAC,CAACzE,QAAQ,CAAC,CAAC;EAAC6E,EAAE,GAACxF,CAAC,CAAC,yCAAyC,CAAC,CAACK,OAAO,CAAC,WAAW,EAACkF,EAAE,CAAC,CAAC5E,QAAQ,CAAC,CAAC;EAAC8E,CAAC,GAAC;IAACC,UAAU,EAACF,EAAE;IAACG,IAAI,EAACpB,EAAE;IAACqB,GAAG,EAACV,EAAE;IAACW,MAAM,EAACrB,EAAE;IAACsB,OAAO,EAACpB,EAAE;IAACqB,EAAE,EAACtB,CAAC;IAACuB,IAAI,EAACV,EAAE;IAACW,QAAQ,EAACpB,EAAE;IAACqB,IAAI,EAACf,EAAE;IAACgB,OAAO,EAAC7B,EAAE;IAAC8B,SAAS,EAACb,EAAE;IAACc,KAAK,EAACvG,CAAC;IAACwG,IAAI,EAACtB;EAAE,CAAC;EAACuB,EAAE,GAACvG,CAAC,CAAC,6JAA6J,CAAC,CAACK,OAAO,CAAC,IAAI,EAACoE,CAAC,CAAC,CAACpE,OAAO,CAAC,SAAS,EAAC,uBAAuB,CAAC,CAACA,OAAO,CAAC,YAAY,EAAC,SAAS,CAAC,CAACA,OAAO,CAAC,MAAM,EAAC,wBAAwB,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAC,gDAAgD,CAAC,CAACA,OAAO,CAAC,MAAM,EAAC,wBAAwB,CAAC,CAACA,OAAO,CAAC,MAAM,EAAC,6DAA6D,CAAC,CAACA,OAAO,CAAC,KAAK,EAAC+E,CAAC,CAAC,CAACzE,QAAQ,CAAC,CAAC;EAAC6F,EAAE,GAAC;IAAC,GAAGf,CAAC;IAACQ,QAAQ,EAACnB,EAAE;IAACuB,KAAK,EAACE,EAAE;IAACH,SAAS,EAACpG,CAAC,CAAC+E,CAAC,CAAC,CAAC1E,OAAO,CAAC,IAAI,EAACoE,CAAC,CAAC,CAACpE,OAAO,CAAC,SAAS,EAAC,uBAAuB,CAAC,CAACA,OAAO,CAAC,WAAW,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAACkG,EAAE,CAAC,CAAClG,OAAO,CAAC,YAAY,EAAC,SAAS,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAC,gDAAgD,CAAC,CAACA,OAAO,CAAC,MAAM,EAAC,wBAAwB,CAAC,CAACA,OAAO,CAAC,MAAM,EAAC,6DAA6D,CAAC,CAACA,OAAO,CAAC,KAAK,EAAC+E,CAAC,CAAC,CAACzE,QAAQ,CAAC;EAAC,CAAC;EAAC8F,EAAE,GAAC;IAAC,GAAGhB,CAAC;IAACO,IAAI,EAAChG,CAAC,CAAE,wIAAuI,CAAC,CAACK,OAAO,CAAC,SAAS,EAACgF,CAAC,CAAC,CAAChF,OAAO,CAAC,MAAM,EAAC,mKAAmK,CAAC,CAACM,QAAQ,CAAC,CAAC;IAACiF,GAAG,EAAC,mEAAmE;IAACE,OAAO,EAAC,wBAAwB;IAACD,MAAM,EAAC/F,CAAC;IAACmG,QAAQ,EAAC,kCAAkC;IAACG,SAAS,EAACpG,CAAC,CAAC+E,CAAC,CAAC,CAAC1E,OAAO,CAAC,IAAI,EAACoE,CAAC,CAAC,CAACpE,OAAO,CAAC,SAAS,EAAE;AAC7wN,EAAE,CAAC,CAACA,OAAO,CAAC,UAAU,EAACwE,EAAE,CAAC,CAACxE,OAAO,CAAC,QAAQ,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,YAAY,EAAC,SAAS,CAAC,CAACA,OAAO,CAAC,SAAS,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAC,EAAE,CAAC,CAACM,QAAQ,CAAC;EAAC,CAAC;EAAC+F,EAAE,GAAC,6CAA6C;EAACC,EAAE,GAAC,qCAAqC;EAACC,EAAE,GAAC,uBAAuB;EAACC,EAAE,GAAC,6EAA6E;EAACC,CAAC,GAAC,eAAe;EAACC,CAAC,GAAC,iBAAiB;EAACC,EAAE,GAAC,kBAAkB;EAACC,EAAE,GAACjH,CAAC,CAAC,uBAAuB,EAAC,GAAG,CAAC,CAACK,OAAO,CAAC,aAAa,EAAC0G,CAAC,CAAC,CAACpG,QAAQ,CAAC,CAAC;EAACuG,EAAE,GAAC,oBAAoB;EAACC,EAAE,GAAC,sBAAsB;EAACC,EAAE,GAAC,wBAAwB;EAACC,EAAE,GAAC,oFAAoF;EAACC,EAAE,GAAC,+DAA+D;EAACC,EAAE,GAACvH,CAAC,CAACsH,EAAE,EAAC,GAAG,CAAC,CAACjH,OAAO,CAAC,QAAQ,EAACyG,CAAC,CAAC,CAACnG,QAAQ,CAAC,CAAC;EAAC6G,EAAE,GAACxH,CAAC,CAACsH,EAAE,EAAC,GAAG,CAAC,CAACjH,OAAO,CAAC,QAAQ,EAAC6G,EAAE,CAAC,CAACvG,QAAQ,CAAC,CAAC;EAAC8G,EAAE,GAAC,uQAAuQ;EAACC,EAAE,GAAC1H,CAAC,CAACyH,EAAE,EAAC,IAAI,CAAC,CAACpH,OAAO,CAAC,gBAAgB,EAAC2G,EAAE,CAAC,CAAC3G,OAAO,CAAC,aAAa,EAAC0G,CAAC,CAAC,CAAC1G,OAAO,CAAC,QAAQ,EAACyG,CAAC,CAAC,CAACnG,QAAQ,CAAC,CAAC;EAACgH,EAAE,GAAC3H,CAAC,CAACyH,EAAE,EAAC,IAAI,CAAC,CAACpH,OAAO,CAAC,gBAAgB,EAAC+G,EAAE,CAAC,CAAC/G,OAAO,CAAC,aAAa,EAAC8G,EAAE,CAAC,CAAC9G,OAAO,CAAC,QAAQ,EAAC6G,EAAE,CAAC,CAACvG,QAAQ,CAAC,CAAC;EAACiH,EAAE,GAAC5H,CAAC,CAAC,kNAAkN,EAAC,IAAI,CAAC,CAACK,OAAO,CAAC,gBAAgB,EAAC2G,EAAE,CAAC,CAAC3G,OAAO,CAAC,aAAa,EAAC0G,CAAC,CAAC,CAAC1G,OAAO,CAAC,QAAQ,EAACyG,CAAC,CAAC,CAACnG,QAAQ,CAAC,CAAC;EAACkH,EAAE,GAAC7H,CAAC,CAAC,WAAW,EAAC,IAAI,CAAC,CAACK,OAAO,CAAC,QAAQ,EAACyG,CAAC,CAAC,CAACnG,QAAQ,CAAC,CAAC;EAACmH,EAAE,GAAC9H,CAAC,CAAC,qCAAqC,CAAC,CAACK,OAAO,CAAC,QAAQ,EAAC,8BAA8B,CAAC,CAACA,OAAO,CAAC,OAAO,EAAC,8IAA8I,CAAC,CAACM,QAAQ,CAAC,CAAC;EAACoH,EAAE,GAAC/H,CAAC,CAACqF,CAAC,CAAC,CAAChF,OAAO,CAAC,WAAW,EAAC,KAAK,CAAC,CAACM,QAAQ,CAAC,CAAC;EAACqH,EAAE,GAAChI,CAAC,CAAC,0JAA0J,CAAC,CAACK,OAAO,CAAC,SAAS,EAAC0H,EAAE,CAAC,CAAC1H,OAAO,CAAC,WAAW,EAAC,6EAA6E,CAAC,CAACM,QAAQ,CAAC,CAAC;EAACsH,CAAC,GAAC,qDAAqD;EAACC,EAAE,GAAClI,CAAC,CAAC,mEAAmE,CAAC,CAACK,OAAO,CAAC,OAAO,EAAC4H,CAAC,CAAC,CAAC5H,OAAO,CAAC,MAAM,EAAC,yCAAyC,CAAC,CAACA,OAAO,CAAC,OAAO,EAAC,6DAA6D,CAAC,CAACM,QAAQ,CAAC,CAAC;EAACwH,EAAE,GAACnI,CAAC,CAAC,yBAAyB,CAAC,CAACK,OAAO,CAAC,OAAO,EAAC4H,CAAC,CAAC,CAAC5H,OAAO,CAAC,KAAK,EAAC4E,CAAC,CAAC,CAACtE,QAAQ,CAAC,CAAC;EAACyH,EAAE,GAACpI,CAAC,CAAC,uBAAuB,CAAC,CAACK,OAAO,CAAC,KAAK,EAAC4E,CAAC,CAAC,CAACtE,QAAQ,CAAC,CAAC;EAAC0H,EAAE,GAACrI,CAAC,CAAC,uBAAuB,EAAC,GAAG,CAAC,CAACK,OAAO,CAAC,SAAS,EAAC8H,EAAE,CAAC,CAAC9H,OAAO,CAAC,QAAQ,EAAC+H,EAAE,CAAC,CAACzH,QAAQ,CAAC,CAAC;EAAC2H,CAAC,GAAC;IAACC,UAAU,EAACzI,CAAC;IAAC0I,cAAc,EAACX,EAAE;IAACY,QAAQ,EAACX,EAAE;IAACY,SAAS,EAACrB,EAAE;IAACsB,EAAE,EAAC/B,EAAE;IAACjB,IAAI,EAACgB,EAAE;IAACiC,GAAG,EAAC9I,CAAC;IAAC+I,cAAc,EAACtB,EAAE;IAACuB,iBAAiB,EAACpB,EAAE;IAACqB,iBAAiB,EAACnB,EAAE;IAACoB,MAAM,EAACtC,EAAE;IAACuC,IAAI,EAACf,EAAE;IAACgB,MAAM,EAACd,EAAE;IAACe,WAAW,EAAClC,EAAE;IAACmC,OAAO,EAACjB,EAAE;IAACkB,aAAa,EAAChB,EAAE;IAACiB,GAAG,EAACtB,EAAE;IAAC1B,IAAI,EAACO,EAAE;IAAC0C,GAAG,EAACzJ;EAAC,CAAC;EAAC0J,EAAE,GAAC;IAAC,GAAGlB,CAAC;IAACW,IAAI,EAACjJ,CAAC,CAAC,yBAAyB,CAAC,CAACK,OAAO,CAAC,OAAO,EAAC4H,CAAC,CAAC,CAACtH,QAAQ,CAAC,CAAC;IAACyI,OAAO,EAACpJ,CAAC,CAAC,+BAA+B,CAAC,CAACK,OAAO,CAAC,OAAO,EAAC4H,CAAC,CAAC,CAACtH,QAAQ,CAAC;EAAC,CAAC;EAAC8I,CAAC,GAAC;IAAC,GAAGnB,CAAC;IAACQ,iBAAiB,EAACnB,EAAE;IAACkB,cAAc,EAACrB,EAAE;IAAC+B,GAAG,EAACvJ,CAAC,CAAC,kEAAkE,EAAC,GAAG,CAAC,CAACK,OAAO,CAAC,OAAO,EAAC,2EAA2E,CAAC,CAACM,QAAQ,CAAC,CAAC;IAAC4H,UAAU,EAAC,4EAA4E;IAACK,GAAG,EAAC,+DAA+D;IAACtC,IAAI,EAAC;EAA4N,CAAC;EAACoD,EAAE,GAAC;IAAC,GAAGD,CAAC;IAACd,EAAE,EAAC3I,CAAC,CAAC4G,EAAE,CAAC,CAACvG,OAAO,CAAC,MAAM,EAAC,GAAG,CAAC,CAACM,QAAQ,CAAC,CAAC;IAAC2F,IAAI,EAACtG,CAAC,CAACyJ,CAAC,CAACnD,IAAI,CAAC,CAACjG,OAAO,CAAC,MAAM,EAAC,eAAe,CAAC,CAACA,OAAO,CAAC,SAAS,EAAC,GAAG,CAAC,CAACM,QAAQ,CAAC;EAAC,CAAC;EAACgJ,CAAC,GAAC;IAACC,MAAM,EAACnE,CAAC;IAACrG,GAAG,EAACoH,EAAE;IAAClH,QAAQ,EAACmH;EAAE,CAAC;EAACoD,CAAC,GAAC;IAACD,MAAM,EAACtB,CAAC;IAAClJ,GAAG,EAACqK,CAAC;IAACvK,MAAM,EAACwK,EAAE;IAACpK,QAAQ,EAACkK;EAAE,CAAC;AAAC,IAAIM,EAAE,GAAC;IAAC,GAAG,EAAC,OAAO;IAAC,GAAG,EAAC,MAAM;IAAC,GAAG,EAAC,MAAM;IAAC,GAAG,EAAC,QAAQ;IAAC,GAAG,EAAC;EAAO,CAAC;EAACC,EAAE,GAAClK,CAAC,IAAEiK,EAAE,CAACjK,CAAC,CAAC;AAAC,SAASmK,CAACA,CAACnK,CAAC,EAACI,CAAC,EAAC;EAAC,IAAGA,CAAC,EAAC;IAAC,IAAGQ,CAAC,CAACwC,UAAU,CAACgH,IAAI,CAACpK,CAAC,CAAC,EAAC,OAAOA,CAAC,CAACQ,OAAO,CAACI,CAAC,CAACyC,aAAa,EAAC6G,EAAE,CAAC;EAAA,CAAC,MAAK,IAAGtJ,CAAC,CAAC0C,kBAAkB,CAAC8G,IAAI,CAACpK,CAAC,CAAC,EAAC,OAAOA,CAAC,CAACQ,OAAO,CAACI,CAAC,CAAC2C,qBAAqB,EAAC2G,EAAE,CAAC;EAAC,OAAOlK,CAAC;AAAA;AAAC,SAASqK,CAACA,CAACrK,CAAC,EAAC;EAAC,IAAG;IAACA,CAAC,GAACsK,SAAS,CAACtK,CAAC,CAAC,CAACQ,OAAO,CAACI,CAAC,CAAC6C,aAAa,EAAC,GAAG,CAAC;EAAA,CAAC,OAAK;IAAC,OAAO,IAAI;EAAA;EAAC,OAAOzD,CAAC;AAAA;AAAC,SAASuK,CAACA,CAACvK,CAAC,EAACI,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACL,CAAC,CAACQ,OAAO,CAACI,CAAC,CAAC8C,QAAQ,EAAC,CAAChD,CAAC,EAACC,CAAC,EAAC6J,CAAC,KAAG;MAAC,IAAIC,CAAC,GAAC,CAAC,CAAC;QAACC,CAAC,GAAC/J,CAAC;MAAC,OAAK,EAAE+J,CAAC,IAAE,CAAC,IAAEF,CAAC,CAACE,CAAC,CAAC,KAAG,IAAI,GAAED,CAAC,GAAC,CAACA,CAAC;MAAC,OAAOA,CAAC,GAAC,GAAG,GAAC,IAAI;IAAA,CAAC,CAAC;IAAClK,CAAC,GAACF,CAAC,CAACsK,KAAK,CAAC/J,CAAC,CAAC+C,SAAS,CAAC;IAAClD,CAAC,GAAC,CAAC;EAAC,IAAGF,CAAC,CAAC,CAAC,CAAC,CAACqK,IAAI,CAAC,CAAC,IAAErK,CAAC,CAACsK,KAAK,CAAC,CAAC,EAACtK,CAAC,CAACuK,MAAM,GAAC,CAAC,IAAE,CAACvK,CAAC,CAACwK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEH,IAAI,CAAC,CAAC,IAAErK,CAAC,CAACyK,GAAG,CAAC,CAAC,EAAC5K,CAAC,EAAC,IAAGG,CAAC,CAACuK,MAAM,GAAC1K,CAAC,EAACG,CAAC,CAAC0K,MAAM,CAAC7K,CAAC,CAAC,CAAC,KAAK,OAAKG,CAAC,CAACuK,MAAM,GAAC1K,CAAC,GAAEG,CAAC,CAAC2K,IAAI,CAAC,EAAE,CAAC;EAAC,OAAKzK,CAAC,GAACF,CAAC,CAACuK,MAAM,EAACrK,CAAC,EAAE,EAACF,CAAC,CAACE,CAAC,CAAC,GAACF,CAAC,CAACE,CAAC,CAAC,CAACmK,IAAI,CAAC,CAAC,CAACpK,OAAO,CAACI,CAAC,CAACgD,SAAS,EAAC,GAAG,CAAC;EAAC,OAAOrD,CAAC;AAAA;AAAC,SAAS4K,CAACA,CAACnL,CAAC,EAACI,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIE,CAAC,GAACP,CAAC,CAAC8K,MAAM;EAAC,IAAGvK,CAAC,KAAG,CAAC,EAAC,OAAM,EAAE;EAAC,IAAIE,CAAC,GAAC,CAAC;EAAC,OAAKA,CAAC,GAACF,CAAC,GAAE;IAAC,IAAIG,CAAC,GAACV,CAAC,CAACoL,MAAM,CAAC7K,CAAC,GAACE,CAAC,GAAC,CAAC,CAAC;IAAC,IAAGC,CAAC,KAAGN,CAAC,IAAE,CAACC,CAAC,EAACI,CAAC,EAAE,CAAC,KAAK,IAAGC,CAAC,KAAGN,CAAC,IAAEC,CAAC,EAACI,CAAC,EAAE,CAAC,KAAK;EAAK;EAAC,OAAOT,CAAC,CAACqL,KAAK,CAAC,CAAC,EAAC9K,CAAC,GAACE,CAAC,CAAC;AAAA;AAAC,SAAS6K,EAAEA,CAACtL,CAAC,EAACI,CAAC,EAAC;EAAC,IAAGJ,CAAC,CAACuL,OAAO,CAACnL,CAAC,CAAC,CAAC,CAAC,CAAC,KAAG,CAAC,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,IAAIC,CAAC,GAAC,CAAC;EAAC,KAAI,IAAIE,CAAC,GAAC,CAAC,EAACA,CAAC,GAACP,CAAC,CAAC8K,MAAM,EAACvK,CAAC,EAAE,EAAC,IAAGP,CAAC,CAACO,CAAC,CAAC,KAAG,IAAI,EAACA,CAAC,EAAE,CAAC,KAAK,IAAGP,CAAC,CAACO,CAAC,CAAC,KAAGH,CAAC,CAAC,CAAC,CAAC,EAACC,CAAC,EAAE,CAAC,KAAK,IAAGL,CAAC,CAACO,CAAC,CAAC,KAAGH,CAAC,CAAC,CAAC,CAAC,KAAGC,CAAC,EAAE,EAACA,CAAC,GAAC,CAAC,CAAC,EAAC,OAAOE,CAAC;EAAC,OAAOF,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC;AAAA;AAAC,SAASmL,EAAEA,CAACxL,CAAC,EAACI,CAAC,EAACC,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACN,CAAC,CAACqL,IAAI;IAAC9K,CAAC,GAACP,CAAC,CAACsL,KAAK,IAAE,IAAI;IAAClB,CAAC,GAACxK,CAAC,CAAC,CAAC,CAAC,CAACQ,OAAO,CAACC,CAAC,CAACkL,KAAK,CAAC1K,iBAAiB,EAAC,IAAI,CAAC;EAACV,CAAC,CAACqL,KAAK,CAACC,MAAM,GAAC,CAAC,CAAC;EAAC,IAAIpB,CAAC,GAAC;IAACqB,IAAI,EAAC9L,CAAC,CAAC,CAAC,CAAC,CAACoL,MAAM,CAAC,CAAC,CAAC,KAAG,GAAG,GAAC,OAAO,GAAC,MAAM;IAACW,GAAG,EAAC1L,CAAC;IAACoL,IAAI,EAAC/K,CAAC;IAACgL,KAAK,EAAC/K,CAAC;IAAC8F,IAAI,EAAC+D,CAAC;IAACwB,MAAM,EAACzL,CAAC,CAAC0L,YAAY,CAACzB,CAAC;EAAC,CAAC;EAAC,OAAOjK,CAAC,CAACqL,KAAK,CAACC,MAAM,GAAC,CAAC,CAAC,EAACpB,CAAC;AAAA;AAAC,SAASyB,EAAEA,CAAClM,CAAC,EAACI,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIE,CAAC,GAACP,CAAC,CAACmM,KAAK,CAAC9L,CAAC,CAACsL,KAAK,CAACzK,sBAAsB,CAAC;EAAC,IAAGX,CAAC,KAAG,IAAI,EAAC,OAAOH,CAAC;EAAC,IAAIK,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC;EAAC,OAAOH,CAAC,CAACuK,KAAK,CAAE;AAClvK,CAAC,CAAC,CAACyB,GAAG,CAAC1L,CAAC,IAAE;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACyL,KAAK,CAAC9L,CAAC,CAACsL,KAAK,CAACxK,cAAc,CAAC;IAAC,IAAGR,CAAC,KAAG,IAAI,EAAC,OAAOD,CAAC;IAAC,IAAG,CAAC8J,CAAC,CAAC,GAAC7J,CAAC;IAAC,OAAO6J,CAAC,CAACM,MAAM,IAAErK,CAAC,CAACqK,MAAM,GAACpK,CAAC,CAAC2K,KAAK,CAAC5K,CAAC,CAACqK,MAAM,CAAC,GAACpK,CAAC;EAAA,CAAC,CAAC,CAAC2L,IAAI,CAAE;AACrI,CAAC,CAAC;AAAA;AAAC,IAAIC,CAAC,GAAC,MAAK;EAACC,OAAO;EAACC,KAAK;EAACC,KAAK;EAACC,WAAWA,CAACtM,CAAC,EAAC;IAAC,IAAI,CAACmM,OAAO,GAACnM,CAAC,IAAEN,CAAC;EAAA;EAAC6M,KAAKA,CAACvM,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAACtG,OAAO,CAACpG,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,CAACyK,MAAM,GAAC,CAAC,EAAC,OAAM;MAACgB,IAAI,EAAC,OAAO;MAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC;IAAC,CAAC;EAAA;EAACyF,IAAIA,CAAC1F,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAAC9G,IAAI,CAAC5F,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAAC3K,gBAAgB,EAAC,EAAE,CAAC;MAAC,OAAM;QAAC8K,IAAI,EAAC,MAAM;QAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;QAACwM,cAAc,EAAC,UAAU;QAACpG,IAAI,EAAC,IAAI,CAAC8F,OAAO,CAAC9M,QAAQ,GAACc,CAAC,GAAC4K,CAAC,CAAC5K,CAAC,EAAE;AACzW,CAAC;MAAC,CAAC;IAAA;EAAC;EAACyF,MAAMA,CAAC5F,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAAC5G,MAAM,CAAC9F,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC;QAACI,CAAC,GAACyL,EAAE,CAAC3L,CAAC,EAACF,CAAC,CAAC,CAAC,CAAC,IAAE,EAAE,EAAC,IAAI,CAACmM,KAAK,CAAC;MAAC,OAAM;QAACV,IAAI,EAAC,MAAM;QAACC,GAAG,EAACxL,CAAC;QAACuM,IAAI,EAACzM,CAAC,CAAC,CAAC,CAAC,GAACA,CAAC,CAAC,CAAC,CAAC,CAACuK,IAAI,CAAC,CAAC,CAACpK,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACO,MAAM,CAACpE,cAAc,EAAC,IAAI,CAAC,GAACtI,CAAC,CAAC,CAAC,CAAC;QAACoG,IAAI,EAAChG;MAAC,CAAC;IAAA;EAAC;EAACwF,OAAOA,CAAC7F,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAAC3G,OAAO,CAAC/F,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,CAACuK,IAAI,CAAC,CAAC;MAAC,IAAG,IAAI,CAAC4B,KAAK,CAACb,KAAK,CAACvK,UAAU,CAACgJ,IAAI,CAAC7J,CAAC,CAAC,EAAC;QAAC,IAAIE,CAAC,GAAC0K,CAAC,CAAC5K,CAAC,EAAC,GAAG,CAAC;QAAC,CAAC,IAAI,CAACgM,OAAO,CAAC9M,QAAQ,IAAE,CAACgB,CAAC,IAAE,IAAI,CAAC+L,KAAK,CAACb,KAAK,CAACrK,eAAe,CAAC8I,IAAI,CAAC3J,CAAC,CAAC,MAAIF,CAAC,GAACE,CAAC,CAACmK,IAAI,CAAC,CAAC,CAAC;MAAA;MAAC,OAAM;QAACkB,IAAI,EAAC,SAAS;QAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;QAAC2M,KAAK,EAAC3M,CAAC,CAAC,CAAC,CAAC,CAACyK,MAAM;QAACrE,IAAI,EAAClG,CAAC;QAACyL,MAAM,EAAC,IAAI,CAACS,KAAK,CAACM,MAAM,CAACxM,CAAC;MAAC,CAAC;IAAA;EAAC;EAAC2F,EAAEA,CAAC9F,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAAC1G,EAAE,CAAChG,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC,OAAM;MAACyL,IAAI,EAAC,IAAI;MAACC,GAAG,EAACZ,CAAC,CAAC9K,CAAC,CAAC,CAAC,CAAC,EAAE;AACnkB,CAAC;IAAC,CAAC;EAAA;EAACwF,UAAUA,CAACzF,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAAC/G,UAAU,CAAC3F,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAAC4K,CAAC,CAAC9K,CAAC,CAAC,CAAC,CAAC,EAAE;AAChF,CAAC,CAAC,CAACsK,KAAK,CAAE;AACV,CAAC,CAAC;QAAClK,CAAC,GAAC,EAAE;QAACC,CAAC,GAAC,EAAE;QAACC,CAAC,GAAC,EAAE;MAAC,OAAKJ,CAAC,CAACuK,MAAM,GAAC,CAAC,GAAE;QAAC,IAAIN,CAAC,GAAC,CAAC,CAAC;UAACC,CAAC,GAAC,EAAE;UAACC,CAAC;QAAC,KAAIA,CAAC,GAAC,CAAC,EAACA,CAAC,GAACnK,CAAC,CAACuK,MAAM,EAACJ,CAAC,EAAE,EAAC,IAAG,IAAI,CAAC8B,KAAK,CAACb,KAAK,CAAC9J,eAAe,CAACuI,IAAI,CAAC7J,CAAC,CAACmK,CAAC,CAAC,CAAC,EAACD,CAAC,CAACS,IAAI,CAAC3K,CAAC,CAACmK,CAAC,CAAC,CAAC,EAACF,CAAC,GAAC,CAAC,CAAC,CAAC,KAAK,IAAG,CAACA,CAAC,EAACC,CAAC,CAACS,IAAI,CAAC3K,CAAC,CAACmK,CAAC,CAAC,CAAC,CAAC,KAAK;QAAMnK,CAAC,GAACA,CAAC,CAAC8K,KAAK,CAACX,CAAC,CAAC;QAAC,IAAIuC,CAAC,GAACxC,CAAC,CAAC4B,IAAI,CAAE;AAC1M,CAAC,CAAC;UAACa,CAAC,GAACD,CAAC,CAACzM,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAAC7J,uBAAuB,EAAE;AACzD,OAAO,CAAC,CAACtB,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAAC5J,wBAAwB,EAAC,EAAE,CAAC;QAACtB,CAAC,GAACA,CAAC,GAAE,GAAEA,CAAE;AACxE,EAAEwM,CAAE,EAAC,GAACA,CAAC,EAACvM,CAAC,GAACA,CAAC,GAAE,GAAEA,CAAE;AACjB,EAAEwM,CAAE,EAAC,GAACA,CAAC;QAAC,IAAIC,CAAC,GAAC,IAAI,CAACV,KAAK,CAACb,KAAK,CAACwB,GAAG;QAAC,IAAG,IAAI,CAACX,KAAK,CAACb,KAAK,CAACwB,GAAG,GAAC,CAAC,CAAC,EAAC,IAAI,CAACX,KAAK,CAACY,WAAW,CAACH,CAAC,EAACvM,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,IAAI,CAAC8L,KAAK,CAACb,KAAK,CAACwB,GAAG,GAACD,CAAC,EAAC5M,CAAC,CAACuK,MAAM,KAAG,CAAC,EAAC;QAAM,IAAIwC,CAAC,GAAC3M,CAAC,CAACoK,EAAE,CAAC,CAAC,CAAC,CAAC;QAAC,IAAGuC,CAAC,EAAExB,IAAI,KAAG,MAAM,EAAC;QAAM,IAAGwB,CAAC,EAAExB,IAAI,KAAG,YAAY,EAAC;UAAC,IAAIyB,CAAC,GAACD,CAAC;YAACE,CAAC,GAACD,CAAC,CAACxB,GAAG,GAAE;AAC5N,CAAC,GAACxL,CAAC,CAAC8L,IAAI,CAAE;AACV,CAAC,CAAC;YAACoB,CAAC,GAAC,IAAI,CAAC5H,UAAU,CAAC2H,CAAC,CAAC;UAAC7M,CAAC,CAACA,CAAC,CAACmK,MAAM,GAAC,CAAC,CAAC,GAAC2C,CAAC,EAAChN,CAAC,GAACA,CAAC,CAACiN,SAAS,CAAC,CAAC,EAACjN,CAAC,CAACqK,MAAM,GAACyC,CAAC,CAACxB,GAAG,CAACjB,MAAM,CAAC,GAAC2C,CAAC,CAAC1B,GAAG,EAACrL,CAAC,GAACA,CAAC,CAACgN,SAAS,CAAC,CAAC,EAAChN,CAAC,CAACoK,MAAM,GAACyC,CAAC,CAAC9G,IAAI,CAACqE,MAAM,CAAC,GAAC2C,CAAC,CAAChH,IAAI;UAAC;QAAK,CAAC,MAAK,IAAG6G,CAAC,EAAExB,IAAI,KAAG,MAAM,EAAC;UAAC,IAAIyB,CAAC,GAACD,CAAC;YAACE,CAAC,GAACD,CAAC,CAACxB,GAAG,GAAE;AACrL,CAAC,GAACxL,CAAC,CAAC8L,IAAI,CAAE;AACV,CAAC,CAAC;YAACoB,CAAC,GAAC,IAAI,CAACpH,IAAI,CAACmH,CAAC,CAAC;UAAC7M,CAAC,CAACA,CAAC,CAACmK,MAAM,GAAC,CAAC,CAAC,GAAC2C,CAAC,EAAChN,CAAC,GAACA,CAAC,CAACiN,SAAS,CAAC,CAAC,EAACjN,CAAC,CAACqK,MAAM,GAACwC,CAAC,CAACvB,GAAG,CAACjB,MAAM,CAAC,GAAC2C,CAAC,CAAC1B,GAAG,EAACrL,CAAC,GAACA,CAAC,CAACgN,SAAS,CAAC,CAAC,EAAChN,CAAC,CAACoK,MAAM,GAACyC,CAAC,CAACxB,GAAG,CAACjB,MAAM,CAAC,GAAC2C,CAAC,CAAC1B,GAAG,EAACxL,CAAC,GAACiN,CAAC,CAACE,SAAS,CAAC/M,CAAC,CAACoK,EAAE,CAAC,CAAC,CAAC,CAAC,CAACgB,GAAG,CAACjB,MAAM,CAAC,CAACH,KAAK,CAAE;AACtK,CAAC,CAAC;UAAC;QAAQ;MAAC;MAAC,OAAM;QAACmB,IAAI,EAAC,YAAY;QAACC,GAAG,EAACtL,CAAC;QAACuL,MAAM,EAACrL,CAAC;QAAC8F,IAAI,EAAC/F;MAAC,CAAC;IAAA;EAAC;EAAC2F,IAAIA,CAACjG,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAACvG,IAAI,CAACnG,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,CAACuK,IAAI,CAAC,CAAC;QAACnK,CAAC,GAACF,CAAC,CAACuK,MAAM,GAAC,CAAC;QAACpK,CAAC,GAAC;UAACoL,IAAI,EAAC,MAAM;UAACC,GAAG,EAAC,EAAE;UAAC4B,OAAO,EAAClN,CAAC;UAACmN,KAAK,EAACnN,CAAC,GAAC,CAACF,CAAC,CAAC8K,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,GAAC,EAAE;UAACwC,KAAK,EAAC,CAAC,CAAC;UAACC,KAAK,EAAC;QAAE,CAAC;MAACvN,CAAC,GAACE,CAAC,GAAE,aAAYF,CAAC,CAAC8K,KAAK,CAAC,CAAC,CAAC,CAAE,EAAC,GAAE,KAAI9K,CAAE,EAAC,EAAC,IAAI,CAACgM,OAAO,CAAC9M,QAAQ,KAAGc,CAAC,GAACE,CAAC,GAACF,CAAC,GAAC,OAAO,CAAC;MAAC,IAAII,CAAC,GAAC,IAAI,CAAC6L,KAAK,CAACb,KAAK,CAAC1H,aAAa,CAAC1D,CAAC,CAAC;QAACiK,CAAC,GAAC,CAAC,CAAC;MAAC,OAAKpK,CAAC,GAAE;QAAC,IAAIsK,CAAC,GAAC,CAAC,CAAC;UAACuC,CAAC,GAAC,EAAE;UAACC,CAAC,GAAC,EAAE;QAAC,IAAG,EAAE7M,CAAC,GAACM,CAAC,CAACT,IAAI,CAACE,CAAC,CAAC,CAAC,IAAE,IAAI,CAACoM,KAAK,CAACI,KAAK,CAAC1G,EAAE,CAACkE,IAAI,CAAChK,CAAC,CAAC,EAAC;QAAM6M,CAAC,GAAC5M,CAAC,CAAC,CAAC,CAAC,EAACD,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACT,CAAC,CAACnC,MAAM,CAAC;QAAC,IAAIqC,CAAC,GAAC9M,CAAC,CAAC,CAAC,CAAC,CAACsK,KAAK,CAAE;AAC1d,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACnK,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAAC3J,eAAe,EAAC+L,CAAC,IAAE,GAAG,CAACC,MAAM,CAAC,CAAC,GAACD,CAAC,CAACjD,MAAM,CAAC,CAAC;UAACwC,CAAC,GAAClN,CAAC,CAACuK,KAAK,CAAE;AACvF,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAAC4C,CAAC,GAAC,CAACJ,CAAC,CAACvC,IAAI,CAAC,CAAC;UAAC4C,CAAC,GAAC,CAAC;QAAC,IAAG,IAAI,CAACjB,OAAO,CAAC9M,QAAQ,IAAE+N,CAAC,GAAC,CAAC,EAACN,CAAC,GAACC,CAAC,CAACc,SAAS,CAAC,CAAC,IAAEV,CAAC,GAACC,CAAC,GAACnN,CAAC,CAAC,CAAC,CAAC,CAACyK,MAAM,GAAC,CAAC,IAAE0C,CAAC,GAACnN,CAAC,CAAC,CAAC,CAAC,CAAC6N,MAAM,CAAC,IAAI,CAAC1B,KAAK,CAACb,KAAK,CAACpK,YAAY,CAAC,EAACiM,CAAC,GAACA,CAAC,GAAC,CAAC,GAAC,CAAC,GAACA,CAAC,EAACN,CAAC,GAACC,CAAC,CAAC9B,KAAK,CAACmC,CAAC,CAAC,EAACA,CAAC,IAAEnN,CAAC,CAAC,CAAC,CAAC,CAACyK,MAAM,CAAC,EAACyC,CAAC,IAAE,IAAI,CAACf,KAAK,CAACb,KAAK,CAAChK,SAAS,CAACyI,IAAI,CAACkD,CAAC,CAAC,KAAGL,CAAC,IAAEK,CAAC,GAAE;AAC5N,CAAC,EAAClN,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACJ,CAAC,CAACxC,MAAM,GAAC,CAAC,CAAC,EAACJ,CAAC,GAAC,CAAC,CAAC,CAAC,EAAC,CAACA,CAAC,EAAC;UAAC,IAAIqD,CAAC,GAAC,IAAI,CAACvB,KAAK,CAACb,KAAK,CAACzH,eAAe,CAACsJ,CAAC,CAAC;YAACW,EAAE,GAAC,IAAI,CAAC3B,KAAK,CAACb,KAAK,CAACtH,OAAO,CAACmJ,CAAC,CAAC;YAACY,EAAE,GAAC,IAAI,CAAC5B,KAAK,CAACb,KAAK,CAACrH,gBAAgB,CAACkJ,CAAC,CAAC;YAACa,EAAE,GAAC,IAAI,CAAC7B,KAAK,CAACb,KAAK,CAACpH,iBAAiB,CAACiJ,CAAC,CAAC;YAACc,EAAE,GAAC,IAAI,CAAC9B,KAAK,CAACb,KAAK,CAACnH,cAAc,CAACgJ,CAAC,CAAC;UAAC,OAAKpN,CAAC,GAAE;YAAC,IAAImO,CAAC,GAACnO,CAAC,CAACuK,KAAK,CAAE;AAC9P,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAAC6D,CAAC;YAAC,IAAGlB,CAAC,GAACiB,CAAC,EAAC,IAAI,CAAChC,OAAO,CAAC9M,QAAQ,IAAE6N,CAAC,GAACA,CAAC,CAAC9M,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAAC1J,kBAAkB,EAAC,IAAI,CAAC,EAACuM,CAAC,GAAClB,CAAC,IAAEkB,CAAC,GAAClB,CAAC,CAAC9M,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAAClK,aAAa,EAAC,MAAM,CAAC,EAAC2M,EAAE,CAAChE,IAAI,CAACkD,CAAC,CAAC,IAAEe,EAAE,CAACjE,IAAI,CAACkD,CAAC,CAAC,IAAEgB,EAAE,CAAClE,IAAI,CAACkD,CAAC,CAAC,IAAES,CAAC,CAAC3D,IAAI,CAACkD,CAAC,CAAC,IAAEa,EAAE,CAAC/D,IAAI,CAACkD,CAAC,CAAC,EAAC;YAAM,IAAGkB,CAAC,CAACN,MAAM,CAAC,IAAI,CAAC1B,KAAK,CAACb,KAAK,CAACpK,YAAY,CAAC,IAAEiM,CAAC,IAAE,CAACF,CAAC,CAAC1C,IAAI,CAAC,CAAC,EAACsC,CAAC,IAAG;AACnR,CAAC,GAACsB,CAAC,CAACnD,KAAK,CAACmC,CAAC,CAAC,CAAC,KAAI;cAAC,IAAGD,CAAC,IAAEJ,CAAC,CAAC3M,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAAClK,aAAa,EAAC,MAAM,CAAC,CAACyM,MAAM,CAAC,IAAI,CAAC1B,KAAK,CAACb,KAAK,CAACpK,YAAY,CAAC,IAAE,CAAC,IAAE6M,EAAE,CAAChE,IAAI,CAAC+C,CAAC,CAAC,IAAEkB,EAAE,CAACjE,IAAI,CAAC+C,CAAC,CAAC,IAAEgB,EAAE,CAAC/D,IAAI,CAAC+C,CAAC,CAAC,EAAC;cAAMD,CAAC,IAAG;AAChK,CAAC,GAACI,CAAC;YAAA;YAAC,CAACC,CAAC,IAAE,CAACD,CAAC,CAAC1C,IAAI,CAAC,CAAC,KAAG2C,CAAC,GAAC,CAAC,CAAC,CAAC,EAACN,CAAC,IAAEsB,CAAC,GAAE;AAChC,CAAC,EAACnO,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACa,CAAC,CAACzD,MAAM,GAAC,CAAC,CAAC,EAACqC,CAAC,GAACqB,CAAC,CAACnD,KAAK,CAACmC,CAAC,CAAC;UAAA;QAAC;QAAC9M,CAAC,CAACmN,KAAK,KAAGrD,CAAC,GAAC9J,CAAC,CAACmN,KAAK,GAAC,CAAC,CAAC,GAAC,IAAI,CAACrB,KAAK,CAACb,KAAK,CAAC/J,eAAe,CAACwI,IAAI,CAAC6C,CAAC,CAAC,KAAGzC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;QAAC,IAAIiD,CAAC,GAAC,IAAI;UAACgB,CAAC;QAAC,IAAI,CAAClC,OAAO,CAAChN,GAAG,KAAGkO,CAAC,GAAC,IAAI,CAACjB,KAAK,CAACb,KAAK,CAACzJ,UAAU,CAAChC,IAAI,CAACgN,CAAC,CAAC,EAACO,CAAC,KAAGgB,CAAC,GAAChB,CAAC,CAAC,CAAC,CAAC,KAAG,MAAM,EAACP,CAAC,GAACA,CAAC,CAAC1M,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAACxJ,eAAe,EAAC,EAAE,CAAC,CAAC,CAAC,EAACzB,CAAC,CAACoN,KAAK,CAAC5C,IAAI,CAAC;UAACY,IAAI,EAAC,WAAW;UAACC,GAAG,EAACkB,CAAC;UAACyB,IAAI,EAAC,CAAC,CAACjB,CAAC;UAACkB,OAAO,EAACF,CAAC;UAACZ,KAAK,EAAC,CAAC,CAAC;UAACpH,IAAI,EAACyG,CAAC;UAAClB,MAAM,EAAC;QAAE,CAAC,CAAC,EAACtL,CAAC,CAACqL,GAAG,IAAEkB,CAAC;MAAA;MAAC,IAAIxC,CAAC,GAAC/J,CAAC,CAACoN,KAAK,CAAC/C,EAAE,CAAC,CAAC,CAAC,CAAC;MAAC,IAAGN,CAAC,EAACA,CAAC,CAACsB,GAAG,GAACtB,CAAC,CAACsB,GAAG,CAAC6C,OAAO,CAAC,CAAC,EAACnE,CAAC,CAAChE,IAAI,GAACgE,CAAC,CAAChE,IAAI,CAACmI,OAAO,CAAC,CAAC,CAAC,KAAK;MAAOlO,CAAC,CAACqL,GAAG,GAACrL,CAAC,CAACqL,GAAG,CAAC6C,OAAO,CAAC,CAAC;MAAC,KAAI,IAAIlE,CAAC,GAAC,CAAC,EAACA,CAAC,GAAChK,CAAC,CAACoN,KAAK,CAAChD,MAAM,EAACJ,CAAC,EAAE,EAAC,IAAG,IAAI,CAAC+B,KAAK,CAACb,KAAK,CAACwB,GAAG,GAAC,CAAC,CAAC,EAAC1M,CAAC,CAACoN,KAAK,CAACpD,CAAC,CAAC,CAACsB,MAAM,GAAC,IAAI,CAACS,KAAK,CAACY,WAAW,CAAC3M,CAAC,CAACoN,KAAK,CAACpD,CAAC,CAAC,CAACjE,IAAI,EAAC,EAAE,CAAC,EAAC,CAAC/F,CAAC,CAACmN,KAAK,EAAC;QAAC,IAAIZ,CAAC,GAACvM,CAAC,CAACoN,KAAK,CAACpD,CAAC,CAAC,CAACsB,MAAM,CAAC6C,MAAM,CAAC1B,CAAC,IAAEA,CAAC,CAACrB,IAAI,KAAG,OAAO,CAAC;UAACoB,CAAC,GAACD,CAAC,CAACnC,MAAM,GAAC,CAAC,IAAEmC,CAAC,CAAC6B,IAAI,CAAC3B,CAAC,IAAE,IAAI,CAACX,KAAK,CAACb,KAAK,CAACvJ,OAAO,CAACgI,IAAI,CAAC+C,CAAC,CAACpB,GAAG,CAAC,CAAC;QAACrL,CAAC,CAACmN,KAAK,GAACX,CAAC;MAAA;MAAC,IAAGxM,CAAC,CAACmN,KAAK,EAAC,KAAI,IAAInD,CAAC,GAAC,CAAC,EAACA,CAAC,GAAChK,CAAC,CAACoN,KAAK,CAAChD,MAAM,EAACJ,CAAC,EAAE,EAAChK,CAAC,CAACoN,KAAK,CAACpD,CAAC,CAAC,CAACmD,KAAK,GAAC,CAAC,CAAC;MAAC,OAAOnN,CAAC;IAAA;EAAC;EAACyF,IAAIA,CAAC/F,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAACzG,IAAI,CAACjG,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC,OAAM;MAACyL,IAAI,EAAC,MAAM;MAACc,KAAK,EAAC,CAAC,CAAC;MAACb,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;MAAC0O,GAAG,EAAC1O,CAAC,CAAC,CAAC,CAAC,KAAG,KAAK,IAAEA,CAAC,CAAC,CAAC,CAAC,KAAG,QAAQ,IAAEA,CAAC,CAAC,CAAC,CAAC,KAAG,OAAO;MAACoG,IAAI,EAACpG,CAAC,CAAC,CAAC;IAAC,CAAC;EAAA;EAAC0F,GAAGA,CAAC3F,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAAC7G,GAAG,CAAC7F,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,CAAC2O,WAAW,CAAC,CAAC,CAACxO,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAACjK,mBAAmB,EAAC,GAAG,CAAC;QAACjB,CAAC,GAACJ,CAAC,CAAC,CAAC,CAAC,GAACA,CAAC,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAACtJ,YAAY,EAAC,IAAI,CAAC,CAAC7B,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACO,MAAM,CAACpE,cAAc,EAAC,IAAI,CAAC,GAAC,EAAE;QAACjI,CAAC,GAACL,CAAC,CAAC,CAAC,CAAC,GAACA,CAAC,CAAC,CAAC,CAAC,CAACqN,SAAS,CAAC,CAAC,EAACrN,CAAC,CAAC,CAAC,CAAC,CAACyK,MAAM,GAAC,CAAC,CAAC,CAACtK,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACO,MAAM,CAACpE,cAAc,EAAC,IAAI,CAAC,GAACtI,CAAC,CAAC,CAAC,CAAC;MAAC,OAAM;QAACyL,IAAI,EAAC,KAAK;QAACrC,GAAG,EAAClJ,CAAC;QAACwL,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;QAACoL,IAAI,EAAChL,CAAC;QAACiL,KAAK,EAAChL;MAAC,CAAC;IAAA;EAAC;EAAC8F,KAAKA,CAACpG,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAACpG,KAAK,CAACtG,IAAI,CAACE,CAAC,CAAC;IAAC,IAAG,CAACC,CAAC,IAAE,CAAC,IAAI,CAACmM,KAAK,CAACb,KAAK,CAACrJ,cAAc,CAAC8H,IAAI,CAAC/J,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;IAAO,IAAIE,CAAC,GAACgK,CAAC,CAAClK,CAAC,CAAC,CAAC,CAAC,CAAC;MAACI,CAAC,GAACJ,CAAC,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAACpJ,eAAe,EAAC,EAAE,CAAC,CAACoI,KAAK,CAAC,GAAG,CAAC;MAACjK,CAAC,GAACL,CAAC,CAAC,CAAC,CAAC,EAAEuK,IAAI,CAAC,CAAC,GAACvK,CAAC,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAACnJ,iBAAiB,EAAC,EAAE,CAAC,CAACmI,KAAK,CAAE;AACphD,CAAC,CAAC,GAAC,EAAE;MAAChK,CAAC,GAAC;QAACmL,IAAI,EAAC,OAAO;QAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;QAAC4O,MAAM,EAAC,EAAE;QAACC,KAAK,EAAC,EAAE;QAACC,IAAI,EAAC;MAAE,CAAC;IAAC,IAAG5O,CAAC,CAACuK,MAAM,KAAGrK,CAAC,CAACqK,MAAM,EAAC;MAAC,KAAI,IAAIN,CAAC,IAAI/J,CAAC,EAAC,IAAI,CAAC+L,KAAK,CAACb,KAAK,CAAClJ,eAAe,CAAC2H,IAAI,CAACI,CAAC,CAAC,GAAC7J,CAAC,CAACuO,KAAK,CAAChE,IAAI,CAAC,OAAO,CAAC,GAAC,IAAI,CAACsB,KAAK,CAACb,KAAK,CAACjJ,gBAAgB,CAAC0H,IAAI,CAACI,CAAC,CAAC,GAAC7J,CAAC,CAACuO,KAAK,CAAChE,IAAI,CAAC,QAAQ,CAAC,GAAC,IAAI,CAACsB,KAAK,CAACb,KAAK,CAAChJ,cAAc,CAACyH,IAAI,CAACI,CAAC,CAAC,GAAC7J,CAAC,CAACuO,KAAK,CAAChE,IAAI,CAAC,MAAM,CAAC,GAACvK,CAAC,CAACuO,KAAK,CAAChE,IAAI,CAAC,IAAI,CAAC;MAAC,KAAI,IAAIV,CAAC,GAAC,CAAC,EAACA,CAAC,GAACjK,CAAC,CAACuK,MAAM,EAACN,CAAC,EAAE,EAAC7J,CAAC,CAACsO,MAAM,CAAC/D,IAAI,CAAC;QAACzE,IAAI,EAAClG,CAAC,CAACiK,CAAC,CAAC;QAACwB,MAAM,EAAC,IAAI,CAACS,KAAK,CAACM,MAAM,CAACxM,CAAC,CAACiK,CAAC,CAAC,CAAC;QAACyE,MAAM,EAAC,CAAC,CAAC;QAACC,KAAK,EAACvO,CAAC,CAACuO,KAAK,CAAC1E,CAAC;MAAC,CAAC,CAAC;MAAC,KAAI,IAAIA,CAAC,IAAI9J,CAAC,EAACC,CAAC,CAACwO,IAAI,CAACjE,IAAI,CAACX,CAAC,CAACC,CAAC,EAAC7J,CAAC,CAACsO,MAAM,CAACnE,MAAM,CAAC,CAACsB,GAAG,CAAC,CAAC3B,CAAC,EAACC,CAAC,MAAI;QAACjE,IAAI,EAACgE,CAAC;QAACuB,MAAM,EAAC,IAAI,CAACS,KAAK,CAACM,MAAM,CAACtC,CAAC,CAAC;QAACwE,MAAM,EAAC,CAAC,CAAC;QAACC,KAAK,EAACvO,CAAC,CAACuO,KAAK,CAACxE,CAAC;MAAC,CAAC,CAAC,CAAC,CAAC;MAAC,OAAO/J,CAAC;IAAA;EAAC;EAACyF,QAAQA,CAAChG,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAACxG,QAAQ,CAAClG,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC,OAAM;MAACyL,IAAI,EAAC,SAAS;MAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;MAAC2M,KAAK,EAAC3M,CAAC,CAAC,CAAC,CAAC,CAAC+K,MAAM,CAAC,CAAC,CAAC,KAAG,GAAG,GAAC,CAAC,GAAC,CAAC;MAAC3E,IAAI,EAACpG,CAAC,CAAC,CAAC,CAAC;MAAC2L,MAAM,EAAC,IAAI,CAACS,KAAK,CAACM,MAAM,CAAC1M,CAAC,CAAC,CAAC,CAAC;IAAC,CAAC;EAAA;EAACkG,SAASA,CAACnG,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAACrG,SAAS,CAACrG,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,CAAC+K,MAAM,CAAC/K,CAAC,CAAC,CAAC,CAAC,CAACyK,MAAM,GAAC,CAAC,CAAC,KAAI;AAC5yB,CAAC,GAACzK,CAAC,CAAC,CAAC,CAAC,CAACgL,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,GAAChL,CAAC,CAAC,CAAC,CAAC;MAAC,OAAM;QAACyL,IAAI,EAAC,WAAW;QAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;QAACoG,IAAI,EAAClG,CAAC;QAACyL,MAAM,EAAC,IAAI,CAACS,KAAK,CAACM,MAAM,CAACxM,CAAC;MAAC,CAAC;IAAA;EAAC;EAACkG,IAAIA,CAACrG,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACI,KAAK,CAACnG,IAAI,CAACvG,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC,OAAM;MAACyL,IAAI,EAAC,MAAM;MAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;MAACoG,IAAI,EAACpG,CAAC,CAAC,CAAC,CAAC;MAAC2L,MAAM,EAAC,IAAI,CAACS,KAAK,CAACM,MAAM,CAAC1M,CAAC,CAAC,CAAC,CAAC;IAAC,CAAC;EAAA;EAAC8I,MAAMA,CAAC/I,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACO,MAAM,CAAC5D,MAAM,CAACjJ,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC,OAAM;MAACyL,IAAI,EAAC,QAAQ;MAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;MAACoG,IAAI,EAACpG,CAAC,CAAC,CAAC;IAAC,CAAC;EAAA;EAACoJ,GAAGA,CAACrJ,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACO,MAAM,CAACtD,GAAG,CAACvJ,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC,OAAM,CAAC,IAAI,CAACoM,KAAK,CAACb,KAAK,CAACC,MAAM,IAAE,IAAI,CAACW,KAAK,CAACb,KAAK,CAAC/I,SAAS,CAACwH,IAAI,CAAC/J,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,IAAI,CAACoM,KAAK,CAACb,KAAK,CAACC,MAAM,GAAC,CAAC,CAAC,GAAC,IAAI,CAACY,KAAK,CAACb,KAAK,CAACC,MAAM,IAAE,IAAI,CAACW,KAAK,CAACb,KAAK,CAAC9I,OAAO,CAACuH,IAAI,CAAC/J,CAAC,CAAC,CAAC,CAAC,CAAC,KAAG,IAAI,CAACoM,KAAK,CAACb,KAAK,CAACC,MAAM,GAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,CAACY,KAAK,CAACb,KAAK,CAACwD,UAAU,IAAE,IAAI,CAAC5C,KAAK,CAACb,KAAK,CAAC7I,iBAAiB,CAACsH,IAAI,CAAC/J,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,IAAI,CAACoM,KAAK,CAACb,KAAK,CAACwD,UAAU,GAAC,CAAC,CAAC,GAAC,IAAI,CAAC3C,KAAK,CAACb,KAAK,CAACwD,UAAU,IAAE,IAAI,CAAC5C,KAAK,CAACb,KAAK,CAAC5I,eAAe,CAACqH,IAAI,CAAC/J,CAAC,CAAC,CAAC,CAAC,CAAC,KAAG,IAAI,CAACoM,KAAK,CAACb,KAAK,CAACwD,UAAU,GAAC,CAAC,CAAC,CAAC,EAAC;MAACtD,IAAI,EAAC,MAAM;MAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;MAACwL,MAAM,EAAC,IAAI,CAACY,KAAK,CAACb,KAAK,CAACC,MAAM;MAACuD,UAAU,EAAC,IAAI,CAAC3C,KAAK,CAACb,KAAK,CAACwD,UAAU;MAACxC,KAAK,EAAC,CAAC,CAAC;MAACnG,IAAI,EAACpG,CAAC,CAAC,CAAC;IAAC,CAAC;EAAA;EAAC+I,IAAIA,CAAChJ,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACO,MAAM,CAAC3D,IAAI,CAAClJ,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,CAACuK,IAAI,CAAC,CAAC;MAAC,IAAG,CAAC,IAAI,CAAC2B,OAAO,CAAC9M,QAAQ,IAAE,IAAI,CAAC+M,KAAK,CAACb,KAAK,CAAC3I,iBAAiB,CAACoH,IAAI,CAAC7J,CAAC,CAAC,EAAC;QAAC,IAAG,CAAC,IAAI,CAACiM,KAAK,CAACb,KAAK,CAAC1I,eAAe,CAACmH,IAAI,CAAC7J,CAAC,CAAC,EAAC;QAAO,IAAII,CAAC,GAACwK,CAAC,CAAC5K,CAAC,CAAC8K,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,IAAI,CAAC;QAAC,IAAG,CAAC9K,CAAC,CAACuK,MAAM,GAACnK,CAAC,CAACmK,MAAM,IAAE,CAAC,KAAG,CAAC,EAAC;MAAM,CAAC,MAAI;QAAC,IAAInK,CAAC,GAAC2K,EAAE,CAACjL,CAAC,CAAC,CAAC,CAAC,EAAC,IAAI,CAAC;QAAC,IAAGM,CAAC,KAAG,CAAC,CAAC,EAAC;QAAO,IAAGA,CAAC,GAAC,CAAC,CAAC,EAAC;UAAC,IAAI8J,CAAC,GAAC,CAACpK,CAAC,CAAC,CAAC,CAAC,CAACkL,OAAO,CAAC,GAAG,CAAC,KAAG,CAAC,GAAC,CAAC,GAAC,CAAC,IAAElL,CAAC,CAAC,CAAC,CAAC,CAACyK,MAAM,GAACnK,CAAC;UAACN,CAAC,CAAC,CAAC,CAAC,GAACA,CAAC,CAAC,CAAC,CAAC,CAACqN,SAAS,CAAC,CAAC,EAAC/M,CAAC,CAAC,EAACN,CAAC,CAAC,CAAC,CAAC,GAACA,CAAC,CAAC,CAAC,CAAC,CAACqN,SAAS,CAAC,CAAC,EAACjD,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,EAACvK,CAAC,CAAC,CAAC,CAAC,GAAC,EAAE;QAAA;MAAC;MAAC,IAAII,CAAC,GAACJ,CAAC,CAAC,CAAC,CAAC;QAACK,CAAC,GAAC,EAAE;MAAC,IAAG,IAAI,CAAC6L,OAAO,CAAC9M,QAAQ,EAAC;QAAC,IAAIkB,CAAC,GAAC,IAAI,CAAC6L,KAAK,CAACb,KAAK,CAACzI,iBAAiB,CAAChD,IAAI,CAACO,CAAC,CAAC;QAACE,CAAC,KAAGF,CAAC,GAACE,CAAC,CAAC,CAAC,CAAC,EAACD,CAAC,GAACC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAA,CAAC,MAAKD,CAAC,GAACL,CAAC,CAAC,CAAC,CAAC,GAACA,CAAC,CAAC,CAAC,CAAC,CAACgL,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,GAAC,EAAE;MAAC,OAAO5K,CAAC,GAACA,CAAC,CAACmK,IAAI,CAAC,CAAC,EAAC,IAAI,CAAC4B,KAAK,CAACb,KAAK,CAAC3I,iBAAiB,CAACoH,IAAI,CAAC3J,CAAC,CAAC,KAAG,IAAI,CAAC8L,OAAO,CAAC9M,QAAQ,IAAE,CAAC,IAAI,CAAC+M,KAAK,CAACb,KAAK,CAAC1I,eAAe,CAACmH,IAAI,CAAC7J,CAAC,CAAC,GAACE,CAAC,GAACA,CAAC,CAAC4K,KAAK,CAAC,CAAC,CAAC,GAAC5K,CAAC,GAACA,CAAC,CAAC4K,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACG,EAAE,CAACnL,CAAC,EAAC;QAACoL,IAAI,EAAChL,CAAC,IAAEA,CAAC,CAACD,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACO,MAAM,CAACpE,cAAc,EAAC,IAAI,CAAC;QAAC+C,KAAK,EAAChL,CAAC,IAAEA,CAAC,CAACF,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACO,MAAM,CAACpE,cAAc,EAAC,IAAI;MAAC,CAAC,EAACtI,CAAC,CAAC,CAAC,CAAC,EAAC,IAAI,CAACoM,KAAK,EAAC,IAAI,CAACD,KAAK,CAAC;IAAA;EAAC;EAACjD,OAAOA,CAACnJ,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIE,CAAC;IAAC,IAAG,CAACA,CAAC,GAAC,IAAI,CAACiM,KAAK,CAACO,MAAM,CAACxD,OAAO,CAACrJ,IAAI,CAACE,CAAC,CAAC,MAAIG,CAAC,GAAC,IAAI,CAACiM,KAAK,CAACO,MAAM,CAAC1D,MAAM,CAACnJ,IAAI,CAACE,CAAC,CAAC,CAAC,EAAC;MAAC,IAAIK,CAAC,GAAC,CAACF,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAACjK,mBAAmB,EAAC,GAAG,CAAC;QAAChB,CAAC,GAACL,CAAC,CAACI,CAAC,CAACuO,WAAW,CAAC,CAAC,CAAC;MAAC,IAAG,CAACtO,CAAC,EAAC;QAAC,IAAIC,CAAC,GAACJ,CAAC,CAAC,CAAC,CAAC,CAAC6K,MAAM,CAAC,CAAC,CAAC;QAAC,OAAM;UAACU,IAAI,EAAC,MAAM;UAACC,GAAG,EAACpL,CAAC;UAAC8F,IAAI,EAAC9F;QAAC,CAAC;MAAA;MAAC,OAAO6K,EAAE,CAACjL,CAAC,EAACG,CAAC,EAACH,CAAC,CAAC,CAAC,CAAC,EAAC,IAAI,CAACkM,KAAK,EAAC,IAAI,CAACD,KAAK,CAAC;IAAA;EAAC;EAAC6C,QAAQA,CAACjP,CAAC,EAACC,CAAC,EAACE,CAAC,GAAC,EAAE,EAAC;IAAC,IAAIE,CAAC,GAAC,IAAI,CAAC+L,KAAK,CAACO,MAAM,CAAC/D,cAAc,CAAC9I,IAAI,CAACE,CAAC,CAAC;IAAC,IAAG,CAACK,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,IAAEF,CAAC,CAAC4L,KAAK,CAAC,IAAI,CAACK,KAAK,CAACb,KAAK,CAACxI,mBAAmB,CAAC,EAAC;IAAO,IAAG,EAAE1C,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,IAAE,EAAE,CAAC,IAAE,CAACF,CAAC,IAAE,IAAI,CAACiM,KAAK,CAACO,MAAM,CAACzD,WAAW,CAACpJ,IAAI,CAACK,CAAC,CAAC,EAAC;MAAC,IAAII,CAAC,GAAC,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACqK,MAAM,GAAC,CAAC;QAACN,CAAC;QAACC,CAAC;QAACC,CAAC,GAAC/J,CAAC;QAACsM,CAAC,GAAC,CAAC;QAACC,CAAC,GAACzM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAG,GAAG,GAAC,IAAI,CAAC+L,KAAK,CAACO,MAAM,CAAC9D,iBAAiB,GAAC,IAAI,CAACuD,KAAK,CAACO,MAAM,CAAC7D,iBAAiB;MAAC,KAAIgE,CAAC,CAACoC,SAAS,GAAC,CAAC,EAACjP,CAAC,GAACA,CAAC,CAACgL,KAAK,CAAC,CAAC,CAAC,GAACjL,CAAC,CAAC0K,MAAM,GAACnK,CAAC,CAAC,EAAC,CAACF,CAAC,GAACyM,CAAC,CAAChN,IAAI,CAACG,CAAC,CAAC,KAAG,IAAI,GAAE;QAAC,IAAGmK,CAAC,GAAC/J,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC+J,CAAC,EAAC;QAAS,IAAGC,CAAC,GAAC,CAAC,GAAGD,CAAC,CAAC,CAACM,MAAM,EAACrK,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,EAAC;UAACiK,CAAC,IAAED,CAAC;UAAC;QAAQ,CAAC,MAAK,IAAG,CAAChK,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,KAAGE,CAAC,GAAC,CAAC,IAAE,EAAE,CAACA,CAAC,GAAC8J,CAAC,IAAE,CAAC,CAAC,EAAC;UAACwC,CAAC,IAAExC,CAAC;UAAC;QAAQ;QAAC,IAAGC,CAAC,IAAED,CAAC,EAACC,CAAC,GAAC,CAAC,EAAC;QAASD,CAAC,GAACtG,IAAI,CAACC,GAAG,CAACqG,CAAC,EAACA,CAAC,GAACC,CAAC,GAACuC,CAAC,CAAC;QAAC,IAAIE,CAAC,GAAC,CAAC,GAAG1M,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACqK,MAAM;UAACwC,CAAC,GAAClN,CAAC,CAACiL,KAAK,CAAC,CAAC,EAAC1K,CAAC,GAACF,CAAC,CAAC8O,KAAK,GAACpC,CAAC,GAAC1C,CAAC,CAAC;QAAC,IAAGtG,IAAI,CAACC,GAAG,CAACzD,CAAC,EAAC8J,CAAC,CAAC,GAAC,CAAC,EAAC;UAAC,IAAI+C,CAAC,GAACF,CAAC,CAACjC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;UAAC,OAAM;YAACS,IAAI,EAAC,IAAI;YAACC,GAAG,EAACuB,CAAC;YAAC7G,IAAI,EAAC+G,CAAC;YAACxB,MAAM,EAAC,IAAI,CAACS,KAAK,CAACR,YAAY,CAACuB,CAAC;UAAC,CAAC;QAAA;QAAC,IAAID,CAAC,GAACD,CAAC,CAACjC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;QAAC,OAAM;UAACS,IAAI,EAAC,QAAQ;UAACC,GAAG,EAACuB,CAAC;UAAC7G,IAAI,EAAC8G,CAAC;UAACvB,MAAM,EAAC,IAAI,CAACS,KAAK,CAACR,YAAY,CAACsB,CAAC;QAAC,CAAC;MAAA;IAAC;EAAC;EAACiC,QAAQA,CAACpP,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACO,MAAM,CAACjH,IAAI,CAAC5F,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,IAAI,CAACgM,KAAK,CAACb,KAAK,CAACnK,iBAAiB,EAAC,GAAG,CAAC;QAACf,CAAC,GAAC,IAAI,CAAC+L,KAAK,CAACb,KAAK,CAACpK,YAAY,CAAC6I,IAAI,CAAC7J,CAAC,CAAC;QAACG,CAAC,GAAC,IAAI,CAAC8L,KAAK,CAACb,KAAK,CAACtK,iBAAiB,CAAC+I,IAAI,CAAC7J,CAAC,CAAC,IAAE,IAAI,CAACiM,KAAK,CAACb,KAAK,CAACrK,eAAe,CAAC8I,IAAI,CAAC7J,CAAC,CAAC;MAAC,OAAOE,CAAC,IAAEC,CAAC,KAAGH,CAAC,GAACA,CAAC,CAACmN,SAAS,CAAC,CAAC,EAACnN,CAAC,CAACuK,MAAM,GAAC,CAAC,CAAC,CAAC,EAAC;QAACgB,IAAI,EAAC,UAAU;QAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;QAACoG,IAAI,EAAClG;MAAC,CAAC;IAAA;EAAC;EAACuI,EAAEA,CAAC1I,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACO,MAAM,CAACjE,EAAE,CAAC5I,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC,OAAM;MAACyL,IAAI,EAAC,IAAI;MAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC;IAAC,CAAC;EAAA;EAAC0I,GAAGA,CAAC3I,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACO,MAAM,CAAChE,GAAG,CAAC7I,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC,OAAM;MAACyL,IAAI,EAAC,KAAK;MAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;MAACoG,IAAI,EAACpG,CAAC,CAAC,CAAC,CAAC;MAAC2L,MAAM,EAAC,IAAI,CAACS,KAAK,CAACR,YAAY,CAAC5L,CAAC,CAAC,CAAC,CAAC;IAAC,CAAC;EAAA;EAACuI,QAAQA,CAACxI,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACO,MAAM,CAACnE,QAAQ,CAAC1I,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,EAACE,CAAC;MAAC,OAAOJ,CAAC,CAAC,CAAC,CAAC,KAAG,GAAG,IAAEE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,EAACI,CAAC,GAAC,SAAS,GAACF,CAAC,KAAGA,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,EAACI,CAAC,GAACF,CAAC,CAAC,EAAC;QAACuL,IAAI,EAAC,MAAM;QAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;QAACoG,IAAI,EAAClG,CAAC;QAACkL,IAAI,EAAChL,CAAC;QAACuL,MAAM,EAAC,CAAC;UAACF,IAAI,EAAC,MAAM;UAACC,GAAG,EAACxL,CAAC;UAACkG,IAAI,EAAClG;QAAC,CAAC;MAAC,CAAC;IAAA;EAAC;EAACmJ,GAAGA,CAACtJ,CAAC,EAAC;IAAC,IAAIC,CAAC;IAAC,IAAGA,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACO,MAAM,CAACrD,GAAG,CAACxJ,IAAI,CAACE,CAAC,CAAC,EAAC;MAAC,IAAIG,CAAC,EAACE,CAAC;MAAC,IAAGJ,CAAC,CAAC,CAAC,CAAC,KAAG,GAAG,EAACE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,EAACI,CAAC,GAAC,SAAS,GAACF,CAAC,CAAC,KAAI;QAAC,IAAIG,CAAC;QAAC,GAAGA,CAAC,GAACL,CAAC,CAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACO,MAAM,CAACrE,UAAU,CAACxI,IAAI,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAE,EAAE,CAAC,QAAMK,CAAC,KAAGL,CAAC,CAAC,CAAC,CAAC;QAAEE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,KAAG,MAAM,GAACI,CAAC,GAAC,SAAS,GAACJ,CAAC,CAAC,CAAC,CAAC,GAACI,CAAC,GAACJ,CAAC,CAAC,CAAC,CAAC;MAAA;MAAC,OAAM;QAACyL,IAAI,EAAC,MAAM;QAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;QAACoG,IAAI,EAAClG,CAAC;QAACkL,IAAI,EAAChL,CAAC;QAACuL,MAAM,EAAC,CAAC;UAACF,IAAI,EAAC,MAAM;UAACC,GAAG,EAACxL,CAAC;UAACkG,IAAI,EAAClG;QAAC,CAAC;MAAC,CAAC;IAAA;EAAC;EAACkP,UAAUA,CAACrP,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACmM,KAAK,CAACO,MAAM,CAACtG,IAAI,CAACvG,IAAI,CAACE,CAAC,CAAC;IAAC,IAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAAC,IAAI,CAACkM,KAAK,CAACb,KAAK,CAACwD,UAAU;MAAC,OAAM;QAACtD,IAAI,EAAC,MAAM;QAACC,GAAG,EAAC1L,CAAC,CAAC,CAAC,CAAC;QAACoG,IAAI,EAACpG,CAAC,CAAC,CAAC,CAAC;QAACqP,OAAO,EAACnP;MAAC,CAAC;IAAA;EAAC;AAAC,CAAC;AAAC,IAAIoP,CAAC,GAAC,MAAM3P,CAAC;EAACgM,MAAM;EAACO,OAAO;EAACX,KAAK;EAAChM,SAAS;EAACgQ,WAAW;EAAClD,WAAWA,CAACtM,CAAC,EAAC;IAAC,IAAI,CAAC4L,MAAM,GAAC,EAAE,EAAC,IAAI,CAACA,MAAM,CAAC6D,KAAK,GAACC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,EAAC,IAAI,CAACxD,OAAO,GAACnM,CAAC,IAAEN,CAAC,EAAC,IAAI,CAACyM,OAAO,CAAC3M,SAAS,GAAC,IAAI,CAAC2M,OAAO,CAAC3M,SAAS,IAAE,IAAI0M,CAAC,CAAD,CAAC,EAAC,IAAI,CAAC1M,SAAS,GAAC,IAAI,CAAC2M,OAAO,CAAC3M,SAAS,EAAC,IAAI,CAACA,SAAS,CAAC2M,OAAO,GAAC,IAAI,CAACA,OAAO,EAAC,IAAI,CAAC3M,SAAS,CAAC6M,KAAK,GAAC,IAAI,EAAC,IAAI,CAACmD,WAAW,GAAC,EAAE,EAAC,IAAI,CAAChE,KAAK,GAAC;MAACC,MAAM,EAAC,CAAC,CAAC;MAACuD,UAAU,EAAC,CAAC,CAAC;MAAChC,GAAG,EAAC,CAAC;IAAC,CAAC;IAAC,IAAI/M,CAAC,GAAC;MAACsL,KAAK,EAAC/K,CAAC;MAACgM,KAAK,EAAC9C,CAAC,CAACC,MAAM;MAACgD,MAAM,EAAC/C,CAAC,CAACD;IAAM,CAAC;IAAC,IAAI,CAACwC,OAAO,CAAC9M,QAAQ,IAAEY,CAAC,CAACuM,KAAK,GAAC9C,CAAC,CAACrK,QAAQ,EAACY,CAAC,CAAC0M,MAAM,GAAC/C,CAAC,CAACvK,QAAQ,IAAE,IAAI,CAAC8M,OAAO,CAAChN,GAAG,KAAGc,CAAC,CAACuM,KAAK,GAAC9C,CAAC,CAACvK,GAAG,EAAC,IAAI,CAACgN,OAAO,CAAClN,MAAM,GAACgB,CAAC,CAAC0M,MAAM,GAAC/C,CAAC,CAAC3K,MAAM,GAACgB,CAAC,CAAC0M,MAAM,GAAC/C,CAAC,CAACzK,GAAG,CAAC,EAAC,IAAI,CAACK,SAAS,CAAC4M,KAAK,GAACnM,CAAC;EAAA;EAAC,WAAWmM,KAAKA,CAAA,EAAE;IAAC,OAAM;MAACI,KAAK,EAAC9C,CAAC;MAACiD,MAAM,EAAC/C;IAAC,CAAC;EAAA;EAAC,OAAOgG,GAAGA,CAAC5P,CAAC,EAACC,CAAC,EAAC;IAAC,OAAO,IAAIL,CAAC,CAACK,CAAC,CAAC,CAAC2P,GAAG,CAAC5P,CAAC,CAAC;EAAA;EAAC,OAAO6P,SAASA,CAAC7P,CAAC,EAACC,CAAC,EAAC;IAAC,OAAO,IAAIL,CAAC,CAACK,CAAC,CAAC,CAAC4L,YAAY,CAAC7L,CAAC,CAAC;EAAA;EAAC4P,GAAGA,CAAC5P,CAAC,EAAC;IAACA,CAAC,GAACA,CAAC,CAACI,OAAO,CAACI,CAAC,CAACiD,cAAc,EAAE;AACzqJ,CAAC,CAAC,EAAC,IAAI,CAACwJ,WAAW,CAACjN,CAAC,EAAC,IAAI,CAAC4L,MAAM,CAAC;IAAC,KAAI,IAAI3L,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACuP,WAAW,CAAC9E,MAAM,EAACzK,CAAC,EAAE,EAAC;MAAC,IAAIE,CAAC,GAAC,IAAI,CAACqP,WAAW,CAACvP,CAAC,CAAC;MAAC,IAAI,CAAC4L,YAAY,CAAC1L,CAAC,CAAC2P,GAAG,EAAC3P,CAAC,CAACyL,MAAM,CAAC;IAAA;IAAC,OAAO,IAAI,CAAC4D,WAAW,GAAC,EAAE,EAAC,IAAI,CAAC5D,MAAM;EAAA;EAACqB,WAAWA,CAACjN,CAAC,EAACC,CAAC,GAAC,EAAE,EAACE,CAAC,GAAC,CAAC,CAAC,EAAC;IAAC,KAAI,IAAI,CAACgM,OAAO,CAAC9M,QAAQ,KAAGW,CAAC,GAACA,CAAC,CAACI,OAAO,CAACI,CAAC,CAACa,aAAa,EAAC,MAAM,CAAC,CAACjB,OAAO,CAACI,CAAC,CAACkD,SAAS,EAAC,EAAE,CAAC,CAAC,EAAC1D,CAAC,GAAE;MAAC,IAAIK,CAAC;MAAC,IAAG,IAAI,CAAC8L,OAAO,CAACjN,UAAU,EAAEsN,KAAK,EAAEkC,IAAI,CAACnO,CAAC,IAAE,CAACF,CAAC,GAACE,CAAC,CAACwP,IAAI,CAAC;QAAC1D,KAAK,EAAC;MAAI,CAAC,EAACrM,CAAC,EAACC,CAAC,CAAC,KAAGD,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC,EAAC,CAAC,CAAC,IAAE,CAAC,CAAC,CAAC,EAAC;MAAS,IAAGA,CAAC,GAAC,IAAI,CAACb,SAAS,CAAC+M,KAAK,CAACvM,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC;QAAC,IAAInK,CAAC,GAACN,CAAC,CAAC0K,EAAE,CAAC,CAAC,CAAC,CAAC;QAACtK,CAAC,CAACsL,GAAG,CAACjB,MAAM,KAAG,CAAC,IAAEnK,CAAC,KAAG,KAAK,CAAC,GAACA,CAAC,CAACoL,GAAG,IAAG;AAC3hB,CAAC,GAAC1L,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAACb,SAAS,CAACkG,IAAI,CAAC1F,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC;QAAC,IAAInK,CAAC,GAACN,CAAC,CAAC0K,EAAE,CAAC,CAAC,CAAC,CAAC;QAACpK,CAAC,EAAEmL,IAAI,KAAG,WAAW,IAAEnL,CAAC,EAAEmL,IAAI,KAAG,MAAM,IAAEnL,CAAC,CAACoL,GAAG,IAAG;AAC9I,CAAC,GAACtL,CAAC,CAACsL,GAAG,EAACpL,CAAC,CAAC8F,IAAI,IAAG;AACjB,CAAC,GAAChG,CAAC,CAACgG,IAAI,EAAC,IAAI,CAACmJ,WAAW,CAAC7E,EAAE,CAAC,CAAC,CAAC,CAAC,CAACmF,GAAG,GAACvP,CAAC,CAAC8F,IAAI,IAAEpG,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAACb,SAAS,CAACoG,MAAM,CAAC5F,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAACb,SAAS,CAACqG,OAAO,CAAC7F,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAACb,SAAS,CAACsG,EAAE,CAAC9F,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAACb,SAAS,CAACiG,UAAU,CAACzF,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAACb,SAAS,CAACyG,IAAI,CAACjG,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAACb,SAAS,CAACuG,IAAI,CAAC/F,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAACb,SAAS,CAACmG,GAAG,CAAC3F,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC;QAAC,IAAInK,CAAC,GAACN,CAAC,CAAC0K,EAAE,CAAC,CAAC,CAAC,CAAC;QAACpK,CAAC,EAAEmL,IAAI,KAAG,WAAW,IAAEnL,CAAC,EAAEmL,IAAI,KAAG,MAAM,IAAEnL,CAAC,CAACoL,GAAG,IAAG;AACzoB,CAAC,GAACtL,CAAC,CAACsL,GAAG,EAACpL,CAAC,CAAC8F,IAAI,IAAG;AACjB,CAAC,GAAChG,CAAC,CAACsL,GAAG,EAAC,IAAI,CAAC6D,WAAW,CAAC7E,EAAE,CAAC,CAAC,CAAC,CAAC,CAACmF,GAAG,GAACvP,CAAC,CAAC8F,IAAI,IAAE,IAAI,CAACuF,MAAM,CAAC6D,KAAK,CAACpP,CAAC,CAACgJ,GAAG,CAAC,KAAG,IAAI,CAACuC,MAAM,CAAC6D,KAAK,CAACpP,CAAC,CAACgJ,GAAG,CAAC,GAAC;UAACgC,IAAI,EAAChL,CAAC,CAACgL,IAAI;UAACC,KAAK,EAACjL,CAAC,CAACiL;QAAK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGjL,CAAC,GAAC,IAAI,CAACb,SAAS,CAAC4G,KAAK,CAACpG,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAACb,SAAS,CAACwG,QAAQ,CAAChG,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAIC,CAAC,GAACN,CAAC;MAAC,IAAG,IAAI,CAACmM,OAAO,CAACjN,UAAU,EAAE8Q,UAAU,EAAC;QAAC,IAAIzP,CAAC,GAAC,CAAC,GAAC,CAAC;UAAC6J,CAAC,GAACpK,CAAC,CAACiL,KAAK,CAAC,CAAC,CAAC;UAACZ,CAAC;QAAC,IAAI,CAAC8B,OAAO,CAACjN,UAAU,CAAC8Q,UAAU,CAACC,OAAO,CAAC3F,CAAC,IAAE;UAACD,CAAC,GAACC,CAAC,CAACyF,IAAI,CAAC;YAAC1D,KAAK,EAAC;UAAI,CAAC,EAACjC,CAAC,CAAC,EAAC,OAAOC,CAAC,IAAE,QAAQ,IAAEA,CAAC,IAAE,CAAC,KAAG9J,CAAC,GAACwD,IAAI,CAACC,GAAG,CAACzD,CAAC,EAAC8J,CAAC,CAAC,CAAC;QAAA,CAAC,CAAC,EAAC9J,CAAC,GAAC,CAAC,GAAC,CAAC,IAAEA,CAAC,IAAE,CAAC,KAAGD,CAAC,GAACN,CAAC,CAACsN,SAAS,CAAC,CAAC,EAAC/M,CAAC,GAAC,CAAC,CAAC,CAAC;MAAA;MAAC,IAAG,IAAI,CAACiL,KAAK,CAACwB,GAAG,KAAG3M,CAAC,GAAC,IAAI,CAACb,SAAS,CAAC2G,SAAS,CAAC7F,CAAC,CAAC,CAAC,EAAC;QAAC,IAAIC,CAAC,GAACN,CAAC,CAAC0K,EAAE,CAAC,CAAC,CAAC,CAAC;QAACxK,CAAC,IAAEI,CAAC,EAAEmL,IAAI,KAAG,WAAW,IAAEnL,CAAC,CAACoL,GAAG,IAAG;AAC3mB,CAAC,GAACtL,CAAC,CAACsL,GAAG,EAACpL,CAAC,CAAC8F,IAAI,IAAG;AACjB,CAAC,GAAChG,CAAC,CAACgG,IAAI,EAAC,IAAI,CAACmJ,WAAW,CAAC5E,GAAG,CAAC,CAAC,EAAC,IAAI,CAAC4E,WAAW,CAAC7E,EAAE,CAAC,CAAC,CAAC,CAAC,CAACmF,GAAG,GAACvP,CAAC,CAAC8F,IAAI,IAAEpG,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC,EAACF,CAAC,GAACG,CAAC,CAACoK,MAAM,KAAG1K,CAAC,CAAC0K,MAAM,EAAC1K,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC;QAAC;MAAQ;MAAC,IAAGrK,CAAC,GAAC,IAAI,CAACb,SAAS,CAAC6G,IAAI,CAACrG,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAACjN,CAAC,CAACsL,GAAG,CAACjB,MAAM,CAAC;QAAC,IAAInK,CAAC,GAACN,CAAC,CAAC0K,EAAE,CAAC,CAAC,CAAC,CAAC;QAACpK,CAAC,EAAEmL,IAAI,KAAG,MAAM,IAAEnL,CAAC,CAACoL,GAAG,IAAG;AAC3O,CAAC,GAACtL,CAAC,CAACsL,GAAG,EAACpL,CAAC,CAAC8F,IAAI,IAAG;AACjB,CAAC,GAAChG,CAAC,CAACgG,IAAI,EAAC,IAAI,CAACmJ,WAAW,CAAC5E,GAAG,CAAC,CAAC,EAAC,IAAI,CAAC4E,WAAW,CAAC7E,EAAE,CAAC,CAAC,CAAC,CAAC,CAACmF,GAAG,GAACvP,CAAC,CAAC8F,IAAI,IAAEpG,CAAC,CAAC6K,IAAI,CAACzK,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGL,CAAC,EAAC;QAAC,IAAIO,CAAC,GAAC,yBAAyB,GAACP,CAAC,CAACkQ,UAAU,CAAC,CAAC,CAAC;QAAC,IAAG,IAAI,CAAC/D,OAAO,CAAC5M,MAAM,EAAC;UAAC4Q,OAAO,CAACC,KAAK,CAAC7P,CAAC,CAAC;UAAC;QAAK,CAAC,MAAK,MAAM,IAAI8P,KAAK,CAAC9P,CAAC,CAAC;MAAA;IAAC;IAAC,OAAO,IAAI,CAACiL,KAAK,CAACwB,GAAG,GAAC,CAAC,CAAC,EAAC/M,CAAC;EAAA;EAAC0M,MAAMA,CAAC3M,CAAC,EAACC,CAAC,GAAC,EAAE,EAAC;IAAC,OAAO,IAAI,CAACuP,WAAW,CAAC1E,IAAI,CAAC;MAACgF,GAAG,EAAC9P,CAAC;MAAC4L,MAAM,EAAC3L;IAAC,CAAC,CAAC,EAACA,CAAC;EAAA;EAAC4L,YAAYA,CAAC7L,CAAC,EAACC,CAAC,GAAC,EAAE,EAAC;IAAC,IAAIE,CAAC,GAACH,CAAC;MAACK,CAAC,GAAC,IAAI;IAAC,IAAG,IAAI,CAACuL,MAAM,CAAC6D,KAAK,EAAC;MAAC,IAAIrF,CAAC,GAACsF,MAAM,CAACY,IAAI,CAAC,IAAI,CAAC1E,MAAM,CAAC6D,KAAK,CAAC;MAAC,IAAGrF,CAAC,CAACM,MAAM,GAAC,CAAC,EAAC,OAAK,CAACrK,CAAC,GAAC,IAAI,CAACb,SAAS,CAAC4M,KAAK,CAACO,MAAM,CAACvD,aAAa,CAACtJ,IAAI,CAACK,CAAC,CAAC,KAAG,IAAI,GAAEiK,CAAC,CAACmG,QAAQ,CAAClQ,CAAC,CAAC,CAAC,CAAC,CAAC4K,KAAK,CAAC5K,CAAC,CAAC,CAAC,CAAC,CAACmQ,WAAW,CAAC,GAAG,CAAC,GAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,KAAGrQ,CAAC,GAACA,CAAC,CAAC8K,KAAK,CAAC,CAAC,EAAC5K,CAAC,CAAC8O,KAAK,CAAC,GAAC,GAAG,GAAC,GAAG,CAACvB,MAAM,CAACvN,CAAC,CAAC,CAAC,CAAC,CAACqK,MAAM,GAAC,CAAC,CAAC,GAAC,GAAG,GAACvK,CAAC,CAAC8K,KAAK,CAAC,IAAI,CAACzL,SAAS,CAAC4M,KAAK,CAACO,MAAM,CAACvD,aAAa,CAAC8F,SAAS,CAAC,CAAC;IAAA;IAAC,OAAK,CAAC7O,CAAC,GAAC,IAAI,CAACb,SAAS,CAAC4M,KAAK,CAACO,MAAM,CAACpE,cAAc,CAACzI,IAAI,CAACK,CAAC,CAAC,KAAG,IAAI,GAAEA,CAAC,GAACA,CAAC,CAAC8K,KAAK,CAAC,CAAC,EAAC5K,CAAC,CAAC8O,KAAK,CAAC,GAAC,IAAI,GAAChP,CAAC,CAAC8K,KAAK,CAAC,IAAI,CAACzL,SAAS,CAAC4M,KAAK,CAACO,MAAM,CAACpE,cAAc,CAAC2G,SAAS,CAAC;IAAC,OAAK,CAAC7O,CAAC,GAAC,IAAI,CAACb,SAAS,CAAC4M,KAAK,CAACO,MAAM,CAAClE,SAAS,CAAC3I,IAAI,CAACK,CAAC,CAAC,KAAG,IAAI,GAAEA,CAAC,GAACA,CAAC,CAAC8K,KAAK,CAAC,CAAC,EAAC5K,CAAC,CAAC8O,KAAK,CAAC,GAAC,GAAG,GAAC,GAAG,CAACvB,MAAM,CAACvN,CAAC,CAAC,CAAC,CAAC,CAACqK,MAAM,GAAC,CAAC,CAAC,GAAC,GAAG,GAACvK,CAAC,CAAC8K,KAAK,CAAC,IAAI,CAACzL,SAAS,CAAC4M,KAAK,CAACO,MAAM,CAAClE,SAAS,CAACyG,SAAS,CAAC;IAAC,IAAI5O,CAAC,GAAC,CAAC,CAAC;MAACC,CAAC,GAAC,EAAE;IAAC,OAAKP,CAAC,GAAE;MAACM,CAAC,KAAGC,CAAC,GAAC,EAAE,CAAC,EAACD,CAAC,GAAC,CAAC,CAAC;MAAC,IAAI8J,CAAC;MAAC,IAAG,IAAI,CAAC+B,OAAO,CAACjN,UAAU,EAAEyN,MAAM,EAAE+B,IAAI,CAACpE,CAAC,IAAE,CAACF,CAAC,GAACE,CAAC,CAACyF,IAAI,CAAC;QAAC1D,KAAK,EAAC;MAAI,CAAC,EAACrM,CAAC,EAACC,CAAC,CAAC,KAAGD,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC,EAAC,CAAC,CAAC,IAAE,CAAC,CAAC,CAAC,EAAC;MAAS,IAAGA,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAACuJ,MAAM,CAAC/I,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAAC6J,GAAG,CAACrJ,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAACwJ,IAAI,CAAChJ,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAAC2J,OAAO,CAACnJ,CAAC,EAAC,IAAI,CAAC4L,MAAM,CAAC6D,KAAK,CAAC,EAAC;QAACzP,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC;QAAC,IAAIJ,CAAC,GAACrK,CAAC,CAAC0K,EAAE,CAAC,CAAC,CAAC,CAAC;QAACP,CAAC,CAACsB,IAAI,KAAG,MAAM,IAAEpB,CAAC,EAAEoB,IAAI,KAAG,MAAM,IAAEpB,CAAC,CAACqB,GAAG,IAAEvB,CAAC,CAACuB,GAAG,EAACrB,CAAC,CAACjE,IAAI,IAAE+D,CAAC,CAAC/D,IAAI,IAAEpG,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAACyP,QAAQ,CAACjP,CAAC,EAACG,CAAC,EAACI,CAAC,CAAC,EAAC;QAACP,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAAC4P,QAAQ,CAACpP,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAACkJ,EAAE,CAAC1I,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAACmJ,GAAG,CAAC3I,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGA,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAACgJ,QAAQ,CAACxI,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAG,CAAC,IAAI,CAACoB,KAAK,CAACC,MAAM,KAAGrB,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAAC8J,GAAG,CAACtJ,CAAC,CAAC,CAAC,EAAC;QAACA,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACzK,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAIC,CAAC,GAACrK,CAAC;MAAC,IAAG,IAAI,CAACmM,OAAO,CAACjN,UAAU,EAAEuR,WAAW,EAAC;QAAC,IAAInG,CAAC,GAAC,CAAC,GAAC,CAAC;UAACuC,CAAC,GAAC7M,CAAC,CAACiL,KAAK,CAAC,CAAC,CAAC;UAAC6B,CAAC;QAAC,IAAI,CAACX,OAAO,CAACjN,UAAU,CAACuR,WAAW,CAACR,OAAO,CAAClD,CAAC,IAAE;UAACD,CAAC,GAACC,CAAC,CAACgD,IAAI,CAAC;YAAC1D,KAAK,EAAC;UAAI,CAAC,EAACQ,CAAC,CAAC,EAAC,OAAOC,CAAC,IAAE,QAAQ,IAAEA,CAAC,IAAE,CAAC,KAAGxC,CAAC,GAACvG,IAAI,CAACC,GAAG,CAACsG,CAAC,EAACwC,CAAC,CAAC,CAAC;QAAA,CAAC,CAAC,EAACxC,CAAC,GAAC,CAAC,GAAC,CAAC,IAAEA,CAAC,IAAE,CAAC,KAAGD,CAAC,GAACrK,CAAC,CAACsN,SAAS,CAAC,CAAC,EAAChD,CAAC,GAAC,CAAC,CAAC,CAAC;MAAA;MAAC,IAAGF,CAAC,GAAC,IAAI,CAAC5K,SAAS,CAAC6P,UAAU,CAAChF,CAAC,CAAC,EAAC;QAACrK,CAAC,GAACA,CAAC,CAACsN,SAAS,CAAClD,CAAC,CAACuB,GAAG,CAACjB,MAAM,CAAC,EAACN,CAAC,CAACuB,GAAG,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,KAAG,GAAG,KAAG1K,CAAC,GAAC6J,CAAC,CAACuB,GAAG,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC3K,CAAC,GAAC,CAAC,CAAC;QAAC,IAAIgK,CAAC,GAACrK,CAAC,CAAC0K,EAAE,CAAC,CAAC,CAAC,CAAC;QAACL,CAAC,EAAEoB,IAAI,KAAG,MAAM,IAAEpB,CAAC,CAACqB,GAAG,IAAEvB,CAAC,CAACuB,GAAG,EAACrB,CAAC,CAACjE,IAAI,IAAE+D,CAAC,CAAC/D,IAAI,IAAEpG,CAAC,CAAC6K,IAAI,CAACV,CAAC,CAAC;QAAC;MAAQ;MAAC,IAAGpK,CAAC,EAAC;QAAC,IAAIsK,CAAC,GAAC,yBAAyB,GAACtK,CAAC,CAACkQ,UAAU,CAAC,CAAC,CAAC;QAAC,IAAG,IAAI,CAAC/D,OAAO,CAAC5M,MAAM,EAAC;UAAC4Q,OAAO,CAACC,KAAK,CAAC9F,CAAC,CAAC;UAAC;QAAK,CAAC,MAAK,MAAM,IAAI+F,KAAK,CAAC/F,CAAC,CAAC;MAAA;IAAC;IAAC,OAAOrK,CAAC;EAAA;AAAC,CAAC;AAAC,IAAIyQ,CAAC,GAAC,MAAK;EAACvE,OAAO;EAACwE,MAAM;EAACrE,WAAWA,CAACtM,CAAC,EAAC;IAAC,IAAI,CAACmM,OAAO,GAACnM,CAAC,IAAEN,CAAC;EAAA;EAAC6M,KAAKA,CAACvM,CAAC,EAAC;IAAC,OAAM,EAAE;EAAA;EAAC0F,IAAIA,CAAC;IAACW,IAAI,EAACrG,CAAC;IAAC0M,IAAI,EAACzM,CAAC;IAACqP,OAAO,EAACnP;EAAC,CAAC,EAAC;IAAC,IAAIE,CAAC,GAAC,CAACJ,CAAC,IAAE,EAAE,EAAE8L,KAAK,CAACvL,CAAC,CAACmD,aAAa,CAAC,GAAG,CAAC,CAAC;MAACrD,CAAC,GAACN,CAAC,CAACI,OAAO,CAACI,CAAC,CAACoD,aAAa,EAAC,EAAE,CAAC,GAAE;AACruF,CAAC;IAAC,OAAOvD,CAAC,GAAC,6BAA6B,GAAC0J,CAAC,CAAC1J,CAAC,CAAC,GAAC,IAAI,IAAEF,CAAC,GAACG,CAAC,GAACyJ,CAAC,CAACzJ,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,GAAE;AAClE,CAAC,GAAC,aAAa,IAAEH,CAAC,GAACG,CAAC,GAACyJ,CAAC,CAACzJ,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,GAAE;AAC/B,CAAC;EAAA;EAACmF,UAAUA,CAAC;IAACmG,MAAM,EAAC5L;EAAC,CAAC,EAAC;IAAC,OAAO;AAChC,EAAE,IAAI,CAAC2Q,MAAM,CAACC,KAAK,CAAC5Q,CAAC,CAAE;AACvB,CAAC;EAAA;EAAC+F,IAAIA,CAAC;IAACM,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAOA,CAAC;EAAA;EAAC6F,OAAOA,CAAC;IAAC+F,MAAM,EAAC5L,CAAC;IAAC4M,KAAK,EAAC3M;EAAC,CAAC,EAAC;IAAC,OAAO,KAAIA,CAAE,IAAG,IAAI,CAAC0Q,MAAM,CAACE,WAAW,CAAC7Q,CAAC,CAAE,MAAKC,CAAE;AACxG,CAAC;EAAA;EAAC6F,EAAEA,CAAC9F,CAAC,EAAC;IAAC,OAAO;AACf,CAAC;EAAA;EAACiG,IAAIA,CAACjG,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACuN,OAAO;MAACpN,CAAC,GAACH,CAAC,CAACwN,KAAK;MAACnN,CAAC,GAAC,EAAE;IAAC,KAAI,IAAI+J,CAAC,GAAC,CAAC,EAACA,CAAC,GAACpK,CAAC,CAAC0N,KAAK,CAAChD,MAAM,EAACN,CAAC,EAAE,EAAC;MAAC,IAAIC,CAAC,GAACrK,CAAC,CAAC0N,KAAK,CAACtD,CAAC,CAAC;MAAC/J,CAAC,IAAE,IAAI,CAACyQ,QAAQ,CAACzG,CAAC,CAAC;IAAA;IAAC,IAAI/J,CAAC,GAACL,CAAC,GAAC,IAAI,GAAC,IAAI;MAACM,CAAC,GAACN,CAAC,IAAEE,CAAC,KAAG,CAAC,GAAC,UAAU,GAACA,CAAC,GAAC,GAAG,GAAC,EAAE;IAAC,OAAM,GAAG,GAACG,CAAC,GAACC,CAAC,GAAE;AAChL,CAAC,GAACF,CAAC,GAAC,IAAI,GAACC,CAAC,GAAE;AACZ,CAAC;EAAA;EAACwQ,QAAQA,CAAC9Q,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,EAAE;IAAC,IAAGD,CAAC,CAACsO,IAAI,EAAC;MAAC,IAAInO,CAAC,GAAC,IAAI,CAAC4Q,QAAQ,CAAC;QAACxC,OAAO,EAAC,CAAC,CAACvO,CAAC,CAACuO;MAAO,CAAC,CAAC;MAACvO,CAAC,CAACyN,KAAK,GAACzN,CAAC,CAAC4L,MAAM,CAAC,CAAC,CAAC,EAAEF,IAAI,KAAG,WAAW,IAAE1L,CAAC,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAACvF,IAAI,GAAClG,CAAC,GAAC,GAAG,GAACH,CAAC,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAACvF,IAAI,EAACrG,CAAC,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,IAAE5L,CAAC,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAClB,MAAM,GAAC,CAAC,IAAE1K,CAAC,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAACF,IAAI,KAAG,MAAM,KAAG1L,CAAC,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAACvF,IAAI,GAAClG,CAAC,GAAC,GAAG,GAAC4J,CAAC,CAAC/J,CAAC,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAACvF,IAAI,CAAC,EAACrG,CAAC,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC0D,OAAO,GAAC,CAAC,CAAC,CAAC,IAAEtP,CAAC,CAAC4L,MAAM,CAACoF,OAAO,CAAC;QAACtF,IAAI,EAAC,MAAM;QAACC,GAAG,EAACxL,CAAC,GAAC,GAAG;QAACkG,IAAI,EAAClG,CAAC,GAAC,GAAG;QAACmP,OAAO,EAAC,CAAC;MAAC,CAAC,CAAC,GAACrP,CAAC,IAAEE,CAAC,GAAC,GAAG;IAAA;IAAC,OAAOF,CAAC,IAAE,IAAI,CAAC0Q,MAAM,CAACC,KAAK,CAAC5Q,CAAC,CAAC4L,MAAM,EAAC,CAAC,CAAC5L,CAAC,CAACyN,KAAK,CAAC,EAAE,OAAMxN,CAAE;AACzd,CAAC;EAAA;EAAC8Q,QAAQA,CAAC;IAACxC,OAAO,EAACvO;EAAC,CAAC,EAAC;IAAC,OAAM,SAAS,IAAEA,CAAC,GAAC,aAAa,GAAC,EAAE,CAAC,GAAC,8BAA8B;EAAA;EAACmG,SAASA,CAAC;IAACyF,MAAM,EAAC5L;EAAC,CAAC,EAAC;IAAC,OAAO,MAAK,IAAI,CAAC2Q,MAAM,CAACE,WAAW,CAAC7Q,CAAC,CAAE;AACzJ,CAAC;EAAA;EAACoG,KAAKA,CAACpG,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,EAAE;MAACE,CAAC,GAAC,EAAE;IAAC,KAAI,IAAIG,CAAC,GAAC,CAAC,EAACA,CAAC,GAACN,CAAC,CAAC6O,MAAM,CAACnE,MAAM,EAACpK,CAAC,EAAE,EAACH,CAAC,IAAE,IAAI,CAAC8Q,SAAS,CAACjR,CAAC,CAAC6O,MAAM,CAACvO,CAAC,CAAC,CAAC;IAACL,CAAC,IAAE,IAAI,CAACiR,QAAQ,CAAC;MAAC7K,IAAI,EAAClG;IAAC,CAAC,CAAC;IAAC,IAAIE,CAAC,GAAC,EAAE;IAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACN,CAAC,CAAC+O,IAAI,CAACrE,MAAM,EAACpK,CAAC,EAAE,EAAC;MAAC,IAAIC,CAAC,GAACP,CAAC,CAAC+O,IAAI,CAACzO,CAAC,CAAC;MAACH,CAAC,GAAC,EAAE;MAAC,KAAI,IAAIiK,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC7J,CAAC,CAACmK,MAAM,EAACN,CAAC,EAAE,EAACjK,CAAC,IAAE,IAAI,CAAC8Q,SAAS,CAAC1Q,CAAC,CAAC6J,CAAC,CAAC,CAAC;MAAC/J,CAAC,IAAE,IAAI,CAAC6Q,QAAQ,CAAC;QAAC7K,IAAI,EAAClG;MAAC,CAAC,CAAC;IAAA;IAAC,OAAOE,CAAC,KAAGA,CAAC,GAAE,UAASA,CAAE,UAAS,CAAC,EAAE;AACvS;AACA,CAAC,GAACJ,CAAC,GAAE;AACL,CAAC,GAACI,CAAC,GAAE;AACL,CAAC;EAAA;EAAC6Q,QAAQA,CAAC;IAAC7K,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAO;AAC5B,EAAEA,CAAE;AACJ,CAAC;EAAA;EAACiR,SAASA,CAACjR,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAAC0Q,MAAM,CAACE,WAAW,CAAC7Q,CAAC,CAAC4L,MAAM,CAAC;MAACzL,CAAC,GAACH,CAAC,CAAC6O,MAAM,GAAC,IAAI,GAAC,IAAI;IAAC,OAAM,CAAC7O,CAAC,CAAC8O,KAAK,GAAE,IAAG3O,CAAE,WAAUH,CAAC,CAAC8O,KAAM,IAAG,GAAE,IAAG3O,CAAE,GAAE,IAAEF,CAAC,GAAE,KAAIE,CAAE;AAC1I,CAAC;EAAA;EAACgR,MAAMA,CAAC;IAACvF,MAAM,EAAC5L;EAAC,CAAC,EAAC;IAAC,OAAO,WAAU,IAAI,CAAC2Q,MAAM,CAACE,WAAW,CAAC7Q,CAAC,CAAE,WAAU;EAAA;EAACoR,EAAEA,CAAC;IAACxF,MAAM,EAAC5L;EAAC,CAAC,EAAC;IAAC,OAAO,OAAM,IAAI,CAAC2Q,MAAM,CAACE,WAAW,CAAC7Q,CAAC,CAAE,OAAM;EAAA;EAACoP,QAAQA,CAAC;IAAC/I,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAO,SAAQ+J,CAAC,CAAC/J,CAAC,EAAC,CAAC,CAAC,CAAE,SAAQ;EAAA;EAAC0I,EAAEA,CAAC1I,CAAC,EAAC;IAAC,OAAM,MAAM;EAAA;EAAC2I,GAAGA,CAAC;IAACiD,MAAM,EAAC5L;EAAC,CAAC,EAAC;IAAC,OAAO,QAAO,IAAI,CAAC2Q,MAAM,CAACE,WAAW,CAAC7Q,CAAC,CAAE,QAAO;EAAA;EAACgJ,IAAIA,CAAC;IAACqC,IAAI,EAACrL,CAAC;IAACsL,KAAK,EAACrL,CAAC;IAAC2L,MAAM,EAACzL;EAAC,CAAC,EAAC;IAAC,IAAIE,CAAC,GAAC,IAAI,CAACsQ,MAAM,CAACE,WAAW,CAAC1Q,CAAC,CAAC;MAACG,CAAC,GAAC2J,CAAC,CAACjK,CAAC,CAAC;IAAC,IAAGM,CAAC,KAAG,IAAI,EAAC,OAAOD,CAAC;IAACL,CAAC,GAACM,CAAC;IAAC,IAAIC,CAAC,GAAC,WAAW,GAACP,CAAC,GAAC,GAAG;IAAC,OAAOC,CAAC,KAAGM,CAAC,IAAE,UAAU,GAACwJ,CAAC,CAAC9J,CAAC,CAAC,GAAC,GAAG,CAAC,EAACM,CAAC,IAAE,GAAG,GAACF,CAAC,GAAC,MAAM,EAACE,CAAC;EAAA;EAAC8Q,KAAKA,CAAC;IAAChG,IAAI,EAACrL,CAAC;IAACsL,KAAK,EAACrL,CAAC;IAACoG,IAAI,EAAClG,CAAC;IAACyL,MAAM,EAACvL;EAAC,CAAC,EAAC;IAACA,CAAC,KAAGF,CAAC,GAAC,IAAI,CAACwQ,MAAM,CAACE,WAAW,CAACxQ,CAAC,EAAC,IAAI,CAACsQ,MAAM,CAACW,YAAY,CAAC,CAAC;IAAC,IAAIhR,CAAC,GAAC2J,CAAC,CAACjK,CAAC,CAAC;IAAC,IAAGM,CAAC,KAAG,IAAI,EAAC,OAAOyJ,CAAC,CAAC5J,CAAC,CAAC;IAACH,CAAC,GAACM,CAAC;IAAC,IAAIC,CAAC,GAAE,aAAYP,CAAE,UAASG,CAAE,GAAE;IAAC,OAAOF,CAAC,KAAGM,CAAC,IAAG,WAAUwJ,CAAC,CAAC9J,CAAC,CAAE,GAAE,CAAC,EAACM,CAAC,IAAE,GAAG,EAACA,CAAC;EAAA;EAAC8F,IAAIA,CAACrG,CAAC,EAAC;IAAC,OAAM,QAAQ,IAAGA,CAAC,IAAEA,CAAC,CAAC4L,MAAM,GAAC,IAAI,CAAC+E,MAAM,CAACE,WAAW,CAAC7Q,CAAC,CAAC4L,MAAM,CAAC,GAAC,SAAS,IAAG5L,CAAC,IAAEA,CAAC,CAACsP,OAAO,GAACtP,CAAC,CAACqG,IAAI,GAAC0D,CAAC,CAAC/J,CAAC,CAACqG,IAAI,CAAC;EAAA;AAAC,CAAC;AAAC,IAAIkL,CAAC,GAAC,MAAK;EAACJ,MAAMA,CAAC;IAAC9K,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAOA,CAAC;EAAA;EAACoR,EAAEA,CAAC;IAAC/K,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAOA,CAAC;EAAA;EAACoP,QAAQA,CAAC;IAAC/I,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAOA,CAAC;EAAA;EAAC2I,GAAGA,CAAC;IAACtC,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAOA,CAAC;EAAA;EAAC+F,IAAIA,CAAC;IAACM,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAOA,CAAC;EAAA;EAACqG,IAAIA,CAAC;IAACA,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAOA,CAAC;EAAA;EAACgJ,IAAIA,CAAC;IAAC3C,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAM,EAAE,GAACA,CAAC;EAAA;EAACqR,KAAKA,CAAC;IAAChL,IAAI,EAACrG;EAAC,CAAC,EAAC;IAAC,OAAM,EAAE,GAACA,CAAC;EAAA;EAAC0I,EAAEA,CAAA,EAAE;IAAC,OAAM,EAAE;EAAA;AAAC,CAAC;AAAC,IAAI8I,CAAC,GAAC,MAAM5R,CAAC;EAACuM,OAAO;EAAC7M,QAAQ;EAACgS,YAAY;EAAChF,WAAWA,CAACtM,CAAC,EAAC;IAAC,IAAI,CAACmM,OAAO,GAACnM,CAAC,IAAEN,CAAC,EAAC,IAAI,CAACyM,OAAO,CAAC7M,QAAQ,GAAC,IAAI,CAAC6M,OAAO,CAAC7M,QAAQ,IAAE,IAAIoR,CAAC,CAAD,CAAC,EAAC,IAAI,CAACpR,QAAQ,GAAC,IAAI,CAAC6M,OAAO,CAAC7M,QAAQ,EAAC,IAAI,CAACA,QAAQ,CAAC6M,OAAO,GAAC,IAAI,CAACA,OAAO,EAAC,IAAI,CAAC7M,QAAQ,CAACqR,MAAM,GAAC,IAAI,EAAC,IAAI,CAACW,YAAY,GAAC,IAAIC,CAAC,CAAD,CAAC;EAAA;EAAC,OAAOX,KAAKA,CAAC5Q,CAAC,EAACC,CAAC,EAAC;IAAC,OAAO,IAAIL,CAAC,CAACK,CAAC,CAAC,CAAC2Q,KAAK,CAAC5Q,CAAC,CAAC;EAAA;EAAC,OAAO6Q,WAAWA,CAAC7Q,CAAC,EAACC,CAAC,EAAC;IAAC,OAAO,IAAIL,CAAC,CAACK,CAAC,CAAC,CAAC4Q,WAAW,CAAC7Q,CAAC,CAAC;EAAA;EAAC4Q,KAAKA,CAAC5Q,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,EAAC;IAAC,IAAIE,CAAC,GAAC,EAAE;IAAC,KAAI,IAAIE,CAAC,GAAC,CAAC,EAACA,CAAC,GAACL,CAAC,CAAC0K,MAAM,EAACrK,CAAC,EAAE,EAAC;MAAC,IAAIC,CAAC,GAACN,CAAC,CAACK,CAAC,CAAC;MAAC,IAAG,IAAI,CAAC8L,OAAO,CAACjN,UAAU,EAAEuS,SAAS,GAAGnR,CAAC,CAACoL,IAAI,CAAC,EAAC;QAAC,IAAItB,CAAC,GAAC9J,CAAC;UAAC+J,CAAC,GAAC,IAAI,CAAC8B,OAAO,CAACjN,UAAU,CAACuS,SAAS,CAACrH,CAAC,CAACsB,IAAI,CAAC,CAACqE,IAAI,CAAC;YAACY,MAAM,EAAC;UAAI,CAAC,EAACvG,CAAC,CAAC;QAAC,IAAGC,CAAC,KAAG,CAAC,CAAC,IAAE,CAAC,CAAC,OAAO,EAAC,IAAI,EAAC,SAAS,EAAC,MAAM,EAAC,OAAO,EAAC,YAAY,EAAC,MAAM,EAAC,MAAM,EAAC,WAAW,EAAC,MAAM,CAAC,CAACkG,QAAQ,CAACnG,CAAC,CAACsB,IAAI,CAAC,EAAC;UAACvL,CAAC,IAAEkK,CAAC,IAAE,EAAE;UAAC;QAAQ;MAAC;MAAC,IAAI9J,CAAC,GAACD,CAAC;MAAC,QAAOC,CAAC,CAACmL,IAAI;QAAE,KAAI,OAAO;UAAC;YAACvL,CAAC,IAAE,IAAI,CAACb,QAAQ,CAACiN,KAAK,CAAChM,CAAC,CAAC;YAAC;UAAQ;QAAC,KAAI,IAAI;UAAC;YAACJ,CAAC,IAAE,IAAI,CAACb,QAAQ,CAACwG,EAAE,CAACvF,CAAC,CAAC;YAAC;UAAQ;QAAC,KAAI,SAAS;UAAC;YAACJ,CAAC,IAAE,IAAI,CAACb,QAAQ,CAACuG,OAAO,CAACtF,CAAC,CAAC;YAAC;UAAQ;QAAC,KAAI,MAAM;UAAC;YAACJ,CAAC,IAAE,IAAI,CAACb,QAAQ,CAACoG,IAAI,CAACnF,CAAC,CAAC;YAAC;UAAQ;QAAC,KAAI,OAAO;UAAC;YAACJ,CAAC,IAAE,IAAI,CAACb,QAAQ,CAAC8G,KAAK,CAAC7F,CAAC,CAAC;YAAC;UAAQ;QAAC,KAAI,YAAY;UAAC;YAACJ,CAAC,IAAE,IAAI,CAACb,QAAQ,CAACmG,UAAU,CAAClF,CAAC,CAAC;YAAC;UAAQ;QAAC,KAAI,MAAM;UAAC;YAACJ,CAAC,IAAE,IAAI,CAACb,QAAQ,CAAC2G,IAAI,CAAC1F,CAAC,CAAC;YAAC;UAAQ;QAAC,KAAI,MAAM;UAAC;YAACJ,CAAC,IAAE,IAAI,CAACb,QAAQ,CAACyG,IAAI,CAACxF,CAAC,CAAC;YAAC;UAAQ;QAAC,KAAI,WAAW;UAAC;YAACJ,CAAC,IAAE,IAAI,CAACb,QAAQ,CAAC6G,SAAS,CAAC5F,CAAC,CAAC;YAAC;UAAQ;QAAC,KAAI,MAAM;UAAC;YAAC,IAAI6J,CAAC,GAAC7J,CAAC;cAAC8J,CAAC,GAAC,IAAI,CAAC/K,QAAQ,CAAC+G,IAAI,CAAC+D,CAAC,CAAC;YAAC,OAAK/J,CAAC,GAAC,CAAC,GAACL,CAAC,CAAC0K,MAAM,IAAE1K,CAAC,CAACK,CAAC,GAAC,CAAC,CAAC,CAACqL,IAAI,KAAG,MAAM,GAAEtB,CAAC,GAACpK,CAAC,CAAC,EAAEK,CAAC,CAAC,EAACgK,CAAC,IAAG;AAC5rE,CAAC,GAAC,IAAI,CAAC/K,QAAQ,CAAC+G,IAAI,CAAC+D,CAAC,CAAC;YAACnK,CAAC,GAACE,CAAC,IAAE,IAAI,CAACb,QAAQ,CAAC6G,SAAS,CAAC;cAACuF,IAAI,EAAC,WAAW;cAACC,GAAG,EAACtB,CAAC;cAAChE,IAAI,EAACgE,CAAC;cAACuB,MAAM,EAAC,CAAC;gBAACF,IAAI,EAAC,MAAM;gBAACC,GAAG,EAACtB,CAAC;gBAAChE,IAAI,EAACgE,CAAC;gBAACiF,OAAO,EAAC,CAAC;cAAC,CAAC;YAAC,CAAC,CAAC,GAACnP,CAAC,IAAEkK,CAAC;YAAC;UAAQ;QAAC;UAAQ;YAAC,IAAID,CAAC,GAAC,cAAc,GAAC7J,CAAC,CAACmL,IAAI,GAAC,uBAAuB;YAAC,IAAG,IAAI,CAACS,OAAO,CAAC5M,MAAM,EAAC,OAAO4Q,OAAO,CAACC,KAAK,CAAChG,CAAC,CAAC,EAAC,EAAE;YAAC,MAAM,IAAIiG,KAAK,CAACjG,CAAC,CAAC;UAAA;MAAC;IAAC;IAAC,OAAOjK,CAAC;EAAA;EAAC0Q,WAAWA,CAAC7Q,CAAC,EAACC,CAAC,GAAC,IAAI,CAACX,QAAQ,EAAC;IAAC,IAAIa,CAAC,GAAC,EAAE;IAAC,KAAI,IAAIE,CAAC,GAAC,CAAC,EAACA,CAAC,GAACL,CAAC,CAAC0K,MAAM,EAACrK,CAAC,EAAE,EAAC;MAAC,IAAIC,CAAC,GAACN,CAAC,CAACK,CAAC,CAAC;MAAC,IAAG,IAAI,CAAC8L,OAAO,CAACjN,UAAU,EAAEuS,SAAS,GAAGnR,CAAC,CAACoL,IAAI,CAAC,EAAC;QAAC,IAAItB,CAAC,GAAC,IAAI,CAAC+B,OAAO,CAACjN,UAAU,CAACuS,SAAS,CAACnR,CAAC,CAACoL,IAAI,CAAC,CAACqE,IAAI,CAAC;UAACY,MAAM,EAAC;QAAI,CAAC,EAACrQ,CAAC,CAAC;QAAC,IAAG8J,CAAC,KAAG,CAAC,CAAC,IAAE,CAAC,CAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,CAAC,CAACmG,QAAQ,CAACjQ,CAAC,CAACoL,IAAI,CAAC,EAAC;UAACvL,CAAC,IAAEiK,CAAC,IAAE,EAAE;UAAC;QAAQ;MAAC;MAAC,IAAI7J,CAAC,GAACD,CAAC;MAAC,QAAOC,CAAC,CAACmL,IAAI;QAAE,KAAI,QAAQ;UAAC;YAACvL,CAAC,IAAEF,CAAC,CAACoG,IAAI,CAAC9F,CAAC,CAAC;YAAC;UAAK;QAAC,KAAI,MAAM;UAAC;YAACJ,CAAC,IAAEF,CAAC,CAAC8F,IAAI,CAACxF,CAAC,CAAC;YAAC;UAAK;QAAC,KAAI,MAAM;UAAC;YAACJ,CAAC,IAAEF,CAAC,CAAC+I,IAAI,CAACzI,CAAC,CAAC;YAAC;UAAK;QAAC,KAAI,OAAO;UAAC;YAACJ,CAAC,IAAEF,CAAC,CAACoR,KAAK,CAAC9Q,CAAC,CAAC;YAAC;UAAK;QAAC,KAAI,QAAQ;UAAC;YAACJ,CAAC,IAAEF,CAAC,CAACkR,MAAM,CAAC5Q,CAAC,CAAC;YAAC;UAAK;QAAC,KAAI,IAAI;UAAC;YAACJ,CAAC,IAAEF,CAAC,CAACmR,EAAE,CAAC7Q,CAAC,CAAC;YAAC;UAAK;QAAC,KAAI,UAAU;UAAC;YAACJ,CAAC,IAAEF,CAAC,CAACmP,QAAQ,CAAC7O,CAAC,CAAC;YAAC;UAAK;QAAC,KAAI,IAAI;UAAC;YAACJ,CAAC,IAAEF,CAAC,CAACyI,EAAE,CAACnI,CAAC,CAAC;YAAC;UAAK;QAAC,KAAI,KAAK;UAAC;YAACJ,CAAC,IAAEF,CAAC,CAAC0I,GAAG,CAACpI,CAAC,CAAC;YAAC;UAAK;QAAC,KAAI,MAAM;UAAC;YAACJ,CAAC,IAAEF,CAAC,CAACoG,IAAI,CAAC9F,CAAC,CAAC;YAAC;UAAK;QAAC;UAAQ;YAAC,IAAI6J,CAAC,GAAC,cAAc,GAAC7J,CAAC,CAACmL,IAAI,GAAC,uBAAuB;YAAC,IAAG,IAAI,CAACS,OAAO,CAAC5M,MAAM,EAAC,OAAO4Q,OAAO,CAACC,KAAK,CAAChG,CAAC,CAAC,EAAC,EAAE;YAAC,MAAM,IAAIiG,KAAK,CAACjG,CAAC,CAAC;UAAA;MAAC;IAAC;IAAC,OAAOjK,CAAC;EAAA;AAAC,CAAC;AAAC,IAAIuR,CAAC,GAAC,MAAK;EAACvF,OAAO;EAACK,KAAK;EAACF,WAAWA,CAACtM,CAAC,EAAC;IAAC,IAAI,CAACmM,OAAO,GAACnM,CAAC,IAAEN,CAAC;EAAA;EAAC,OAAOiS,gBAAgB,GAAC,IAAIC,GAAG,CAAC,CAAC,YAAY,EAAC,aAAa,EAAC,kBAAkB,CAAC,CAAC;EAACC,UAAUA,CAAC7R,CAAC,EAAC;IAAC,OAAOA,CAAC;EAAA;EAAC8R,WAAWA,CAAC9R,CAAC,EAAC;IAAC,OAAOA,CAAC;EAAA;EAAC+R,gBAAgBA,CAAC/R,CAAC,EAAC;IAAC,OAAOA,CAAC;EAAA;EAACgS,YAAYA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACxF,KAAK,GAAC+C,CAAC,CAACK,GAAG,GAACL,CAAC,CAACM,SAAS;EAAA;EAACoC,aAAaA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACzF,KAAK,GAACgF,CAAC,CAACZ,KAAK,GAACY,CAAC,CAACX,WAAW;EAAA;AAAC,CAAC;AAAC,IAAIqB,CAAC,GAAC,MAAK;EAACC,QAAQ,GAACpT,CAAC,CAAC,CAAC;EAACoN,OAAO,GAAC,IAAI,CAACiG,UAAU;EAACxB,KAAK,GAAC,IAAI,CAACyB,aAAa,CAAC,CAAC,CAAC,CAAC;EAACxB,WAAW,GAAC,IAAI,CAACwB,aAAa,CAAC,CAAC,CAAC,CAAC;EAACC,MAAM,GAACd,CAAC;EAACe,QAAQ,GAAC7B,CAAC;EAAC8B,YAAY,GAACjB,CAAC;EAACkB,KAAK,GAAClD,CAAC;EAACmD,SAAS,GAACxG,CAAC;EAACyG,KAAK,GAACjB,CAAC;EAACpF,WAAWA,CAAC,GAAGtM,CAAC,EAAC;IAAC,IAAI,CAAC4S,GAAG,CAAC,GAAG5S,CAAC,CAAC;EAAA;EAACP,UAAUA,CAACO,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIE,CAAC,GAAC,EAAE;IAAC,KAAI,IAAIE,CAAC,IAAIL,CAAC,EAAC,QAAOG,CAAC,GAACA,CAAC,CAAC0S,MAAM,CAAC5S,CAAC,CAAC8P,IAAI,CAAC,IAAI,EAAC1P,CAAC,CAAC,CAAC,EAACA,CAAC,CAACqL,IAAI;MAAE,KAAI,OAAO;QAAC;UAAC,IAAIpL,CAAC,GAACD,CAAC;UAAC,KAAI,IAAIE,CAAC,IAAID,CAAC,CAACuO,MAAM,EAAC1O,CAAC,GAACA,CAAC,CAAC0S,MAAM,CAAC,IAAI,CAACpT,UAAU,CAACc,CAAC,CAACqL,MAAM,EAAC3L,CAAC,CAAC,CAAC;UAAC,KAAI,IAAIM,CAAC,IAAID,CAAC,CAACyO,IAAI,EAAC,KAAI,IAAI3E,CAAC,IAAI7J,CAAC,EAACJ,CAAC,GAACA,CAAC,CAAC0S,MAAM,CAAC,IAAI,CAACpT,UAAU,CAAC2K,CAAC,CAACwB,MAAM,EAAC3L,CAAC,CAAC,CAAC;UAAC;QAAK;MAAC,KAAI,MAAM;QAAC;UAAC,IAAIK,CAAC,GAACD,CAAC;UAACF,CAAC,GAACA,CAAC,CAAC0S,MAAM,CAAC,IAAI,CAACpT,UAAU,CAACa,CAAC,CAACoN,KAAK,EAACzN,CAAC,CAAC,CAAC;UAAC;QAAK;MAAC;QAAQ;UAAC,IAAIK,CAAC,GAACD,CAAC;UAAC,IAAI,CAAC8R,QAAQ,CAACjT,UAAU,EAAE4T,WAAW,GAAGxS,CAAC,CAACoL,IAAI,CAAC,GAAC,IAAI,CAACyG,QAAQ,CAACjT,UAAU,CAAC4T,WAAW,CAACxS,CAAC,CAACoL,IAAI,CAAC,CAACuE,OAAO,CAAC1P,CAAC,IAAE;YAAC,IAAI6J,CAAC,GAAC9J,CAAC,CAACC,CAAC,CAAC,CAACwS,IAAI,CAAC,CAAC,GAAC,CAAC,CAAC;YAAC5S,CAAC,GAACA,CAAC,CAAC0S,MAAM,CAAC,IAAI,CAACpT,UAAU,CAAC2K,CAAC,EAACnK,CAAC,CAAC,CAAC;UAAA,CAAC,CAAC,GAACK,CAAC,CAACsL,MAAM,KAAGzL,CAAC,GAACA,CAAC,CAAC0S,MAAM,CAAC,IAAI,CAACpT,UAAU,CAACa,CAAC,CAACsL,MAAM,EAAC3L,CAAC,CAAC,CAAC,CAAC;QAAA;IAAC;IAAC,OAAOE,CAAC;EAAA;EAACyS,GAAGA,CAAC,GAAG5S,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACkS,QAAQ,CAACjT,UAAU,IAAE;MAACuS,SAAS,EAAC,CAAC,CAAC;MAACqB,WAAW,EAAC,CAAC;IAAC,CAAC;IAAC,OAAO9S,CAAC,CAACiQ,OAAO,CAAC9P,CAAC,IAAE;MAAC,IAAIE,CAAC,GAAC;QAAC,GAAGF;MAAC,CAAC;MAAC,IAAGE,CAAC,CAACrB,KAAK,GAAC,IAAI,CAACmT,QAAQ,CAACnT,KAAK,IAAEqB,CAAC,CAACrB,KAAK,IAAE,CAAC,CAAC,EAACmB,CAAC,CAACjB,UAAU,KAAGiB,CAAC,CAACjB,UAAU,CAAC+Q,OAAO,CAAC3P,CAAC,IAAE;QAAC,IAAG,CAACA,CAAC,CAAC0S,IAAI,EAAC,MAAM,IAAI3C,KAAK,CAAC,yBAAyB,CAAC;QAAC,IAAG,UAAU,IAAG/P,CAAC,EAAC;UAAC,IAAIC,CAAC,GAACN,CAAC,CAACwR,SAAS,CAACnR,CAAC,CAAC0S,IAAI,CAAC;UAACzS,CAAC,GAACN,CAAC,CAACwR,SAAS,CAACnR,CAAC,CAAC0S,IAAI,CAAC,GAAC,UAAS,GAAG5I,CAAC,EAAC;YAAC,IAAIC,CAAC,GAAC/J,CAAC,CAAChB,QAAQ,CAAC2T,KAAK,CAAC,IAAI,EAAC7I,CAAC,CAAC;YAAC,OAAOC,CAAC,KAAG,CAAC,CAAC,KAAGA,CAAC,GAAC9J,CAAC,CAAC0S,KAAK,CAAC,IAAI,EAAC7I,CAAC,CAAC,CAAC,EAACC,CAAC;UAAA,CAAC,GAACpK,CAAC,CAACwR,SAAS,CAACnR,CAAC,CAAC0S,IAAI,CAAC,GAAC1S,CAAC,CAAChB,QAAQ;QAAA;QAAC,IAAG,WAAW,IAAGgB,CAAC,EAAC;UAAC,IAAG,CAACA,CAAC,CAAC4S,KAAK,IAAE5S,CAAC,CAAC4S,KAAK,KAAG,OAAO,IAAE5S,CAAC,CAAC4S,KAAK,KAAG,QAAQ,EAAC,MAAM,IAAI7C,KAAK,CAAC,6CAA6C,CAAC;UAAC,IAAI9P,CAAC,GAACN,CAAC,CAACK,CAAC,CAAC4S,KAAK,CAAC;UAAC3S,CAAC,GAACA,CAAC,CAACyQ,OAAO,CAAC1Q,CAAC,CAACd,SAAS,CAAC,GAACS,CAAC,CAACK,CAAC,CAAC4S,KAAK,CAAC,GAAC,CAAC5S,CAAC,CAACd,SAAS,CAAC,EAACc,CAAC,CAACkN,KAAK,KAAGlN,CAAC,CAAC4S,KAAK,KAAG,OAAO,GAACjT,CAAC,CAAC+P,UAAU,GAAC/P,CAAC,CAAC+P,UAAU,CAAClF,IAAI,CAACxK,CAAC,CAACkN,KAAK,CAAC,GAACvN,CAAC,CAAC+P,UAAU,GAAC,CAAC1P,CAAC,CAACkN,KAAK,CAAC,GAAClN,CAAC,CAAC4S,KAAK,KAAG,QAAQ,KAAGjT,CAAC,CAACwQ,WAAW,GAACxQ,CAAC,CAACwQ,WAAW,CAAC3F,IAAI,CAACxK,CAAC,CAACkN,KAAK,CAAC,GAACvN,CAAC,CAACwQ,WAAW,GAAC,CAACnQ,CAAC,CAACkN,KAAK,CAAC,CAAC,CAAC;QAAA;QAAC,aAAa,IAAGlN,CAAC,IAAEA,CAAC,CAACwS,WAAW,KAAG7S,CAAC,CAAC6S,WAAW,CAACxS,CAAC,CAAC0S,IAAI,CAAC,GAAC1S,CAAC,CAACwS,WAAW,CAAC;MAAA,CAAC,CAAC,EAACzS,CAAC,CAACnB,UAAU,GAACe,CAAC,CAAC,EAACE,CAAC,CAACb,QAAQ,EAAC;QAAC,IAAIgB,CAAC,GAAC,IAAI,CAAC6R,QAAQ,CAAC7S,QAAQ,IAAE,IAAIoR,CAAC,CAAC,IAAI,CAACyB,QAAQ,CAAC;QAAC,KAAI,IAAI5R,CAAC,IAAIJ,CAAC,CAACb,QAAQ,EAAC;UAAC,IAAG,EAAEiB,CAAC,IAAID,CAAC,CAAC,EAAC,MAAM,IAAI+P,KAAK,CAAE,aAAY9P,CAAE,kBAAiB,CAAC;UAAC,IAAG,CAAC,SAAS,EAAC,QAAQ,CAAC,CAACgQ,QAAQ,CAAChQ,CAAC,CAAC,EAAC;UAAS,IAAI6J,CAAC,GAAC7J,CAAC;YAAC8J,CAAC,GAAClK,CAAC,CAACb,QAAQ,CAAC8K,CAAC,CAAC;YAACE,CAAC,GAAChK,CAAC,CAAC8J,CAAC,CAAC;UAAC9J,CAAC,CAAC8J,CAAC,CAAC,GAAC,CAAC,GAAGyC,CAAC,KAAG;YAAC,IAAIC,CAAC,GAACzC,CAAC,CAAC4I,KAAK,CAAC3S,CAAC,EAACuM,CAAC,CAAC;YAAC,OAAOC,CAAC,KAAG,CAAC,CAAC,KAAGA,CAAC,GAACxC,CAAC,CAAC2I,KAAK,CAAC3S,CAAC,EAACuM,CAAC,CAAC,CAAC,EAACC,CAAC,IAAE,EAAE;UAAA,CAAC;QAAA;QAACzM,CAAC,CAACf,QAAQ,GAACgB,CAAC;MAAA;MAAC,IAAGH,CAAC,CAACX,SAAS,EAAC;QAAC,IAAIc,CAAC,GAAC,IAAI,CAAC6R,QAAQ,CAAC3S,SAAS,IAAE,IAAI0M,CAAC,CAAC,IAAI,CAACiG,QAAQ,CAAC;QAAC,KAAI,IAAI5R,CAAC,IAAIJ,CAAC,CAACX,SAAS,EAAC;UAAC,IAAG,EAAEe,CAAC,IAAID,CAAC,CAAC,EAAC,MAAM,IAAI+P,KAAK,CAAE,cAAa9P,CAAE,kBAAiB,CAAC;UAAC,IAAG,CAAC,SAAS,EAAC,OAAO,EAAC,OAAO,CAAC,CAACgQ,QAAQ,CAAChQ,CAAC,CAAC,EAAC;UAAS,IAAI6J,CAAC,GAAC7J,CAAC;YAAC8J,CAAC,GAAClK,CAAC,CAACX,SAAS,CAAC4K,CAAC,CAAC;YAACE,CAAC,GAAChK,CAAC,CAAC8J,CAAC,CAAC;UAAC9J,CAAC,CAAC8J,CAAC,CAAC,GAAC,CAAC,GAAGyC,CAAC,KAAG;YAAC,IAAIC,CAAC,GAACzC,CAAC,CAAC4I,KAAK,CAAC3S,CAAC,EAACuM,CAAC,CAAC;YAAC,OAAOC,CAAC,KAAG,CAAC,CAAC,KAAGA,CAAC,GAACxC,CAAC,CAAC2I,KAAK,CAAC3S,CAAC,EAACuM,CAAC,CAAC,CAAC,EAACC,CAAC;UAAA,CAAC;QAAA;QAACzM,CAAC,CAACb,SAAS,GAACc,CAAC;MAAA;MAAC,IAAGH,CAAC,CAACf,KAAK,EAAC;QAAC,IAAIkB,CAAC,GAAC,IAAI,CAAC6R,QAAQ,CAAC/S,KAAK,IAAE,IAAIsS,CAAC,CAAD,CAAC;QAAC,KAAI,IAAInR,CAAC,IAAIJ,CAAC,CAACf,KAAK,EAAC;UAAC,IAAG,EAAEmB,CAAC,IAAID,CAAC,CAAC,EAAC,MAAM,IAAI+P,KAAK,CAAE,SAAQ9P,CAAE,kBAAiB,CAAC;UAAC,IAAG,CAAC,SAAS,EAAC,OAAO,CAAC,CAACgQ,QAAQ,CAAChQ,CAAC,CAAC,EAAC;UAAS,IAAI6J,CAAC,GAAC7J,CAAC;YAAC8J,CAAC,GAAClK,CAAC,CAACf,KAAK,CAACgL,CAAC,CAAC;YAACE,CAAC,GAAChK,CAAC,CAAC8J,CAAC,CAAC;UAACsH,CAAC,CAACC,gBAAgB,CAACwB,GAAG,CAAC5S,CAAC,CAAC,GAACD,CAAC,CAAC8J,CAAC,CAAC,GAACyC,CAAC,IAAE;YAAC,IAAG,IAAI,CAACsF,QAAQ,CAACnT,KAAK,EAAC,OAAOoU,OAAO,CAACC,OAAO,CAAChJ,CAAC,CAAC0F,IAAI,CAACzP,CAAC,EAACuM,CAAC,CAAC,CAAC,CAACyG,IAAI,CAACvG,CAAC,IAAEzC,CAAC,CAACyF,IAAI,CAACzP,CAAC,EAACyM,CAAC,CAAC,CAAC;YAAC,IAAID,CAAC,GAACzC,CAAC,CAAC0F,IAAI,CAACzP,CAAC,EAACuM,CAAC,CAAC;YAAC,OAAOvC,CAAC,CAACyF,IAAI,CAACzP,CAAC,EAACwM,CAAC,CAAC;UAAA,CAAC,GAACxM,CAAC,CAAC8J,CAAC,CAAC,GAAC,CAAC,GAAGyC,CAAC,KAAG;YAAC,IAAIC,CAAC,GAACzC,CAAC,CAAC4I,KAAK,CAAC3S,CAAC,EAACuM,CAAC,CAAC;YAAC,OAAOC,CAAC,KAAG,CAAC,CAAC,KAAGA,CAAC,GAACxC,CAAC,CAAC2I,KAAK,CAAC3S,CAAC,EAACuM,CAAC,CAAC,CAAC,EAACC,CAAC;UAAA,CAAC;QAAA;QAACzM,CAAC,CAACjB,KAAK,GAACkB,CAAC;MAAA;MAAC,IAAGH,CAAC,CAACV,UAAU,EAAC;QAAC,IAAIa,CAAC,GAAC,IAAI,CAAC6R,QAAQ,CAAC1S,UAAU;UAACc,CAAC,GAACJ,CAAC,CAACV,UAAU;QAACY,CAAC,CAACZ,UAAU,GAAC,UAAS2K,CAAC,EAAC;UAAC,IAAIC,CAAC,GAAC,EAAE;UAAC,OAAOA,CAAC,CAACS,IAAI,CAACvK,CAAC,CAACwP,IAAI,CAAC,IAAI,EAAC3F,CAAC,CAAC,CAAC,EAAC9J,CAAC,KAAG+J,CAAC,GAACA,CAAC,CAACwI,MAAM,CAACvS,CAAC,CAACyP,IAAI,CAAC,IAAI,EAAC3F,CAAC,CAAC,CAAC,CAAC,EAACC,CAAC;QAAA,CAAC;MAAA;MAAC,IAAI,CAAC8H,QAAQ,GAAC;QAAC,GAAG,IAAI,CAACA,QAAQ;QAAC,GAAG9R;MAAC,CAAC;IAAA,CAAC,CAAC,EAAC,IAAI;EAAA;EAAC+R,UAAUA,CAACpS,CAAC,EAAC;IAAC,OAAO,IAAI,CAACmS,QAAQ,GAAC;MAAC,GAAG,IAAI,CAACA,QAAQ;MAAC,GAAGnS;IAAC,CAAC,EAAC,IAAI;EAAA;EAACqM,KAAKA,CAACrM,CAAC,EAACC,CAAC,EAAC;IAAC,OAAOsP,CAAC,CAACK,GAAG,CAAC5P,CAAC,EAACC,CAAC,IAAE,IAAI,CAACkS,QAAQ,CAAC;EAAA;EAACxB,MAAMA,CAAC3Q,CAAC,EAACC,CAAC,EAAC;IAAC,OAAOuR,CAAC,CAACZ,KAAK,CAAC5Q,CAAC,EAACC,CAAC,IAAE,IAAI,CAACkS,QAAQ,CAAC;EAAA;EAACE,aAAaA,CAACrS,CAAC,EAAC;IAAC,OAAM,CAACG,CAAC,EAACE,CAAC,KAAG;MAAC,IAAIC,CAAC,GAAC;UAAC,GAAGD;QAAC,CAAC;QAACE,CAAC,GAAC;UAAC,GAAG,IAAI,CAAC4R,QAAQ;UAAC,GAAG7R;QAAC,CAAC;QAAC8J,CAAC,GAAC,IAAI,CAACmJ,OAAO,CAAC,CAAC,CAAChT,CAAC,CAAChB,MAAM,EAAC,CAAC,CAACgB,CAAC,CAACvB,KAAK,CAAC;MAAC,IAAG,IAAI,CAACmT,QAAQ,CAACnT,KAAK,KAAG,CAAC,CAAC,IAAEsB,CAAC,CAACtB,KAAK,KAAG,CAAC,CAAC,EAAC,OAAOoL,CAAC,CAAC,IAAIiG,KAAK,CAAC,oIAAoI,CAAC,CAAC;MAAC,IAAG,OAAOlQ,CAAC,GAAC,GAAG,IAAEA,CAAC,KAAG,IAAI,EAAC,OAAOiK,CAAC,CAAC,IAAIiG,KAAK,CAAC,gDAAgD,CAAC,CAAC;MAAC,IAAG,OAAOlQ,CAAC,IAAE,QAAQ,EAAC,OAAOiK,CAAC,CAAC,IAAIiG,KAAK,CAAC,uCAAuC,GAACX,MAAM,CAAC8D,SAAS,CAACC,QAAQ,CAAC1D,IAAI,CAAC5P,CAAC,CAAC,GAAC,mBAAmB,CAAC,CAAC;MAACI,CAAC,CAACnB,KAAK,KAAGmB,CAAC,CAACnB,KAAK,CAAC+M,OAAO,GAAC5L,CAAC,EAACA,CAAC,CAACnB,KAAK,CAACoN,KAAK,GAACxM,CAAC,CAAC;MAAC,IAAIqK,CAAC,GAAC9J,CAAC,CAACnB,KAAK,GAACmB,CAAC,CAACnB,KAAK,CAAC4S,YAAY,CAAC,CAAC,GAAChS,CAAC,GAACuP,CAAC,CAACK,GAAG,GAACL,CAAC,CAACM,SAAS;QAACvF,CAAC,GAAC/J,CAAC,CAACnB,KAAK,GAACmB,CAAC,CAACnB,KAAK,CAAC6S,aAAa,CAAC,CAAC,GAACjS,CAAC,GAACwR,CAAC,CAACZ,KAAK,GAACY,CAAC,CAACX,WAAW;MAAC,IAAGtQ,CAAC,CAACvB,KAAK,EAAC,OAAOoU,OAAO,CAACC,OAAO,CAAC9S,CAAC,CAACnB,KAAK,GAACmB,CAAC,CAACnB,KAAK,CAACyS,UAAU,CAAC1R,CAAC,CAAC,GAACA,CAAC,CAAC,CAACmT,IAAI,CAACzG,CAAC,IAAExC,CAAC,CAACwC,CAAC,EAACtM,CAAC,CAAC,CAAC,CAAC+S,IAAI,CAACzG,CAAC,IAAEtM,CAAC,CAACnB,KAAK,GAACmB,CAAC,CAACnB,KAAK,CAAC2S,gBAAgB,CAAClF,CAAC,CAAC,GAACA,CAAC,CAAC,CAACyG,IAAI,CAACzG,CAAC,IAAEtM,CAAC,CAACd,UAAU,GAAC2T,OAAO,CAACM,GAAG,CAAC,IAAI,CAACjU,UAAU,CAACoN,CAAC,EAACtM,CAAC,CAACd,UAAU,CAAC,CAAC,CAAC6T,IAAI,CAAC,MAAIzG,CAAC,CAAC,GAACA,CAAC,CAAC,CAACyG,IAAI,CAACzG,CAAC,IAAEvC,CAAC,CAACuC,CAAC,EAACtM,CAAC,CAAC,CAAC,CAAC+S,IAAI,CAACzG,CAAC,IAAEtM,CAAC,CAACnB,KAAK,GAACmB,CAAC,CAACnB,KAAK,CAAC0S,WAAW,CAACjF,CAAC,CAAC,GAACA,CAAC,CAAC,CAAC8G,KAAK,CAACvJ,CAAC,CAAC;MAAC,IAAG;QAAC7J,CAAC,CAACnB,KAAK,KAAGe,CAAC,GAACI,CAAC,CAACnB,KAAK,CAACyS,UAAU,CAAC1R,CAAC,CAAC,CAAC;QAAC,IAAI0M,CAAC,GAACxC,CAAC,CAAClK,CAAC,EAACI,CAAC,CAAC;QAACA,CAAC,CAACnB,KAAK,KAAGyN,CAAC,GAACtM,CAAC,CAACnB,KAAK,CAAC2S,gBAAgB,CAAClF,CAAC,CAAC,CAAC,EAACtM,CAAC,CAACd,UAAU,IAAE,IAAI,CAACA,UAAU,CAACoN,CAAC,EAACtM,CAAC,CAACd,UAAU,CAAC;QAAC,IAAIqN,CAAC,GAACxC,CAAC,CAACuC,CAAC,EAACtM,CAAC,CAAC;QAAC,OAAOA,CAAC,CAACnB,KAAK,KAAG0N,CAAC,GAACvM,CAAC,CAACnB,KAAK,CAAC0S,WAAW,CAAChF,CAAC,CAAC,CAAC,EAACA,CAAC;MAAA,CAAC,QAAMD,CAAC,EAAC;QAAC,OAAOzC,CAAC,CAACyC,CAAC,CAAC;MAAA;IAAC,CAAC;EAAA;EAAC0G,OAAOA,CAACvT,CAAC,EAACC,CAAC,EAAC;IAAC,OAAOE,CAAC,IAAE;MAAC,IAAGA,CAAC,CAACyT,OAAO,IAAG;AACvmL,0DAA0D,EAAC5T,CAAC,EAAC;QAAC,IAAIK,CAAC,GAAC,gCAAgC,GAAC0J,CAAC,CAAC5J,CAAC,CAACyT,OAAO,GAAC,EAAE,EAAC,CAAC,CAAC,CAAC,GAAC,QAAQ;QAAC,OAAO3T,CAAC,GAACmT,OAAO,CAACC,OAAO,CAAChT,CAAC,CAAC,GAACA,CAAC;MAAA;MAAC,IAAGJ,CAAC,EAAC,OAAOmT,OAAO,CAACS,MAAM,CAAC1T,CAAC,CAAC;MAAC,MAAMA,CAAC;IAAA,CAAC;EAAA;AAAC,CAAC;AAAC,IAAI2T,CAAC,GAAC,IAAI5B,CAAC,CAAD,CAAC;AAAC,SAAS6B,CAACA,CAACnU,CAAC,EAACI,CAAC,EAAC;EAAC,OAAO8T,CAAC,CAAClD,KAAK,CAAChR,CAAC,EAACI,CAAC,CAAC;AAAA;AAAC+T,CAAC,CAAC5H,OAAO,GAAC4H,CAAC,CAAC3B,UAAU,GAAC,UAASxS,CAAC,EAAC;EAAC,OAAOkU,CAAC,CAAC1B,UAAU,CAACxS,CAAC,CAAC,EAACmU,CAAC,CAAC5B,QAAQ,GAAC2B,CAAC,CAAC3B,QAAQ,EAACxS,CAAC,CAACoU,CAAC,CAAC5B,QAAQ,CAAC,EAAC4B,CAAC;AAAA,CAAC;AAACA,CAAC,CAACC,WAAW,GAACjV,CAAC;AAACgV,CAAC,CAAC5B,QAAQ,GAACzS,CAAC;AAACqU,CAAC,CAACnB,GAAG,GAAC,UAAS,GAAGhT,CAAC,EAAC;EAAC,OAAOkU,CAAC,CAAClB,GAAG,CAAC,GAAGhT,CAAC,CAAC,EAACmU,CAAC,CAAC5B,QAAQ,GAAC2B,CAAC,CAAC3B,QAAQ,EAACxS,CAAC,CAACoU,CAAC,CAAC5B,QAAQ,CAAC,EAAC4B,CAAC;AAAA,CAAC;AAACA,CAAC,CAACtU,UAAU,GAAC,UAASG,CAAC,EAACI,CAAC,EAAC;EAAC,OAAO8T,CAAC,CAACrU,UAAU,CAACG,CAAC,EAACI,CAAC,CAAC;AAAA,CAAC;AAAC+T,CAAC,CAAClD,WAAW,GAACiD,CAAC,CAACjD,WAAW;AAACkD,CAAC,CAACzB,MAAM,GAACd,CAAC;AAACuC,CAAC,CAACpD,MAAM,GAACa,CAAC,CAACZ,KAAK;AAACmD,CAAC,CAACxB,QAAQ,GAAC7B,CAAC;AAACqD,CAAC,CAACvB,YAAY,GAACjB,CAAC;AAACwC,CAAC,CAACtB,KAAK,GAAClD,CAAC;AAACwE,CAAC,CAAC1H,KAAK,GAACkD,CAAC,CAACK,GAAG;AAACmE,CAAC,CAACrB,SAAS,GAACxG,CAAC;AAAC6H,CAAC,CAACpB,KAAK,GAACjB,CAAC;AAACqC,CAAC,CAACnD,KAAK,GAACmD,CAAC;AAAC,IAAIE,EAAE,GAACF,CAAC,CAAC5H,OAAO;EAAC+H,EAAE,GAACH,CAAC,CAAC3B,UAAU;EAAC+B,EAAE,GAACJ,CAAC,CAACnB,GAAG;EAACwB,EAAE,GAACL,CAAC,CAACtU,UAAU;EAAC4U,EAAE,GAACN,CAAC,CAAClD,WAAW;EAACyD,EAAE,GAACP,CAAC;EAACQ,EAAE,GAAC/C,CAAC,CAACZ,KAAK;EAAC4D,EAAE,GAACjF,CAAC,CAACK,GAAG;AAAC,SAAO8B,CAAC,IAAIiB,KAAK,EAACpD,CAAC,IAAIkD,KAAK,EAACP,CAAC,IAAIuC,MAAM,EAACjD,CAAC,IAAIc,MAAM,EAAC5B,CAAC,IAAI6B,QAAQ,EAAChB,CAAC,IAAIiB,YAAY,EAACtG,CAAC,IAAIwG,SAAS,EAAChT,CAAC,IAAIyS,QAAQ,EAACpT,CAAC,IAAIiV,WAAW,EAACQ,EAAE,IAAInI,KAAK,EAAC0H,CAAC,IAAIW,MAAM,EAACT,EAAE,IAAI9H,OAAO,EAACmI,EAAE,IAAI1D,KAAK,EAACyD,EAAE,IAAIxD,WAAW,EAAC0D,EAAE,IAAI5D,MAAM,EAACuD,EAAE,IAAI9B,UAAU,EAAC+B,EAAE,IAAIvB,GAAG,EAACwB,EAAE,IAAI3U,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}