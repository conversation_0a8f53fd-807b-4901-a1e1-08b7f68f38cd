{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction NewsComponent_div_0_h2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\", 6);\n    i0.ɵɵtext(1, \"Latest News\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewsComponent_div_0_div_2_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function NewsComponent_div_0_div_2_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      ctx_r9.searchTerm = \"\";\n      return i0.ɵɵresetView(ctx_r9.applyFilters());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 34);\n    i0.ɵɵelement(2, \"path\", 35);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewsComponent_div_0_div_2_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const country_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", country_r11);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, country_r11), \" \");\n  }\n}\nfunction NewsComponent_div_0_div_2_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const source_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", source_r12);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", source_r12, \" \");\n  }\n}\nfunction NewsComponent_div_0_div_2_button_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NewsComponent_div_0_div_2_button_38_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r13.clearFilters());\n    });\n    i0.ɵɵtext(1, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewsComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 10);\n    i0.ɵɵelement(4, \"path\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"input\", 12);\n    i0.ɵɵlistener(\"input\", function NewsComponent_div_0_div_2_Template_input_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, NewsComponent_div_0_div_2_button_6_Template, 3, 0, \"button\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 14)(8, \"div\", 15)(9, \"label\", 16);\n    i0.ɵɵtext(10, \"Country:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"select\", 17);\n    i0.ɵɵlistener(\"change\", function NewsComponent_div_0_div_2_Template_select_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.onCountryChange($event));\n    });\n    i0.ɵɵelementStart(12, \"option\", 18);\n    i0.ɵɵtext(13, \"All Countries\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, NewsComponent_div_0_div_2_option_14_Template, 3, 4, \"option\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 15)(16, \"label\", 20);\n    i0.ɵɵtext(17, \"Source:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"select\", 21);\n    i0.ɵɵlistener(\"change\", function NewsComponent_div_0_div_2_Template_select_change_18_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.onSourceChange($event));\n    });\n    i0.ɵɵelementStart(19, \"option\", 18);\n    i0.ɵɵtext(20, \"All Sources\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, NewsComponent_div_0_div_2_option_21_Template, 2, 2, \"option\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 15)(23, \"label\", 22);\n    i0.ɵɵtext(24, \"Sort by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"select\", 23);\n    i0.ɵɵlistener(\"change\", function NewsComponent_div_0_div_2_Template_select_change_25_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.onSortChange($event));\n    });\n    i0.ɵɵelementStart(26, \"option\", 24);\n    i0.ɵɵtext(27, \"Newest First\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"option\", 25);\n    i0.ɵɵtext(29, \"Oldest First\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 26);\n    i0.ɵɵtext(31, \"Title A-Z\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 27);\n    i0.ɵɵtext(33, \"Title Z-A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"option\", 28);\n    i0.ɵɵtext(35, \"Source A-Z\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"option\", 29);\n    i0.ɵɵtext(37, \"Source Z-A\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(38, NewsComponent_div_0_div_2_button_38_Template, 2, 0, \"button\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 31)(40, \"span\", 32);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ctx_r2.selectedCountry);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableCountries);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", ctx_r2.selectedSource);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableSources);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", ctx_r2.sortBy + \"-\" + ctx_r2.sortOrder);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm || ctx_r2.selectedCountry || ctx_r2.selectedSource || ctx_r2.sortBy !== \"date\" || ctx_r2.sortOrder !== \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" Showing \", ctx_r2.filteredNews.length, \" of \", ctx_r2.news.length, \" articles \");\n  }\n}\nfunction NewsComponent_div_0_div_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 40)(1, \"div\", 41)(2, \"span\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"h3\", 44);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 45);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 46)(12, \"span\", 47);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 48);\n    i0.ɵɵtext(16, \"Read \\u2192\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"href\", item_r21.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 6, item_r21.country));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r21.source);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r21.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r21.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 8, item_r21.published, \"medium\"));\n  }\n}\nfunction NewsComponent_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, NewsComponent_div_0_div_3_a_1_Template, 17, 11, \"a\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filteredNews)(\"ngForTrackBy\", ctx_r3.trackByTitle);\n  }\n}\nfunction NewsComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 51);\n    i0.ɵɵelement(3, \"path\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"No articles found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Try adjusting your search terms or filters to find more articles.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NewsComponent_div_0_div_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.clearFilters());\n    });\n    i0.ɵɵtext(9, \" Clear All Filters \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"compact\": a0\n  };\n};\nfunction NewsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, NewsComponent_div_0_h2_1_Template, 2, 0, \"h2\", 2);\n    i0.ɵɵtemplate(2, NewsComponent_div_0_div_2_Template, 42, 10, \"div\", 3);\n    i0.ɵɵtemplate(3, NewsComponent_div_0_div_3_Template, 2, 2, \"div\", 4);\n    i0.ɵɵtemplate(4, NewsComponent_div_0_div_4_Template, 10, 0, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r0.compact));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showTitle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showFilters && !ctx_r0.compact);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filteredNews.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filteredNews.length === 0 && (ctx_r0.searchTerm || ctx_r0.selectedCountry || ctx_r0.selectedSource));\n  }\n}\nexport class NewsComponent {\n  constructor() {\n    this.news = [];\n    this.showTitle = true;\n    this.compact = false;\n    this.showFilters = true; // New input to control filter visibility\n    // Filter and search properties\n    this.filteredNews = [];\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.sortBy = 'date'; // 'date', 'title', 'source'\n    this.sortOrder = 'desc'; // 'asc', 'desc'\n    // Available filter options\n    this.availableCountries = [];\n    this.availableSources = [];\n  }\n  ngOnInit() {\n    this.initializeFilters();\n    this.applyFilters();\n  }\n  ngOnChanges(changes) {\n    if (changes['news']) {\n      this.initializeFilters();\n      this.applyFilters();\n    }\n  }\n  initializeFilters() {\n    // Extract unique countries and sources\n    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n  }\n  applyFilters() {\n    let filtered = [...this.news];\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(item => item.title.toLowerCase().includes(searchLower) || item.description.toLowerCase().includes(searchLower) || item.source.toLowerCase().includes(searchLower));\n    }\n    // Apply country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(item => item.country === this.selectedCountry);\n    }\n    // Apply source filter\n    if (this.selectedSource) {\n      filtered = filtered.filter(item => item.source === this.selectedSource);\n    }\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let comparison = 0;\n      switch (this.sortBy) {\n        case 'date':\n          comparison = new Date(a.published).getTime() - new Date(b.published).getTime();\n          break;\n        case 'title':\n          comparison = a.title.localeCompare(b.title);\n          break;\n        case 'source':\n          comparison = a.source.localeCompare(b.source);\n          break;\n      }\n      return this.sortOrder === 'desc' ? -comparison : comparison;\n    });\n    this.filteredNews = filtered;\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n  onCountryChange(event) {\n    this.selectedCountry = event.target.value;\n    this.applyFilters();\n  }\n  onSourceChange(event) {\n    this.selectedSource = event.target.value;\n    this.applyFilters();\n  }\n  onSortChange(event) {\n    const [sortBy, sortOrder] = event.target.value.split('-');\n    this.sortBy = sortBy;\n    this.sortOrder = sortOrder;\n    this.applyFilters();\n  }\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.sortBy = 'date';\n    this.sortOrder = 'desc';\n    this.applyFilters();\n  }\n  trackByTitle(index, item) {\n    return item.title;\n  }\n  static #_ = this.ɵfac = function NewsComponent_Factory(t) {\n    return new (t || NewsComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NewsComponent,\n    selectors: [[\"app-news\"]],\n    inputs: {\n      news: \"news\",\n      showTitle: \"showTitle\",\n      compact: \"compact\",\n      showFilters: \"showFilters\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"news-wrapper\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"news-wrapper\", 3, \"ngClass\"], [\"class\", \"section-title\", 4, \"ngIf\"], [\"class\", \"news-controls\", 4, \"ngIf\"], [\"class\", \"news-grid\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"section-title\"], [1, \"news-controls\"], [1, \"search-container\"], [1, \"search-input-wrapper\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"search-icon\"], [\"d\", \"M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"type\", \"text\", \"placeholder\", \"Search news by title, description, or source...\", 1, \"search-input\", 3, \"value\", \"input\"], [\"class\", \"clear-search-btn\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [1, \"filter-controls\"], [1, \"filter-group\"], [\"for\", \"country-filter\", 1, \"filter-label\"], [\"id\", \"country-filter\", 1, \"filter-select\", 3, \"value\", \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"source-filter\", 1, \"filter-label\"], [\"id\", \"source-filter\", 1, \"filter-select\", 3, \"value\", \"change\"], [\"for\", \"sort-filter\", 1, \"filter-label\"], [\"id\", \"sort-filter\", 1, \"filter-select\", 3, \"value\", \"change\"], [\"value\", \"date-desc\"], [\"value\", \"date-asc\"], [\"value\", \"title-asc\"], [\"value\", \"title-desc\"], [\"value\", \"source-asc\"], [\"value\", \"source-desc\"], [\"class\", \"clear-filters-btn\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [1, \"results-info\"], [1, \"results-count\"], [\"type\", \"button\", 1, \"clear-search-btn\", 3, \"click\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M18 6L6 18M6 6L18 18\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [3, \"value\"], [\"type\", \"button\", 1, \"clear-filters-btn\", 3, \"click\"], [1, \"news-grid\"], [\"class\", \"news-card\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"news-card\", 3, \"href\"], [1, \"news-card-header\"], [1, \"badge\"], [1, \"source\"], [1, \"title\"], [1, \"description\"], [1, \"news-card-footer\"], [1, \"published\"], [1, \"cta\"], [1, \"no-results\"], [1, \"no-results-icon\"], [\"width\", \"48\", \"height\", \"48\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"]],\n    template: function NewsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NewsComponent_div_0_Template, 5, 7, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.news && ctx.news.length);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i1.TitleCasePipe, i1.DatePipe],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n\\n\\n.news-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  line-height: 1.3;\\n  color: var(--color-gray-900);\\n  font-weight: var(--font-weight-semibold);\\n}\\n\\n.news-card[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n  color: var(--color-gray-600);\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%] {\\n  transition: all var(--transition-fast);\\n}\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: var(--shadow-md);\\n}\\n\\n\\n\\n.news-controls[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-8);\\n  padding: var(--space-6);\\n  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);\\n  border: 1px solid var(--color-gray-200);\\n  border-radius: var(--radius-xl);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n}\\n\\n\\n\\n.search-container[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-6);\\n}\\n\\n.search-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  max-width: 500px;\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: var(--space-4);\\n  color: var(--color-gray-500);\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: var(--space-4) var(--space-12) var(--space-4) var(--space-12);\\n  border: 2px solid var(--color-gray-200);\\n  border-radius: 28px;\\n  font-size: var(--font-size-base);\\n  font-family: var(--font-family-primary);\\n  background: var(--color-white);\\n  transition: all var(--transition-normal);\\n  color: var(--color-gray-900);\\n}\\n.search-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--color-primary);\\n  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.08);\\n  transform: translateY(-1px);\\n}\\n.search-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--color-gray-500);\\n}\\n\\n.clear-search-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: var(--space-3);\\n  background: none;\\n  border: none;\\n  color: var(--color-gray-500);\\n  cursor: pointer;\\n  padding: var(--space-2);\\n  border-radius: 50%;\\n  transition: all var(--transition-fast);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.clear-search-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-700);\\n}\\n\\n\\n\\n.filter-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: var(--space-4);\\n  align-items: flex-end;\\n  margin-bottom: var(--space-4);\\n}\\n\\n.filter-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--space-2);\\n  min-width: 150px;\\n}\\n\\n.filter-label[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-sm);\\n  font-weight: var(--font-weight-medium);\\n  color: var(--color-gray-700);\\n}\\n\\n.filter-select[_ngcontent-%COMP%] {\\n  padding: var(--space-3) var(--space-4);\\n  border: 2px solid var(--color-gray-200);\\n  border-radius: var(--radius-lg);\\n  font-size: var(--font-size-sm);\\n  font-family: var(--font-family-primary);\\n  background: var(--color-white);\\n  color: var(--color-gray-900);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n.filter-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--color-primary);\\n  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.08);\\n}\\n.filter-select[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-gray-300);\\n}\\n\\n.clear-filters-btn[_ngcontent-%COMP%] {\\n  padding: var(--space-3) var(--space-5);\\n  background: linear-gradient(135deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);\\n  border: 1px solid var(--color-gray-300);\\n  border-radius: var(--radius-lg);\\n  font-size: var(--font-size-sm);\\n  font-weight: var(--font-weight-medium);\\n  color: var(--color-gray-700);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  height: -moz-fit-content;\\n  height: fit-content;\\n}\\n.clear-filters-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, var(--color-gray-200) 0%, var(--color-gray-300) 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.clear-filters-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n\\n\\n\\n.results-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-top: var(--space-4);\\n  border-top: 1px solid var(--color-gray-200);\\n}\\n\\n.results-count[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-sm);\\n  color: var(--color-gray-600);\\n  font-weight: var(--font-weight-medium);\\n}\\n\\n\\n\\n.no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: var(--space-12) var(--space-6);\\n  color: var(--color-gray-600);\\n}\\n.no-results[_ngcontent-%COMP%]   .no-results-icon[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n  color: var(--color-gray-400);\\n}\\n.no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--space-2) 0;\\n  color: var(--color-gray-800);\\n  font-size: var(--font-size-lg);\\n  font-weight: var(--font-weight-semibold);\\n}\\n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--space-6) 0;\\n  font-size: var(--font-size-base);\\n  line-height: 1.6;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .news-controls[_ngcontent-%COMP%] {\\n    padding: var(--space-4);\\n    margin-bottom: var(--space-6);\\n  }\\n  .search-input-wrapper[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n  .filter-controls[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: var(--space-3);\\n  }\\n  .filter-group[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n  .clear-filters-btn[_ngcontent-%COMP%] {\\n    align-self: center;\\n    margin-top: var(--space-2);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "NewsComponent_div_0_div_2_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "searchTerm", "ɵɵresetView", "applyFilters", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵproperty", "country_r11", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "source_r12", "NewsComponent_div_0_div_2_button_38_Template_button_click_0_listener", "_r14", "ctx_r13", "clearFilters", "ɵɵnamespaceHTML", "NewsComponent_div_0_div_2_Template_input_input_5_listener", "$event", "_r16", "ctx_r15", "onSearchChange", "ɵɵtemplate", "NewsComponent_div_0_div_2_button_6_Template", "NewsComponent_div_0_div_2_Template_select_change_11_listener", "ctx_r17", "onCountryChange", "NewsComponent_div_0_div_2_option_14_Template", "NewsComponent_div_0_div_2_Template_select_change_18_listener", "ctx_r18", "onSourceChange", "NewsComponent_div_0_div_2_option_21_Template", "NewsComponent_div_0_div_2_Template_select_change_25_listener", "ctx_r19", "onSortChange", "NewsComponent_div_0_div_2_button_38_Template", "ctx_r2", "selectedCountry", "availableCountries", "selectedSource", "availableSources", "sortBy", "sortOrder", "ɵɵtextInterpolate2", "filteredNews", "length", "news", "item_r21", "link", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "country", "source", "title", "description", "ɵɵpipeBind2", "published", "NewsComponent_div_0_div_3_a_1_Template", "ctx_r3", "trackByTitle", "NewsComponent_div_0_div_4_Template_button_click_8_listener", "_r23", "ctx_r22", "NewsComponent_div_0_h2_1_Template", "NewsComponent_div_0_div_2_Template", "NewsComponent_div_0_div_3_Template", "NewsComponent_div_0_div_4_Template", "ɵɵpureFunction1", "_c0", "ctx_r0", "compact", "showTitle", "showFilters", "NewsComponent", "constructor", "ngOnInit", "initializeFilters", "ngOnChanges", "changes", "Set", "map", "item", "sort", "filtered", "trim", "searchLower", "toLowerCase", "filter", "includes", "a", "b", "comparison", "Date", "getTime", "localeCompare", "event", "target", "value", "split", "index", "_", "_2", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "NewsComponent_Template", "rf", "ctx", "NewsComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\news\\news.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\news\\news.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';\n\nexport interface NewsItem {\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string; // ISO string\n  description: string;\n  fetched_at?: string;\n  // allow extra fields (e.g., id, created_at) from backend without strict typing\n  [key: string]: any;\n}\n\n@Component({\n  selector: 'app-news',\n  templateUrl: './news.component.html',\n  styleUrls: ['./news.component.scss']\n})\nexport class NewsComponent implements OnInit, OnChanges {\n  @Input() news: NewsItem[] = [];\n  @Input() showTitle: boolean = true;\n  @Input() compact: boolean = false;\n  @Input() showFilters: boolean = true; // New input to control filter visibility\n\n  // Filter and search properties\n  filteredNews: NewsItem[] = [];\n  searchTerm: string = '';\n  selectedCountry: string = '';\n  selectedSource: string = '';\n  sortBy: string = 'date'; // 'date', 'title', 'source'\n  sortOrder: string = 'desc'; // 'asc', 'desc'\n\n  // Available filter options\n  availableCountries: string[] = [];\n  availableSources: string[] = [];\n\n  ngOnInit() {\n    this.initializeFilters();\n    this.applyFilters();\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (changes['news']) {\n      this.initializeFilters();\n      this.applyFilters();\n    }\n  }\n\n  initializeFilters() {\n    // Extract unique countries and sources\n    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n  }\n\n  applyFilters() {\n    let filtered = [...this.news];\n\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(item =>\n        item.title.toLowerCase().includes(searchLower) ||\n        item.description.toLowerCase().includes(searchLower) ||\n        item.source.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(item => item.country === this.selectedCountry);\n    }\n\n    // Apply source filter\n    if (this.selectedSource) {\n      filtered = filtered.filter(item => item.source === this.selectedSource);\n    }\n\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let comparison = 0;\n\n      switch (this.sortBy) {\n        case 'date':\n          comparison = new Date(a.published).getTime() - new Date(b.published).getTime();\n          break;\n        case 'title':\n          comparison = a.title.localeCompare(b.title);\n          break;\n        case 'source':\n          comparison = a.source.localeCompare(b.source);\n          break;\n      }\n\n      return this.sortOrder === 'desc' ? -comparison : comparison;\n    });\n\n    this.filteredNews = filtered;\n  }\n\n  onSearchChange(event: any) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n\n  onCountryChange(event: any) {\n    this.selectedCountry = event.target.value;\n    this.applyFilters();\n  }\n\n  onSourceChange(event: any) {\n    this.selectedSource = event.target.value;\n    this.applyFilters();\n  }\n\n  onSortChange(event: any) {\n    const [sortBy, sortOrder] = event.target.value.split('-');\n    this.sortBy = sortBy;\n    this.sortOrder = sortOrder;\n    this.applyFilters();\n  }\n\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.sortBy = 'date';\n    this.sortOrder = 'desc';\n    this.applyFilters();\n  }\n\n  trackByTitle(index: number, item: NewsItem) {\n    return item.title;\n  }\n}\n", "<div class=\"news-wrapper\" [ngClass]=\"{ 'compact': compact }\" *ngIf=\"news && news.length\">\n  <h2 class=\"section-title\" *ngIf=\"showTitle\">Latest News</h2>\n\n  <!-- Search and Filter Controls -->\n  <div class=\"news-controls\" *ngIf=\"showFilters && !compact\">\n    <!-- Search Bar -->\n    <div class=\"search-container\">\n      <div class=\"search-input-wrapper\">\n        <svg class=\"search-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        </svg>\n        <input\n          type=\"text\"\n          class=\"search-input\"\n          placeholder=\"Search news by title, description, or source...\"\n          [value]=\"searchTerm\"\n          (input)=\"onSearchChange($event)\"\n        />\n        <button\n          class=\"clear-search-btn\"\n          *ngIf=\"searchTerm\"\n          (click)=\"searchTerm = ''; applyFilters()\"\n          type=\"button\"\n        >\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </button>\n      </div>\n    </div>\n\n    <!-- Filter Controls -->\n    <div class=\"filter-controls\">\n      <div class=\"filter-group\">\n        <label for=\"country-filter\" class=\"filter-label\">Country:</label>\n        <select\n          id=\"country-filter\"\n          class=\"filter-select\"\n          [value]=\"selectedCountry\"\n          (change)=\"onCountryChange($event)\"\n        >\n          <option value=\"\">All Countries</option>\n          <option *ngFor=\"let country of availableCountries\" [value]=\"country\">\n            {{ country | titlecase }}\n          </option>\n        </select>\n      </div>\n\n      <div class=\"filter-group\">\n        <label for=\"source-filter\" class=\"filter-label\">Source:</label>\n        <select\n          id=\"source-filter\"\n          class=\"filter-select\"\n          [value]=\"selectedSource\"\n          (change)=\"onSourceChange($event)\"\n        >\n          <option value=\"\">All Sources</option>\n          <option *ngFor=\"let source of availableSources\" [value]=\"source\">\n            {{ source }}\n          </option>\n        </select>\n      </div>\n\n      <div class=\"filter-group\">\n        <label for=\"sort-filter\" class=\"filter-label\">Sort by:</label>\n        <select\n          id=\"sort-filter\"\n          class=\"filter-select\"\n          [value]=\"sortBy + '-' + sortOrder\"\n          (change)=\"onSortChange($event)\"\n        >\n          <option value=\"date-desc\">Newest First</option>\n          <option value=\"date-asc\">Oldest First</option>\n          <option value=\"title-asc\">Title A-Z</option>\n          <option value=\"title-desc\">Title Z-A</option>\n          <option value=\"source-asc\">Source A-Z</option>\n          <option value=\"source-desc\">Source Z-A</option>\n        </select>\n      </div>\n\n      <button\n        class=\"clear-filters-btn\"\n        (click)=\"clearFilters()\"\n        type=\"button\"\n        *ngIf=\"searchTerm || selectedCountry || selectedSource || sortBy !== 'date' || sortOrder !== 'desc'\"\n      >\n        Clear Filters\n      </button>\n    </div>\n\n    <!-- Results Count -->\n    <div class=\"results-info\">\n      <span class=\"results-count\">\n        Showing {{ filteredNews.length }} of {{ news.length }} articles\n      </span>\n    </div>\n  </div>\n\n  <!-- News Grid -->\n  <div class=\"news-grid\" *ngIf=\"filteredNews.length > 0\">\n    <a\n      class=\"news-card\"\n      *ngFor=\"let item of filteredNews; trackBy: trackByTitle\"\n      [href]=\"item.link\"\n      target=\"_blank\"\n      rel=\"noopener noreferrer\"\n    >\n      <div class=\"news-card-header\">\n        <span class=\"badge\">{{ item.country | titlecase }}</span>\n        <span class=\"source\">{{ item.source }}</span>\n      </div>\n\n      <h3 class=\"title\">{{ item.title }}</h3>\n      <p class=\"description\">{{ item.description }}</p>\n\n      <div class=\"news-card-footer\">\n        <span class=\"published\">{{ item.published | date:'medium' }}</span>\n        <span class=\"cta\">Read →</span>\n      </div>\n    </a>\n  </div>\n\n  <!-- No Results Message -->\n  <div class=\"no-results\" *ngIf=\"filteredNews.length === 0 && (searchTerm || selectedCountry || selectedSource)\">\n    <div class=\"no-results-icon\">\n      <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n        <path d=\"M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n      </svg>\n    </div>\n    <h3>No articles found</h3>\n    <p>Try adjusting your search terms or filters to find more articles.</p>\n    <button class=\"clear-filters-btn\" (click)=\"clearFilters()\" type=\"button\">\n      Clear All Filters\n    </button>\n  </div>\n</div>\n"], "mappings": ";;;;;ICCEA,EAAA,CAAAC,cAAA,YAA4C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAiBtDH,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAI,UAAA,mBAAAC,oEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAD,MAAA,CAAAE,UAAA,GAAsB,EAAE;MAAA,OAAEV,EAAA,CAAAW,WAAA,CAAAH,MAAA,CAAAI,YAAA,EAAc;IAAA,EAAC;IAGzCZ,EAAA,CAAAa,cAAA,EAA+F;IAA/Fb,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAc,SAAA,eAAsH;IACxHd,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBNH,EAAA,CAAAC,cAAA,iBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF0CH,EAAA,CAAAe,UAAA,UAAAC,WAAA,CAAiB;IAClEhB,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAmB,WAAA,OAAAH,WAAA,OACF;;;;;IAaAhB,EAAA,CAAAC,cAAA,iBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFuCH,EAAA,CAAAe,UAAA,UAAAK,UAAA,CAAgB;IAC9DpB,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAE,UAAA,MACF;;;;;;IAqBJpB,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAI,UAAA,mBAAAiB,qEAAA;MAAArB,EAAA,CAAAM,aAAA,CAAAgB,IAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAW,WAAA,CAAAY,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAIxBxB,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAnFbH,EAAA,CAAAC,cAAA,aAA2D;IAIrDD,EAAA,CAAAa,cAAA,EAAmH;IAAnHb,EAAA,CAAAC,cAAA,cAAmH;IACjHD,EAAA,CAAAc,SAAA,eAA4O;IAC9Od,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAyB,eAAA,EAME;IANFzB,EAAA,CAAAC,cAAA,gBAME;IADAD,EAAA,CAAAI,UAAA,mBAAAsB,0DAAAC,MAAA;MAAA3B,EAAA,CAAAM,aAAA,CAAAsB,IAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAW,WAAA,CAAAkB,OAAA,CAAAC,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IALlC3B,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAA+B,UAAA,IAAAC,2CAAA,qBASS;IACXhC,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,cAA6B;IAEwBD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAI,UAAA,oBAAA6B,6DAAAN,MAAA;MAAA3B,EAAA,CAAAM,aAAA,CAAAsB,IAAA;MAAA,MAAAM,OAAA,GAAAlC,EAAA,CAAAS,aAAA;MAAA,OAAUT,EAAA,CAAAW,WAAA,CAAAuB,OAAA,CAAAC,eAAA,CAAAR,MAAA,CAAuB;IAAA,EAAC;IAElC3B,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAA+B,UAAA,KAAAK,4CAAA,qBAES;IACXpC,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,eAA0B;IACwBD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAI,UAAA,oBAAAiC,6DAAAV,MAAA;MAAA3B,EAAA,CAAAM,aAAA,CAAAsB,IAAA;MAAA,MAAAU,OAAA,GAAAtC,EAAA,CAAAS,aAAA;MAAA,OAAUT,EAAA,CAAAW,WAAA,CAAA2B,OAAA,CAAAC,cAAA,CAAAZ,MAAA,CAAsB;IAAA,EAAC;IAEjC3B,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAA+B,UAAA,KAAAS,4CAAA,qBAES;IACXxC,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,eAA0B;IACsBD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAI,UAAA,oBAAAqC,6DAAAd,MAAA;MAAA3B,EAAA,CAAAM,aAAA,CAAAsB,IAAA;MAAA,MAAAc,OAAA,GAAA1C,EAAA,CAAAS,aAAA;MAAA,OAAUT,EAAA,CAAAW,WAAA,CAAA+B,OAAA,CAAAC,YAAA,CAAAhB,MAAA,CAAoB;IAAA,EAAC;IAE/B3B,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC/CH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9CH,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7CH,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9CH,EAAA,CAAAC,cAAA,kBAA4B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAInDH,EAAA,CAAA+B,UAAA,KAAAa,4CAAA,qBAOS;IACX5C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0B;IAEtBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IA/EHH,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAe,UAAA,UAAA8B,MAAA,CAAAnC,UAAA,CAAoB;IAKnBV,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAe,UAAA,SAAA8B,MAAA,CAAAnC,UAAA,CAAgB;IAkBjBV,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAe,UAAA,UAAA8B,MAAA,CAAAC,eAAA,CAAyB;IAIG9C,EAAA,CAAAiB,SAAA,GAAqB;IAArBjB,EAAA,CAAAe,UAAA,YAAA8B,MAAA,CAAAE,kBAAA,CAAqB;IAWjD/C,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAe,UAAA,UAAA8B,MAAA,CAAAG,cAAA,CAAwB;IAIGhD,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAe,UAAA,YAAA8B,MAAA,CAAAI,gBAAA,CAAmB;IAW9CjD,EAAA,CAAAiB,SAAA,GAAkC;IAAlCjB,EAAA,CAAAe,UAAA,UAAA8B,MAAA,CAAAK,MAAA,SAAAL,MAAA,CAAAM,SAAA,CAAkC;IAgBnCnD,EAAA,CAAAiB,SAAA,IAAkG;IAAlGjB,EAAA,CAAAe,UAAA,SAAA8B,MAAA,CAAAnC,UAAA,IAAAmC,MAAA,CAAAC,eAAA,IAAAD,MAAA,CAAAG,cAAA,IAAAH,MAAA,CAAAK,MAAA,eAAAL,MAAA,CAAAM,SAAA,YAAkG;IASnGnD,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAoD,kBAAA,cAAAP,MAAA,CAAAQ,YAAA,CAAAC,MAAA,UAAAT,MAAA,CAAAU,IAAA,CAAAD,MAAA,eACF;;;;;IAMFtD,EAAA,CAAAC,cAAA,YAMC;IAEuBD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG/CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEjDH,EAAA,CAAAC,cAAA,eAA8B;IACJD,EAAA,CAAAE,MAAA,IAAoC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,mBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAdjCH,EAAA,CAAAe,UAAA,SAAAyC,QAAA,CAAAC,IAAA,EAAAzD,EAAA,CAAA0D,aAAA,CAAkB;IAKI1D,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAA2D,iBAAA,CAAA3D,EAAA,CAAAmB,WAAA,OAAAqC,QAAA,CAAAI,OAAA,EAA8B;IAC7B5D,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAA2D,iBAAA,CAAAH,QAAA,CAAAK,MAAA,CAAiB;IAGtB7D,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAA2D,iBAAA,CAAAH,QAAA,CAAAM,KAAA,CAAgB;IACX9D,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAA2D,iBAAA,CAAAH,QAAA,CAAAO,WAAA,CAAsB;IAGnB/D,EAAA,CAAAiB,SAAA,GAAoC;IAApCjB,EAAA,CAAA2D,iBAAA,CAAA3D,EAAA,CAAAgE,WAAA,QAAAR,QAAA,CAAAS,SAAA,YAAoC;;;;;IAjBlEjE,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAA+B,UAAA,IAAAmC,sCAAA,kBAmBI;IACNlE,EAAA,CAAAG,YAAA,EAAM;;;;IAlBeH,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAe,UAAA,YAAAoD,MAAA,CAAAd,YAAA,CAAiB,iBAAAc,MAAA,CAAAC,YAAA;;;;;;IAqBtCpE,EAAA,CAAAC,cAAA,cAA+G;IAE3GD,EAAA,CAAAa,cAAA,EAA+F;IAA/Fb,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAc,SAAA,eAA4O;IAC9Od,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAyB,eAAA,EAAI;IAAJzB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wEAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxEH,EAAA,CAAAC,cAAA,iBAAyE;IAAvCD,EAAA,CAAAI,UAAA,mBAAAiE,2DAAA;MAAArE,EAAA,CAAAM,aAAA,CAAAgE,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAW,WAAA,CAAA4D,OAAA,CAAA/C,YAAA,EAAc;IAAA,EAAC;IACxDxB,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;IArIbH,EAAA,CAAAC,cAAA,aAAyF;IACvFD,EAAA,CAAA+B,UAAA,IAAAyC,iCAAA,gBAA4D;IAG5DxE,EAAA,CAAA+B,UAAA,IAAA0C,kCAAA,mBA4FM;IAGNzE,EAAA,CAAA+B,UAAA,IAAA2C,kCAAA,iBAqBM;IAGN1E,EAAA,CAAA+B,UAAA,IAAA4C,kCAAA,kBAWM;IACR3E,EAAA,CAAAG,YAAA,EAAM;;;;IAvIoBH,EAAA,CAAAe,UAAA,YAAAf,EAAA,CAAA4E,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,OAAA,EAAkC;IAC/B/E,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAe,UAAA,SAAA+D,MAAA,CAAAE,SAAA,CAAe;IAGdhF,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAAe,UAAA,SAAA+D,MAAA,CAAAG,WAAA,KAAAH,MAAA,CAAAC,OAAA,CAA6B;IA+FjC/E,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAAe,UAAA,SAAA+D,MAAA,CAAAzB,YAAA,CAAAC,MAAA,KAA6B;IAwB5BtD,EAAA,CAAAiB,SAAA,GAAoF;IAApFjB,EAAA,CAAAe,UAAA,SAAA+D,MAAA,CAAAzB,YAAA,CAAAC,MAAA,WAAAwB,MAAA,CAAApE,UAAA,IAAAoE,MAAA,CAAAhC,eAAA,IAAAgC,MAAA,CAAA9B,cAAA,EAAoF;;;ADxG/G,OAAM,MAAOkC,aAAa;EAL1BC,YAAA;IAMW,KAAA5B,IAAI,GAAe,EAAE;IACrB,KAAAyB,SAAS,GAAY,IAAI;IACzB,KAAAD,OAAO,GAAY,KAAK;IACxB,KAAAE,WAAW,GAAY,IAAI,CAAC,CAAC;IAEtC;IACA,KAAA5B,YAAY,GAAe,EAAE;IAC7B,KAAA3C,UAAU,GAAW,EAAE;IACvB,KAAAoC,eAAe,GAAW,EAAE;IAC5B,KAAAE,cAAc,GAAW,EAAE;IAC3B,KAAAE,MAAM,GAAW,MAAM,CAAC,CAAC;IACzB,KAAAC,SAAS,GAAW,MAAM,CAAC,CAAC;IAE5B;IACA,KAAAJ,kBAAkB,GAAa,EAAE;IACjC,KAAAE,gBAAgB,GAAa,EAAE;;EAE/BmC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACzE,YAAY,EAAE;EACrB;EAEA0E,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,MAAM,CAAC,EAAE;MACnB,IAAI,CAACF,iBAAiB,EAAE;MACxB,IAAI,CAACzE,YAAY,EAAE;;EAEvB;EAEAyE,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACtC,kBAAkB,GAAG,CAAC,GAAG,IAAIyC,GAAG,CAAC,IAAI,CAACjC,IAAI,CAACkC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC9B,OAAO,CAAC,CAAC,CAAC,CAAC+B,IAAI,EAAE;IAClF,IAAI,CAAC1C,gBAAgB,GAAG,CAAC,GAAG,IAAIuC,GAAG,CAAC,IAAI,CAACjC,IAAI,CAACkC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC7B,MAAM,CAAC,CAAC,CAAC,CAAC8B,IAAI,EAAE;EACjF;EAEA/E,YAAYA,CAAA;IACV,IAAIgF,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACrC,IAAI,CAAC;IAE7B;IACA,IAAI,IAAI,CAAC7C,UAAU,CAACmF,IAAI,EAAE,EAAE;MAC1B,MAAMC,WAAW,GAAG,IAAI,CAACpF,UAAU,CAACqF,WAAW,EAAE;MACjDH,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACN,IAAI,IAC7BA,IAAI,CAAC5B,KAAK,CAACiC,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC9CJ,IAAI,CAAC3B,WAAW,CAACgC,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IACpDJ,IAAI,CAAC7B,MAAM,CAACkC,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CAChD;;IAGH;IACA,IAAI,IAAI,CAAChD,eAAe,EAAE;MACxB8C,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACN,IAAI,IAAIA,IAAI,CAAC9B,OAAO,KAAK,IAAI,CAACd,eAAe,CAAC;;IAG3E;IACA,IAAI,IAAI,CAACE,cAAc,EAAE;MACvB4C,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACN,IAAI,IAAIA,IAAI,CAAC7B,MAAM,KAAK,IAAI,CAACb,cAAc,CAAC;;IAGzE;IACA4C,QAAQ,CAACD,IAAI,CAAC,CAACO,CAAC,EAAEC,CAAC,KAAI;MACrB,IAAIC,UAAU,GAAG,CAAC;MAElB,QAAQ,IAAI,CAAClD,MAAM;QACjB,KAAK,MAAM;UACTkD,UAAU,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACjC,SAAS,CAAC,CAACqC,OAAO,EAAE,GAAG,IAAID,IAAI,CAACF,CAAC,CAAClC,SAAS,CAAC,CAACqC,OAAO,EAAE;UAC9E;QACF,KAAK,OAAO;UACVF,UAAU,GAAGF,CAAC,CAACpC,KAAK,CAACyC,aAAa,CAACJ,CAAC,CAACrC,KAAK,CAAC;UAC3C;QACF,KAAK,QAAQ;UACXsC,UAAU,GAAGF,CAAC,CAACrC,MAAM,CAAC0C,aAAa,CAACJ,CAAC,CAACtC,MAAM,CAAC;UAC7C;;MAGJ,OAAO,IAAI,CAACV,SAAS,KAAK,MAAM,GAAG,CAACiD,UAAU,GAAGA,UAAU;IAC7D,CAAC,CAAC;IAEF,IAAI,CAAC/C,YAAY,GAAGuC,QAAQ;EAC9B;EAEA9D,cAAcA,CAAC0E,KAAU;IACvB,IAAI,CAAC9F,UAAU,GAAG8F,KAAK,CAACC,MAAM,CAACC,KAAK;IACpC,IAAI,CAAC9F,YAAY,EAAE;EACrB;EAEAuB,eAAeA,CAACqE,KAAU;IACxB,IAAI,CAAC1D,eAAe,GAAG0D,KAAK,CAACC,MAAM,CAACC,KAAK;IACzC,IAAI,CAAC9F,YAAY,EAAE;EACrB;EAEA2B,cAAcA,CAACiE,KAAU;IACvB,IAAI,CAACxD,cAAc,GAAGwD,KAAK,CAACC,MAAM,CAACC,KAAK;IACxC,IAAI,CAAC9F,YAAY,EAAE;EACrB;EAEA+B,YAAYA,CAAC6D,KAAU;IACrB,MAAM,CAACtD,MAAM,EAAEC,SAAS,CAAC,GAAGqD,KAAK,CAACC,MAAM,CAACC,KAAK,CAACC,KAAK,CAAC,GAAG,CAAC;IACzD,IAAI,CAACzD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACvC,YAAY,EAAE;EACrB;EAEAY,YAAYA,CAAA;IACV,IAAI,CAACd,UAAU,GAAG,EAAE;IACpB,IAAI,CAACoC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACE,MAAM,GAAG,MAAM;IACpB,IAAI,CAACC,SAAS,GAAG,MAAM;IACvB,IAAI,CAACvC,YAAY,EAAE;EACrB;EAEAwD,YAAYA,CAACwC,KAAa,EAAElB,IAAc;IACxC,OAAOA,IAAI,CAAC5B,KAAK;EACnB;EAAC,QAAA+C,CAAA,G;qBAlHU3B,aAAa;EAAA;EAAA,QAAA4B,EAAA,G;UAAb5B,aAAa;IAAA6B,SAAA;IAAAC,MAAA;MAAAzD,IAAA;MAAAyB,SAAA;MAAAD,OAAA;MAAAE,WAAA;IAAA;IAAAgC,QAAA,GAAAjH,EAAA,CAAAkH,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnB1BxH,EAAA,CAAA+B,UAAA,IAAA2F,4BAAA,iBAuIM;;;QAvIwD1H,EAAA,CAAAe,UAAA,SAAA0G,GAAA,CAAAlE,IAAA,IAAAkE,GAAA,CAAAlE,IAAA,CAAAD,MAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}