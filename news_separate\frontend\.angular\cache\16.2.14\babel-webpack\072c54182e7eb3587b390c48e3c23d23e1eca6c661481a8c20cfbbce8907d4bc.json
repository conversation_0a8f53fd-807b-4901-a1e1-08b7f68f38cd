{"ast": null, "code": "import { marked } from 'marked';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../anonymous-user/anonymous-user.component\";\nimport * as i5 from \"../news/news.component\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatComponent_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_1_div_1_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵelement(1, \"app-news\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"news\", message_r8.news_articles)(\"showTitle\", false)(\"compact\", false);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatComponent_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"div\", 20);\n    i0.ɵɵelementStart(3, \"div\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ChatComponent_div_1_div_1_div_3_div_6_Template, 2, 3, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r8 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c1, message_r8.isUser, !message_r8.isUser));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r6.formatMessageText(message_r8.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 4, message_r8.timestamp, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !message_r8.isUser && message_r8.news_articles && message_r8.news_articles.length);\n  }\n}\nfunction ChatComponent_div_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 19)(2, \"div\", 26);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11, 12);\n    i0.ɵɵlistener(\"scroll\", function ChatComponent_div_1_div_1_Template_div_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onScroll($event));\n    });\n    i0.ɵɵtemplate(2, ChatComponent_div_1_div_1_div_2_Template, 2, 0, \"div\", 13);\n    i0.ɵɵtemplate(3, ChatComponent_div_1_div_1_div_3_Template, 7, 10, \"div\", 14);\n    i0.ɵɵtemplate(4, ChatComponent_div_1_div_1_div_4_Template, 6, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ChatComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, ChatComponent_div_1_div_1_Template, 5, 3, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\", 5)(4, \"textarea\", 6, 7);\n    i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_div_1_Template_textarea_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.currentMessage = $event);\n    })(\"keydown\", function ChatComponent_div_1_Template_textarea_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onKeyPress($event));\n    })(\"input\", function ChatComponent_div_1_Template_textarea_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.adjustTextareaHeight($event));\n    });\n    i0.ɵɵtext(6, \"      \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_1_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.sendMessage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 9);\n    i0.ɵɵelement(9, \"path\", 10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"centered\", ctx_r0.messages.length === 0)(\"bottom\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.currentMessage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.currentMessage.trim() || ctx_r0.isLoading);\n  }\n}\nfunction ChatComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Initializing chat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ChatComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.messages = [];\n    this.currentMessage = '';\n    this.isLoading = false;\n    this.isAuthenticated = false;\n    this.currentSessionId = null;\n    // Pagination\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.isLoadingHistory = false;\n    // Scroll management\n    this.shouldScrollToBottom = true;\n    this.lastScrollHeight = 0;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n  }\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: token => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory(1, false);\n      },\n      error: error => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n  // Listen for scroll events to load more history\n  onScroll(event) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    const clientHeight = element.clientHeight;\n    // Mark that user has scrolled manually (not programmatic)\n    if (this.initialLoadComplete) {\n      this.userHasScrolled = true;\n    }\n    // Clear existing timeout\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    // Debounce scroll events and check conditions\n    this.scrollTimeout = setTimeout(() => {\n      // Only load more if:\n      // 1. Initial load is complete\n      // 2. User has scrolled manually at least once\n      // 3. User is near the top (scrollTop < 100)\n      // 4. There are more pages to load\n      // 5. Not currently loading\n      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n      const isNearTop = scrollTop < 100;\n      const isNotAtBottom = scrollTop < scrollHeight - clientHeight - 50;\n      if (isNearTop && isNotAtBottom && this.hasNextPage && !this.isLoadingHistory && this.initialLoadComplete && this.userHasScrolled) {\n        this.lastScrollHeight = scrollHeight;\n        this.loadMoreHistory();\n      }\n    }, 100); // 100ms debounce\n  }\n\n  loadChatHistory(page = 1, append = false) {\n    if (!this.isAuthenticated) return;\n    this.isLoadingHistory = true;\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        if (append) {\n          // For pagination - reverse the new messages (since API returns newest first)\n          // and prepend older messages to beginning\n          const reversedNewMessages = [...newMessages].reverse();\n          this.messages = [...reversedNewMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - reverse messages to get chronological order (oldest first)\n          this.messages = [...newMessages].reverse();\n          this.shouldScrollToBottom = true;\n          // Set initial load complete after scroll positioning is done\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        this.isLoadingHistory = false;\n      },\n      error: error => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        // If this was the initial load, still mark it as complete after delay\n        if (!append) {\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n      }\n    });\n  }\n  // Convert Django ChatMessage to frontend Message format\n  convertChatMessageToMessage(chatMessage) {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined,\n      news_articles: chatMessage.news_articles || undefined\n    };\n  }\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n  // Maintain scroll position when loading older messages\n  maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    // Create temporary user message and add it instantly\n    const tempUserMessage = {\n      text: messageToSend,\n      isUser: true,\n      timestamp: new Date()\n    };\n    // Add user message instantly to the chat\n    this.messages.push(tempUserMessage);\n    this.shouldScrollToBottom = true;\n    // Set loading state\n    this.isLoading = true;\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        // Convert backend messages\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n        // Replace the temporary user message with the one from backend\n        const lastMessageIndex = this.messages.length - 1;\n        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n          this.messages[lastMessageIndex] = userMessage;\n        }\n        // Add bot message\n        this.messages.push(botMessage);\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        const errorMessage = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n  clearHistory() {\n    this.messages = [];\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n  refreshHistory() {\n    this.currentPage = 1;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.loadChatHistory();\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  adjustTextareaHeight(event) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n  // Format message text using marked library for markdown\n  formatMessageText(text) {\n    if (!text) return '';\n    try {\n      // Configure marked to be more restrictive for security\n      marked.setOptions({\n        breaks: true,\n        gfm: true // Enable GitHub Flavored Markdown\n      });\n      // Convert markdown to HTML using marked (synchronous)\n      const htmlContent = marked.parse(text);\n      // Basic sanitization - remove script tags and dangerous attributes\n      let sanitizedHtml = htmlContent.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '').replace(/javascript:/gi, '').replace(/on\\w+\\s*=/gi, '');\n      return sanitizedHtml;\n    } catch (error) {\n      console.error('Error formatting message text:', error);\n      // Fallback to plain text with basic line break conversion\n      return text.replace(/\\n/g, '<br>');\n    }\n  }\n  ngOnDestroy() {\n    // Clean up any pending timeouts\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function ChatComponent_Factory(t) {\n    return new (t || ChatComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChatComponent,\n    selectors: [[\"app-chat\"]],\n    viewQuery: function ChatComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n      }\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"chat-container\", 4, \"ngIf\"], [\"class\", \"auth-loading\", 4, \"ngIf\"], [1, \"chat-container\"], [\"class\", \"messages-container\", 3, \"scroll\", 4, \"ngIf\"], [1, \"input-container\"], [1, \"input-wrapper\"], [\"placeholder\", \"Ask anything\", \"rows\", \"1\", 1, \"message-input\", 3, \"ngModel\", \"ngModelChange\", \"keydown\", \"input\"], [\"messageTextarea\", \"\"], [1, \"send-button\", 3, \"disabled\", \"click\"], [\"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M2 21L23 12L2 3V10L17 12L2 14V21Z\", \"fill\", \"currentColor\"], [1, \"messages-container\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"pagination-loading\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"message bot-message\", 4, \"ngIf\"], [1, \"pagination-loading\"], [1, \"loading-spinner\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-text\", 3, \"innerHTML\"], [1, \"message-time\"], [\"class\", \"message-news\", 4, \"ngIf\"], [1, \"message-news\"], [3, \"news\", \"showTitle\", \"compact\"], [1, \"message\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"auth-loading\"]],\n    template: function ChatComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-anonymous-user\");\n        i0.ɵɵtemplate(1, ChatComponent_div_1_Template, 10, 7, \"div\", 0);\n        i0.ɵɵtemplate(2, ChatComponent_div_2_Template, 4, 0, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.AnonymousUserComponent, i5.NewsComponent, i2.DatePipe],\n    styles: [\"\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  max-width: 900px;\\n  margin: 0 auto;\\n  background: linear-gradient(to bottom, #ffffff 0%, #fefefe 100%);\\n  position: relative;\\n  overflow: hidden;\\n  font-family: var(--font-family-primary);\\n  border-radius: var(--radius-2xl);\\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);\\n  border: 1px solid rgba(255, 255, 255, 0.8);\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: var(--space-6) var(--space-8) 120px var(--space-8);\\n  background: #f9fafb;\\n  max-height: calc(100vh - 160px);\\n  scroll-behavior: smooth;\\n  position: relative;\\n  z-index: 10;\\n  \\n\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f5f9;\\n  border-radius: var(--radius-md);\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e1;\\n  border-radius: var(--radius-md);\\n  -webkit-transition: var(--transition-fast);\\n  transition: var(--transition-fast);\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #94a3b8;\\n}\\n\\n\\n\\n.pagination-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: var(--space-4) 0;\\n  margin-bottom: var(--space-4);\\n}\\n.pagination-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 2px solid #e2e8f0;\\n  border-top: 2px solid #bdf2bd;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n  display: flex;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.3s ease-out;\\n  position: relative;\\n}\\n.message.user-message[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #e94560 100%);\\n  color: #ffffff;\\n  max-width: 75%;\\n  border-radius: 24px 24px 8px 24px;\\n  box-shadow: 0 8px 32px rgba(233, 69, 96, 0.3), 0 4px 16px rgba(83, 52, 131, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.05);\\n  transition: all var(--transition-normal);\\n  position: relative;\\n  border: none;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  font-weight: 500;\\n  letter-spacing: 0.3px;\\n  \\n\\n  font-family: \\\"SF Pro Display\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, sans-serif;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n  \\n\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 30%, transparent 70%, rgba(233, 69, 96, 0.1) 100%);\\n  border-radius: 24px 24px 8px 24px;\\n  pointer-events: none;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(135deg, #e94560 0%, #533483 25%, #0f3460 50%, #16213e 75%, #1a1a2e 100%);\\n  border-radius: 26px 26px 10px 26px;\\n  z-index: -1;\\n  opacity: 0;\\n  transition: opacity var(--transition-normal);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px) scale(1.02);\\n  box-shadow: 0 16px 48px rgba(233, 69, 96, 0.4), 0 8px 24px rgba(83, 52, 131, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover::after {\\n  opacity: 0.3;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:active {\\n  transform: translateY(-1px) scale(1.01);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]::before {\\n  animation: premiumGlow 3s ease-in-out infinite alternate;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.15);\\n  color: #059669;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.05);\\n  border-color: rgba(0, 0, 0, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  color: #047857;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #059669;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #047857;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: #059669;\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(0, 0, 0, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.message.bot-message[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  \\n\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\\n  color: var(--color-gray-800);\\n  max-width: 75%;\\n  border-radius: 20px 20px 20px 6px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  border: 1px solid var(--color-gray-200);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\\n  border-color: var(--color-gray-300);\\n  \\n\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover   .message-news[_ngcontent-%COMP%] {\\n  margin-top: var(--space-3);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message.bot-message[_ngcontent-%COMP%] {\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-news[_ngcontent-%COMP%] {\\n  max-width: 80%;\\n  margin-left: 0;\\n  align-self: flex-start;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: var(--space-4) var(--space-5);\\n  word-wrap: break-word;\\n  position: relative;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-base);\\n  line-height: 1.6;\\n  margin: 0;\\n  font-weight: var(--font-weight-normal);\\n  word-break: break-word;\\n}\\n.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--space-3) 0;\\n}\\n.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: var(--space-4) 0 var(--space-2) 0;\\n  font-weight: var(--font-weight-semibold);\\n  line-height: 1.3;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.1em;\\n}\\n.message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-size: 1em;\\n}\\n.message-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   b[_ngcontent-%COMP%] {\\n  font-weight: var(--font-weight-semibold);\\n}\\n.message-text[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-style: italic;\\n}\\n.message-text[_ngcontent-%COMP%]   del[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   strike[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n}\\n.message-text[_ngcontent-%COMP%]   u[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: var(--space-3) 0;\\n  padding-left: var(--space-6);\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: var(--space-1) 0;\\n  line-height: 1.5;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  margin: var(--space-4) 0;\\n  padding: var(--space-3) var(--space-4);\\n  border-left: 4px solid #e5e7eb;\\n  background: rgba(0, 0, 0, 0.02);\\n  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;\\n  font-style: italic;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.08);\\n  padding: 2px 6px;\\n  border-radius: var(--radius-sm);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 0.9em;\\n  color: #e11d48;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: var(--radius-md);\\n  padding: var(--space-4);\\n  margin: var(--space-4) 0;\\n  overflow-x: auto;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: none;\\n  padding: 0;\\n  color: #334155;\\n  font-size: 0.875em;\\n}\\n.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #2563eb;\\n  text-decoration: underline;\\n  transition: var(--transition-fast);\\n}\\n.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #1d4ed8;\\n  text-decoration: none;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  margin: var(--space-4) 0;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: var(--space-2) var(--space-3);\\n  border: 1px solid #e2e8f0;\\n  text-align: left;\\n}\\n.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  font-weight: var(--font-weight-semibold);\\n}\\n.message-text[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  border-radius: var(--radius-sm);\\n  margin: var(--space-2) 0;\\n}\\n\\n.message-time[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-xs);\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-top: var(--space-2);\\n  text-align: right;\\n  opacity: 0.8;\\n  transition: var(--transition-fast);\\n  font-weight: var(--font-weight-normal);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: left;\\n  color: var(--color-gray-600);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: right;\\n  color: var(--color-gray-600);\\n}\\n\\n.message[_ngcontent-%COMP%]:hover   .message-time[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  padding: var(--space-6) var(--space-8) var(--space-8) var(--space-8);\\n  background: linear-gradient(to top, #ffffff 0%, #fefefe 100%);\\n  position: fixed;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 100%;\\n  max-width: 900px;\\n  z-index: 50;\\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\\n  transition: var(--transition-fast);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.input-container.centered[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 100%;\\n  max-width: 600px;\\n  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);\\n  z-index: 50;\\n  padding: var(--space-8);\\n  bottom: auto;\\n  border-radius: var(--radius-2xl);\\n  border: 1px solid rgba(0, 0, 0, 0.06);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12), 0 8px 24px rgba(0, 0, 0, 0.08);\\n}\\n.input-container.bottom[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\\n  background: linear-gradient(to top, #ffffff 0%, #fefefe 100%);\\n  width: 100%;\\n  max-width: 900px;\\n  z-index: 50;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.input-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: var(--space-4);\\n  border: 2px solid var(--color-gray-200);\\n  border-radius: 28px;\\n  padding: var(--space-4) var(--space-5);\\n  background: var(--color-white);\\n  transition: all var(--transition-normal);\\n  position: relative;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n}\\n.input-wrapper[_ngcontent-%COMP%]:focus-within {\\n  border-color: var(--color-primary);\\n  background: var(--color-white);\\n  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.08), 0 4px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n.input-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-gray-300);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  background: transparent;\\n  resize: none;\\n  font-size: var(--font-size-base);\\n  line-height: 1.6;\\n  padding: var(--space-2) 0;\\n  min-height: 28px;\\n  max-height: 120px;\\n  font-family: var(--font-family-primary);\\n  overflow-y: auto;\\n  transition: var(--transition-fast);\\n  color: var(--color-gray-900);\\n  font-weight: var(--font-weight-normal);\\n  \\n\\n}\\n.message-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--color-gray-500);\\n  opacity: 1;\\n  font-weight: var(--font-weight-normal);\\n}\\n.message-input[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e1;\\n  border-radius: var(--radius-sm);\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #94a3b8;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--color-white);\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: all var(--transition-normal);\\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.send-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);\\n  border-radius: 50%;\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)::before {\\n  opacity: 1;\\n}\\n.send-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(-1px) scale(1.02);\\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled {\\n  background: var(--color-gray-400);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled::before {\\n  display: none;\\n}\\n.send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  transition: var(--transition-fast);\\n  transform: translateX(1px); \\n\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)   svg[_ngcontent-%COMP%] {\\n  transform: translateX(1px) scale(1.1);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-1);\\n  padding: var(--space-1) 0;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  background: #94a3b8;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n\\n\\n.auth-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  color: var(--color-gray-600);\\n  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 50%, #f0f4f8 100%);\\n}\\n.auth-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border: 4px solid var(--color-gray-200);\\n  border-top: 4px solid var(--color-accent);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: var(--space-4);\\n}\\n.auth-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-lg);\\n  color: var(--color-gray-600);\\n  margin: 0;\\n  font-weight: var(--font-weight-medium);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    opacity: 0.3;\\n    transform: scale(0.7) translateY(0);\\n  }\\n  30% {\\n    opacity: 1;\\n    transform: scale(1.3) translateY(-6px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(40px) scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .chat-container[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    height: 100vh;\\n    border-radius: 0;\\n    border: none;\\n  }\\n  .messages-container[_ngcontent-%COMP%] {\\n    padding: var(--space-5) var(--space-5) 140px var(--space-5);\\n  }\\n  .input-container[_ngcontent-%COMP%] {\\n    padding: var(--space-5) var(--space-5) var(--space-6) var(--space-5);\\n    max-width: 100%;\\n  }\\n  .input-container.centered[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    padding: var(--space-5);\\n    margin: 0 var(--space-4);\\n    width: calc(100% - var(--space-8));\\n  }\\n  .message[_ngcontent-%COMP%] {\\n    margin-bottom: var(--space-5);\\n  }\\n  .message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%], .message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n    max-width: 88%;\\n  }\\n  .input-wrapper[_ngcontent-%COMP%] {\\n    padding: var(--space-1) var(--space-2) var(--space-1) var(--space-4);\\n    gap: var(--space-2);\\n  }\\n  .send-button[_ngcontent-%COMP%] {\\n    width: 42px;\\n    height: 42px;\\n  }\\n  .send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n\\n\\n*[_ngcontent-%COMP%] {\\n  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast), transform var(--transition-fast), box-shadow var(--transition-fast);\\n}\\n\\n\\n\\n.send-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--color-primary);\\n  outline-offset: 3px;\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%]::selection {\\n  background: rgba(16, 185, 129, 0.2);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]::selection {\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n  30% {\\n    transform: translateY(-10px);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["marked", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "message_r8", "news_articles", "ɵɵtext", "ɵɵtemplate", "ChatComponent_div_1_div_1_div_3_div_6_Template", "ɵɵpureFunction2", "_c1", "isUser", "ctx_r6", "formatMessageText", "text", "ɵɵsanitizeHtml", "ɵɵtextInterpolate", "ɵɵpipeBind2", "timestamp", "length", "ɵɵlistener", "ChatComponent_div_1_div_1_Template_div_scroll_0_listener", "$event", "ɵɵrestoreView", "_r12", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "onScroll", "ChatComponent_div_1_div_1_div_2_Template", "ChatComponent_div_1_div_1_div_3_Template", "ChatComponent_div_1_div_1_div_4_Template", "ctx_r2", "isLoadingHistory", "messages", "isLoading", "ChatComponent_div_1_div_1_Template", "ChatComponent_div_1_Template_textarea_ngModelChange_4_listener", "_r14", "ctx_r13", "currentMessage", "ChatComponent_div_1_Template_textarea_keydown_4_listener", "ctx_r15", "onKeyPress", "ChatComponent_div_1_Template_textarea_input_4_listener", "ctx_r16", "adjustTextareaHeight", "ChatComponent_div_1_Template_button_click_7_listener", "ctx_r17", "sendMessage", "ɵɵnamespaceSVG", "ctx_r0", "ɵɵclassProp", "trim", "ChatComponent", "constructor", "authService", "isAuthenticated", "currentSessionId", "currentPage", "hasNextPage", "shouldScrollToBottom", "lastScrollHeight", "initialLoadComplete", "userHasScrolled", "ngOnInit", "ensureAuthenticated", "subscribe", "next", "token", "console", "log", "loadChatHistory", "error", "ngAfterViewChecked", "scrollToBottom", "event", "element", "target", "scrollTop", "scrollHeight", "clientHeight", "scrollTimeout", "clearTimeout", "setTimeout", "isNearTop", "isNotAtBottom", "loadMoreHistory", "page", "append", "undefined", "response", "newMessages", "results", "map", "msg", "convertChatMessageToMessage", "reversedNewMessages", "reverse", "maintainScrollPosition", "chatMessage", "id", "message", "sender", "Date", "session", "prompt", "model", "messagesContainer", "nativeElement", "newScrollHeight", "scrollDifference", "messageToSend", "tempUserMessage", "push", "sendMessageToChatbot", "userMessage", "user_message", "botMessage", "bot_message", "lastMessageIndex", "errorMessage", "clearHistory", "refreshHistory", "key", "shift<PERSON>ey", "preventDefault", "textarea", "style", "height", "Math", "min", "setOptions", "breaks", "gfm", "htmlContent", "parse", "sanitizedHtml", "replace", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "viewQuery", "ChatComponent_Query", "rf", "ctx", "ChatComponent_div_1_Template", "ChatComponent_div_2_Template"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\chat\\chat.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\chat\\chat.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ViewChild, AfterViewChecked } from '@angular/core';\nimport { AuthService } from '../auth.service'; // Adjust path as needed\nimport { marked } from 'marked';\n\ninterface NewsArticle {\n  id?: number;\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string;\n  description: string;\n  fetched_at?: string;\n  created_at?: string;\n}\n\ninterface Message {\n  id?: number;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n  session?: number;\n  prompt?: number;\n  model?: number;\n  news_articles?: NewsArticle[]; // for bot messages that include news\n}\n\n// Django ChatMessage structure\ninterface ChatMessage {\n  id: number;\n  session: number;\n  sender: 'user' | 'bot';\n  message: string;\n  timestamp: string;\n  prompt: number | null;\n  model: number | null;\n  news_articles?: NewsArticle[] | null;\n}\n\n// Django paginated response\ninterface ChatHistoryResponse {\n  count: number;\n  next: string | null;\n  previous: string | null;\n  results: ChatMessage[];\n}\n\n// Django chatbot response\ninterface ChatbotResponse {\n  user_message: ChatMessage;\n  bot_message: ChatMessage;\n}\n\n@Component({\n  selector: 'app-chat',\n  templateUrl: './chat.component.html',\n  styleUrls: ['./chat.component.scss']\n})\nexport class ChatComponent implements OnInit, OnDestroy, AfterViewChecked {\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  \n  messages: Message[] = [];\n  currentMessage: string = '';\n  isLoading: boolean = false;\n  isAuthenticated: boolean = false;\n  currentSessionId: number | null = null;\n  \n  // Pagination\n  currentPage: number = 1;\n  hasNextPage: boolean = false;\n  isLoadingHistory: boolean = false;\n  \n  // Scroll management\n  private shouldScrollToBottom: boolean = true;\n  private lastScrollHeight: number = 0;\n  private initialLoadComplete: boolean = false;\n  private userHasScrolled: boolean = false;\n  private scrollTimeout: any;\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: (token) => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory(1, false);\n      },\n      error: (error) => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n\n  // Listen for scroll events to load more history\n  onScroll(event: any) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    const clientHeight = element.clientHeight;\n    \n    // Mark that user has scrolled manually (not programmatic)\n    if (this.initialLoadComplete) {\n      this.userHasScrolled = true;\n    }\n    \n    // Clear existing timeout\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    \n    // Debounce scroll events and check conditions\n    this.scrollTimeout = setTimeout(() => {\n      // Only load more if:\n      // 1. Initial load is complete\n      // 2. User has scrolled manually at least once\n      // 3. User is near the top (scrollTop < 100)\n      // 4. There are more pages to load\n      // 5. Not currently loading\n      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n      const isNearTop = scrollTop < 100;\n      const isNotAtBottom = scrollTop < (scrollHeight - clientHeight - 50);\n      \n      if (isNearTop && \n          isNotAtBottom &&\n          this.hasNextPage && \n          !this.isLoadingHistory && \n          this.initialLoadComplete &&\n          this.userHasScrolled) {\n        this.lastScrollHeight = scrollHeight;\n        this.loadMoreHistory();\n      }\n    }, 100); // 100ms debounce\n  }\n\n  loadChatHistory(page: number = 1, append: boolean = false) {\n    if (!this.isAuthenticated) return;\n\n    this.isLoadingHistory = true;\n\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatHistoryResponse) => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        \n        if (append) {\n          // For pagination - reverse the new messages (since API returns newest first)\n          // and prepend older messages to beginning\n          const reversedNewMessages = [...newMessages].reverse();\n          this.messages = [...reversedNewMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - reverse messages to get chronological order (oldest first)\n          this.messages = [...newMessages].reverse();\n          this.shouldScrollToBottom = true;\n          \n          // Set initial load complete after scroll positioning is done\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n        \n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        \n        this.isLoadingHistory = false;\n      },\n      error: (error) => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        \n        // If this was the initial load, still mark it as complete after delay\n        if (!append) {\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n      }\n    });\n  }\n\n  // Convert Django ChatMessage to frontend Message format\n  private convertChatMessageToMessage(chatMessage: ChatMessage): Message {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined,\n      news_articles: chatMessage.news_articles || undefined,\n    };\n  }\n\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n\n  // Maintain scroll position when loading older messages\n  private maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n\n    // Create temporary user message and add it instantly\n    const tempUserMessage: Message = {\n      text: messageToSend,\n      isUser: true,\n      timestamp: new Date()\n    };\n\n    // Add user message instantly to the chat\n    this.messages.push(tempUserMessage);\n    this.shouldScrollToBottom = true;\n\n    // Set loading state\n    this.isLoading = true;\n\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatbotResponse) => {\n        // Convert backend messages\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n\n        // Replace the temporary user message with the one from backend\n        const lastMessageIndex = this.messages.length - 1;\n        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n          this.messages[lastMessageIndex] = userMessage;\n        }\n\n        // Add bot message\n        this.messages.push(botMessage);\n\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        const errorMessage: Message = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n\n  clearHistory() {\n    this.messages = [];\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n\n  refreshHistory() {\n    this.currentPage = 1;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.loadChatHistory();\n  }\n\n  onKeyPress(event: KeyboardEvent) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  adjustTextareaHeight(event: any) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n\n  private scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n\n  // Format message text using marked library for markdown\n  formatMessageText(text: string): string {\n    if (!text) return '';\n\n    try {\n      // Configure marked to be more restrictive for security\n      marked.setOptions({\n        breaks: true, // Convert line breaks to <br>\n        gfm: true // Enable GitHub Flavored Markdown\n      });\n\n      // Convert markdown to HTML using marked (synchronous)\n      const htmlContent = marked.parse(text) as string;\n\n      // Basic sanitization - remove script tags and dangerous attributes\n      let sanitizedHtml = htmlContent\n        .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n        .replace(/javascript:/gi, '')\n        .replace(/on\\w+\\s*=/gi, '');\n\n      return sanitizedHtml;\n    } catch (error) {\n      console.error('Error formatting message text:', error);\n      // Fallback to plain text with basic line break conversion\n      return text.replace(/\\n/g, '<br>');\n    }\n  }\n\n  ngOnDestroy() {\n    // Clean up any pending timeouts\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n}", "<!-- chat.component.html -->\n\n<!-- Anonymous user component runs in background -->\n<app-anonymous-user></app-anonymous-user>\n\n<div class=\"chat-container\" *ngIf=\"isAuthenticated\">\n  <!-- Messages container - only show when there are messages -->\n  <div \n    #messagesContainer\n    class=\"messages-container\" \n    (scroll)=\"onScroll($event)\"\n    *ngIf=\"messages.length > 0\">\n    \n    <!-- Loading indicator for pagination at the top -->\n    <div class=\"pagination-loading\" *ngIf=\"isLoadingHistory\">\n      <div class=\"loading-spinner\"></div>\n    </div>\n    \n    <div class=\"message\"\n         *ngFor=\"let message of messages\"\n         [ngClass]=\"{'user-message': message.isUser, 'bot-message': !message.isUser}\">\n      <div class=\"message-content\">\n        <div class=\"message-text\" [innerHTML]=\"formatMessageText(message.text)\"></div>\n        <div class=\"message-time\">{{ message.timestamp | date:'short' }}</div>\n      </div>\n\n      <!-- Render news articles UNDER the bot message bubble when present -->\n      <div class=\"message-news\"\n           *ngIf=\"!message.isUser && message.news_articles && message.news_articles.length\">\n        <app-news\n          [news]=\"message.news_articles\"\n          [showTitle]=\"false\"\n          [compact]=\"false\"></app-news>\n      </div>\n    </div>\n\n    <!-- Loading indicator for current message -->\n    <div class=\"message bot-message\" *ngIf=\"isLoading\">\n      <div class=\"message-content\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input container - always visible -->\n  <div class=\"input-container\" [class.centered]=\"messages.length === 0\" [class.bottom]=\"messages.length > 0\">\n    <div class=\"input-wrapper\">\n      <textarea\n        #messageTextarea\n        [(ngModel)]=\"currentMessage\"\n        (keydown)=\"onKeyPress($event)\"\n        (input)=\"adjustTextareaHeight($event)\"\n        placeholder=\"Ask anything\"\n        class=\"message-input\"\n        rows=\"1\">\n      </textarea>\n      <button\n        (click)=\"sendMessage()\"\n        class=\"send-button\"\n        [disabled]=\"!currentMessage.trim() || isLoading\">\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"currentColor\"/>\n        </svg>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Show loading message while authenticating -->\n<div *ngIf=\"!isAuthenticated\" class=\"auth-loading\">\n  <div class=\"loading-spinner\"></div>\n  <p>Initializing chat...</p>\n</div>"], "mappings": "AAEA,SAASA,MAAM,QAAQ,QAAQ;;;;;;;;;;ICY3BC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,cAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWJH,EAAA,CAAAC,cAAA,cACsF;IACpFD,EAAA,CAAAE,SAAA,mBAG+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;;;;IAHFH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,UAAA,SAAAC,UAAA,CAAAC,aAAA,CAA8B;;;;;;;;;;;IAZpCP,EAAA,CAAAC,cAAA,cAEkF;IAE9ED,EAAA,CAAAE,SAAA,cAA8E;IAC9EF,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAQ,MAAA,GAAsC;;IAAAR,EAAA,CAAAG,YAAA,EAAM;IAIxEH,EAAA,CAAAS,UAAA,IAAAC,8CAAA,kBAMM;IACRV,EAAA,CAAAG,YAAA,EAAM;;;;;IAdDH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,IAAAC,GAAA,EAAAN,UAAA,CAAAO,MAAA,GAAAP,UAAA,CAAAO,MAAA,EAA4E;IAEnDb,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,UAAA,cAAAS,MAAA,CAAAC,iBAAA,CAAAT,UAAA,CAAAU,IAAA,GAAAhB,EAAA,CAAAiB,cAAA,CAA6C;IAC7CjB,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAmB,WAAA,OAAAb,UAAA,CAAAc,SAAA,WAAsC;IAK5DpB,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAK,UAAA,UAAAC,UAAA,CAAAO,MAAA,IAAAP,UAAA,CAAAC,aAAA,IAAAD,UAAA,CAAAC,aAAA,CAAAc,MAAA,CAA8E;;;;;IAStFrB,EAAA,CAAAC,cAAA,cAAmD;IAG7CD,EAAA,CAAAE,SAAA,WAAa;IAGfF,EAAA,CAAAG,YAAA,EAAM;;;;;;IApCZH,EAAA,CAAAC,cAAA,kBAI8B;IAD5BD,EAAA,CAAAsB,UAAA,oBAAAC,yDAAAC,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAU5B,EAAA,CAAA6B,WAAA,CAAAF,OAAA,CAAAG,QAAA,CAAAN,MAAA,CAAgB;IAAA,EAAC;IAI3BxB,EAAA,CAAAS,UAAA,IAAAsB,wCAAA,kBAEM;IAEN/B,EAAA,CAAAS,UAAA,IAAAuB,wCAAA,mBAgBM;IAGNhC,EAAA,CAAAS,UAAA,IAAAwB,wCAAA,kBAQM;IACRjC,EAAA,CAAAG,YAAA,EAAM;;;;IAhC6BH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,UAAA,SAAA6B,MAAA,CAAAC,gBAAA,CAAsB;IAK9BnC,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,UAAA,YAAA6B,MAAA,CAAAE,QAAA,CAAW;IAkBFpC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,UAAA,SAAA6B,MAAA,CAAAG,SAAA,CAAe;;;;;;IAhCrDrC,EAAA,CAAAC,cAAA,aAAoD;IAElDD,EAAA,CAAAS,UAAA,IAAA6B,kCAAA,iBAuCM;IAGNtC,EAAA,CAAAC,cAAA,aAA2G;IAIrGD,EAAA,CAAAsB,UAAA,2BAAAiB,+DAAAf,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAAY,OAAA,CAAAC,cAAA,GAAAlB,MAAA;IAAA,EAA4B,qBAAAmB,yDAAAnB,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAI,OAAA,GAAA5C,EAAA,CAAA4B,aAAA;MAAA,OACjB5B,EAAA,CAAA6B,WAAA,CAAAe,OAAA,CAAAC,UAAA,CAAArB,MAAA,CAAkB;IAAA,EADD,mBAAAsB,uDAAAtB,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAO,OAAA,GAAA/C,EAAA,CAAA4B,aAAA;MAAA,OAEnB5B,EAAA,CAAA6B,WAAA,CAAAkB,OAAA,CAAAC,oBAAA,CAAAxB,MAAA,CAA4B;IAAA,EAFT;IAM9BxB,EAAA,CAAAQ,MAAA;IAAAR,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,gBAGmD;IAFjDD,EAAA,CAAAsB,UAAA,mBAAA2B,qDAAA;MAAAjD,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAU,OAAA,GAAAlD,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAqB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvBnD,EAAA,CAAAoD,cAAA,EAA+F;IAA/FpD,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAE,SAAA,eAAiE;IACnEF,EAAA,CAAAG,YAAA,EAAM;;;;IAvDTH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,UAAA,SAAAgD,MAAA,CAAAjB,QAAA,CAAAf,MAAA,KAAyB;IAsCCrB,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAsD,WAAA,aAAAD,MAAA,CAAAjB,QAAA,CAAAf,MAAA,OAAwC,WAAAgC,MAAA,CAAAjB,QAAA,CAAAf,MAAA;IAI/DrB,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,UAAA,YAAAgD,MAAA,CAAAX,cAAA,CAA4B;IAU5B1C,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,UAAA,cAAAgD,MAAA,CAAAX,cAAA,CAAAa,IAAA,MAAAF,MAAA,CAAAhB,SAAA,CAAgD;;;;;IAUxDrC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAQ,MAAA,2BAAoB;IAAAR,EAAA,CAAAG,YAAA,EAAI;;;ADjB7B,OAAM,MAAOqD,aAAa;EAqBxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAlB/B,KAAAtB,QAAQ,GAAc,EAAE;IACxB,KAAAM,cAAc,GAAW,EAAE;IAC3B,KAAAL,SAAS,GAAY,KAAK;IAC1B,KAAAsB,eAAe,GAAY,KAAK;IAChC,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAA3B,gBAAgB,GAAY,KAAK;IAEjC;IACQ,KAAA4B,oBAAoB,GAAY,IAAI;IACpC,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,eAAe,GAAY,KAAK;EAGO;EAE/CC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,WAAW,CAACU,mBAAmB,EAAE,CAACC,SAAS,CAAC;MAC/CC,IAAI,EAAGC,KAAK,IAAI;QACdC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,IAAI,CAACd,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACe,eAAe,CAAC,CAAC,EAAE,KAAK,CAAC;MAChC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChB,eAAe,GAAG,KAAK;MAC9B;KACD,CAAC;EACJ;EAEAiB,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACb,oBAAoB,EAAE;MAC7B,IAAI,CAACc,cAAc,EAAE;MACrB,IAAI,CAACd,oBAAoB,GAAG,KAAK;;EAErC;EAEA;EACAjC,QAAQA,CAACgD,KAAU;IACjB,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM;IAC5B,MAAMC,SAAS,GAAGF,OAAO,CAACE,SAAS;IACnC,MAAMC,YAAY,GAAGH,OAAO,CAACG,YAAY;IACzC,MAAMC,YAAY,GAAGJ,OAAO,CAACI,YAAY;IAEzC;IACA,IAAI,IAAI,CAAClB,mBAAmB,EAAE;MAC5B,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B;IACA,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC;IACA,IAAI,CAACA,aAAa,GAAGE,UAAU,CAAC,MAAK;MACnC;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMC,SAAS,GAAGN,SAAS,GAAG,GAAG;MACjC,MAAMO,aAAa,GAAGP,SAAS,GAAIC,YAAY,GAAGC,YAAY,GAAG,EAAG;MAEpE,IAAII,SAAS,IACTC,aAAa,IACb,IAAI,CAAC1B,WAAW,IAChB,CAAC,IAAI,CAAC3B,gBAAgB,IACtB,IAAI,CAAC8B,mBAAmB,IACxB,IAAI,CAACC,eAAe,EAAE;QACxB,IAAI,CAACF,gBAAgB,GAAGkB,YAAY;QACpC,IAAI,CAACO,eAAe,EAAE;;IAE1B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;;EAEAf,eAAeA,CAACgB,IAAA,GAAe,CAAC,EAAEC,MAAA,GAAkB,KAAK;IACvD,IAAI,CAAC,IAAI,CAAChC,eAAe,EAAE;IAE3B,IAAI,CAACxB,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAACuB,WAAW,CAACgB,eAAe,CAACgB,IAAI,EAAE,IAAI,CAAC9B,gBAAgB,IAAIgC,SAAS,CAAC,CAACvB,SAAS,CAAC;MACnFC,IAAI,EAAGuB,QAA6B,IAAI;QACtC,MAAMC,WAAW,GAAGD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,GAAG,IAAI,IAAI,CAACC,2BAA2B,CAACD,GAAG,CAAC,CAAC;QAEtF,IAAIN,MAAM,EAAE;UACV;UACA;UACA,MAAMQ,mBAAmB,GAAG,CAAC,GAAGL,WAAW,CAAC,CAACM,OAAO,EAAE;UACtD,IAAI,CAAChE,QAAQ,GAAG,CAAC,GAAG+D,mBAAmB,EAAE,GAAG,IAAI,CAAC/D,QAAQ,CAAC;UAC1D,IAAI,CAACiE,sBAAsB,EAAE;SAC9B,MAAM;UACL;UACA,IAAI,CAACjE,QAAQ,GAAG,CAAC,GAAG0D,WAAW,CAAC,CAACM,OAAO,EAAE;UAC1C,IAAI,CAACrC,oBAAoB,GAAG,IAAI;UAEhC;UACAuB,UAAU,CAAC,MAAK;YACd,IAAI,CAACrB,mBAAmB,GAAG,IAAI;UACjC,CAAC,EAAE,GAAG,CAAC;;QAGT;QACA,IAAI,CAACJ,WAAW,GAAG6B,IAAI;QACvB,IAAI,CAAC5B,WAAW,GAAG,CAAC,CAAC+B,QAAQ,CAACvB,IAAI;QAElC,IAAI,CAACnC,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACxC,gBAAgB,GAAG,KAAK;QAE7B;QACA,IAAI,CAACwD,MAAM,EAAE;UACXL,UAAU,CAAC,MAAK;YACd,IAAI,CAACrB,mBAAmB,GAAG,IAAI;UACjC,CAAC,EAAE,GAAG,CAAC;;MAEX;KACD,CAAC;EACJ;EAEA;EACQiC,2BAA2BA,CAACI,WAAwB;IAC1D,OAAO;MACLC,EAAE,EAAED,WAAW,CAACC,EAAE;MAClBvF,IAAI,EAAEsF,WAAW,CAACE,OAAO;MACzB3F,MAAM,EAAEyF,WAAW,CAACG,MAAM,KAAK,MAAM;MACrCrF,SAAS,EAAE,IAAIsF,IAAI,CAACJ,WAAW,CAAClF,SAAS,CAAC;MAC1CuF,OAAO,EAAEL,WAAW,CAACK,OAAO;MAC5BC,MAAM,EAAEN,WAAW,CAACM,MAAM,IAAIhB,SAAS;MACvCiB,KAAK,EAAEP,WAAW,CAACO,KAAK,IAAIjB,SAAS;MACrCrF,aAAa,EAAE+F,WAAW,CAAC/F,aAAa,IAAIqF;KAC7C;EACH;EAEA;EACAH,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3B,WAAW,IAAI,CAAC,IAAI,CAAC3B,gBAAgB,EAAE;MAC9C,IAAI,CAACuC,eAAe,CAAC,IAAI,CAACb,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;;EAEpD;EAEA;EACQwC,sBAAsBA,CAAA;IAC5Bf,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACwB,iBAAiB,EAAE;QAC1B,MAAM/B,OAAO,GAAG,IAAI,CAAC+B,iBAAiB,CAACC,aAAa;QACpD,MAAMC,eAAe,GAAGjC,OAAO,CAACG,YAAY;QAC5C,MAAM+B,gBAAgB,GAAGD,eAAe,GAAG,IAAI,CAAChD,gBAAgB;QAChEe,OAAO,CAACE,SAAS,GAAGgC,gBAAgB;;IAExC,CAAC,EAAE,EAAE,CAAC;EACR;EAEA9D,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACT,cAAc,CAACa,IAAI,EAAE,IAAI,IAAI,CAAClB,SAAS,IAAI,CAAC,IAAI,CAACsB,eAAe,EAAE;MAC1E;;IAGF;IACA,MAAMuD,aAAa,GAAG,IAAI,CAACxE,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IAExB;IACA,MAAMyE,eAAe,GAAY;MAC/BnG,IAAI,EAAEkG,aAAa;MACnBrG,MAAM,EAAE,IAAI;MACZO,SAAS,EAAE,IAAIsF,IAAI;KACpB;IAED;IACA,IAAI,CAACtE,QAAQ,CAACgF,IAAI,CAACD,eAAe,CAAC;IACnC,IAAI,CAACpD,oBAAoB,GAAG,IAAI;IAEhC;IACA,IAAI,CAAC1B,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACqB,WAAW,CAAC2D,oBAAoB,CAACH,aAAa,EAAE,IAAI,CAACtD,gBAAgB,IAAIgC,SAAS,CAAC,CAACvB,SAAS,CAAC;MACjGC,IAAI,EAAGuB,QAAyB,IAAI;QAClC;QACA,MAAMyB,WAAW,GAAG,IAAI,CAACpB,2BAA2B,CAACL,QAAQ,CAAC0B,YAAY,CAAC;QAC3E,MAAMC,UAAU,GAAG,IAAI,CAACtB,2BAA2B,CAACL,QAAQ,CAAC4B,WAAW,CAAC;QAEzE;QACA,MAAMC,gBAAgB,GAAG,IAAI,CAACtF,QAAQ,CAACf,MAAM,GAAG,CAAC;QACjD,IAAIqG,gBAAgB,IAAI,CAAC,IAAI,IAAI,CAACtF,QAAQ,CAACsF,gBAAgB,CAAC,CAAC7G,MAAM,EAAE;UACnE,IAAI,CAACuB,QAAQ,CAACsF,gBAAgB,CAAC,GAAGJ,WAAW;;QAG/C;QACA,IAAI,CAAClF,QAAQ,CAACgF,IAAI,CAACI,UAAU,CAAC;QAE9B;QACA,IAAI,CAAC,IAAI,CAAC5D,gBAAgB,EAAE;UAC1B,IAAI,CAACA,gBAAgB,GAAGiC,QAAQ,CAAC0B,YAAY,CAACZ,OAAO;;QAGvD,IAAI,CAACtE,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC0B,oBAAoB,GAAG,IAAI;MAClC,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMgD,YAAY,GAAY;UAC5B3G,IAAI,EAAE,sEAAsE;UAC5EH,MAAM,EAAE,KAAK;UACbO,SAAS,EAAE,IAAIsF,IAAI;SACpB;QACD,IAAI,CAACtE,QAAQ,CAACgF,IAAI,CAACO,YAAY,CAAC;QAChC,IAAI,CAACtF,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC0B,oBAAoB,GAAG,IAAI;MAClC;KACD,CAAC;EACJ;EAEA6D,YAAYA,CAAA;IACV,IAAI,CAACxF,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACyB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEAyC,cAAcA,CAAA;IACZ,IAAI,CAAChE,WAAW,GAAG,CAAC;IACpB,IAAI,CAACI,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAElC,IAAI,CAACV,eAAe,EAAE;EACxB;EAEA7B,UAAUA,CAACiC,KAAoB;IAC7B,IAAIA,KAAK,CAACgD,GAAG,KAAK,OAAO,IAAI,CAAChD,KAAK,CAACiD,QAAQ,EAAE;MAC5CjD,KAAK,CAACkD,cAAc,EAAE;MACtB,IAAI,CAAC7E,WAAW,EAAE;;EAEtB;EAEAH,oBAAoBA,CAAC8B,KAAU;IAC7B,MAAMmD,QAAQ,GAAGnD,KAAK,CAACE,MAAM;IAC7BiD,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;IAC9BF,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC/C,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;EACrE;EAEQL,cAAcA,CAAA;IACpBS,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACwB,iBAAiB,EAAE;QAC1B,MAAM/B,OAAO,GAAG,IAAI,CAAC+B,iBAAiB,CAACC,aAAa;QACpDhC,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,EAAE,CAAC;EACR;EAEA;EACAnE,iBAAiBA,CAACC,IAAY;IAC5B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,IAAI;MACF;MACAjB,MAAM,CAACuI,UAAU,CAAC;QAChBC,MAAM,EAAE,IAAI;QACZC,GAAG,EAAE,IAAI,CAAC;OACX,CAAC;MAEF;MACA,MAAMC,WAAW,GAAG1I,MAAM,CAAC2I,KAAK,CAAC1H,IAAI,CAAW;MAEhD;MACA,IAAI2H,aAAa,GAAGF,WAAW,CAC5BG,OAAO,CAAC,qDAAqD,EAAE,EAAE,CAAC,CAClEA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;MAE7B,OAAOD,aAAa;KACrB,CAAC,OAAOhE,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACA,OAAO3D,IAAI,CAAC4H,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;;EAEtC;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACzD,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAAC,QAAA0D,CAAA,G;qBA/SUtF,aAAa,EAAAxD,EAAA,CAAA+I,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAb1F,aAAa;IAAA2F,SAAA;IAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCvD1BtJ,EAAA,CAAAE,SAAA,yBAAyC;QAEzCF,EAAA,CAAAS,UAAA,IAAA+I,4BAAA,kBAiEM;QAGNxJ,EAAA,CAAAS,UAAA,IAAAgJ,4BAAA,iBAGM;;;QAvEuBzJ,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAAK,UAAA,SAAAkJ,GAAA,CAAA5F,eAAA,CAAqB;QAoE5C3D,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAK,UAAA,UAAAkJ,GAAA,CAAA5F,eAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}