/* chat.component.scss - Clean Modern Chat Interface */

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;           /* Changed from 100vh to 100% */
  max-width: 900px;
  margin: 0 auto;
  background: linear-gradient(to bottom, #ffffff 0%, #fefefe 100%);
  position: relative;     /* Ensure this is present */
  overflow: hidden;
  font-family: var(--font-family-primary);
  border-radius: var(--radius-2xl);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-6) var(--space-8) 120px var(--space-8);
  background: #f9fafb;
  max-height: calc(100vh - 160px);
  scroll-behavior: smooth;
  position: relative;
  z-index: 10;

  /* Clean scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: var(--radius-md);
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);

    &:hover {
      background: #94a3b8;
    }
  }
}

/* Clean loading spinner */
.pagination-loading {
  display: flex;
  justify-content: center;
  padding: var(--space-4) 0;
  margin-bottom: var(--space-4);

  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #bdf2bd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.message {
  margin-bottom: var(--space-4);
  display: flex;
  animation: fadeInUp 0.3s ease-out;
  position: relative;

  &.user-message {
    justify-content: flex-end;

    .message-content {
      background: linear-gradient(135deg, #d0ceff 0%, #e6e6ff 100%);
      color: #ffffff;
      max-width: 75%;
      border-radius: 20px 20px 6px 20px;
      box-shadow: 0 3px 12px rgba(79, 70, 229, 0.25);
      transition: all var(--transition-fast);
      position: relative;
      border: none;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(79, 70, 229, 0.3);
      }
    }

    // Special styling for formatted content in user messages
    .message-text {
      // Inline code in user messages
      code {
        background: rgba(0, 0, 0, 0.15);
        color: #059669;
      }

      // Code blocks in user messages
      pre {
        background: rgba(0, 0, 0, 0.05);
        border-color: rgba(0, 0, 0, 0.1);

        code {
          color: #047857;
        }
      }

      // Links in user messages
      a {
        color: #059669;
        &:hover {
          color: #047857;
        }
      }

      // Blockquotes in user messages
      blockquote {
        border-left-color: #059669;
        background: rgba(0, 0, 0, 0.05);
      }

      // Tables in user messages
      th, td {
        border-color: rgba(0, 0, 0, 0.1);
      }

      th {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }

  &.bot-message {
    justify-content: flex-start;
    flex-direction: column;
    align-items: flex-start;

    .message-content {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      color: var(--color-gray-800);
      max-width: 75%;
      border-radius: 20px 20px 20px 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all var(--transition-fast);
      position: relative;
      border: 1px solid var(--color-gray-200);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        border-color: var(--color-gray-300);

/* News container spacing to sit clearly under bubble */
.message-news {
  margin-top: var(--space-3);
}

      }
    }

/* Ensure news block sits clearly UNDER the bot bubble, aligned with it */
.message.bot-message {
  flex-direction: column;
  align-items: flex-start;
}

.message-news {
  max-width: 80%;
  margin-left: 0;
  align-self: flex-start;
}

  }
}

.message-content {
  padding: var(--space-4) var(--space-5);
  word-wrap: break-word;
  position: relative;
}

.message-text {
  font-size: var(--font-size-base);
  line-height: 1.6;
  margin: 0;
  font-weight: var(--font-weight-normal);
  word-break: break-word;

  // Paragraph styling
  p {
    margin: 0 0 var(--space-3) 0;
    &:last-child {
      margin-bottom: 0;
    }
  }

  // Headings
  h1, h2, h3, h4, h5, h6 {
    margin: var(--space-4) 0 var(--space-2) 0;
    font-weight: var(--font-weight-semibold);
    line-height: 1.3;

    &:first-child {
      margin-top: 0;
    }
  }

  h1 { font-size: 1.5em; }
  h2 { font-size: 1.3em; }
  h3 { font-size: 1.2em; }
  h4 { font-size: 1.1em; }
  h5, h6 { font-size: 1em; }

  // Bold and italic text
  strong, b {
    font-weight: var(--font-weight-semibold);
  }

  em, i {
    font-style: italic;
  }

  // Strike-through and underline
  del, strike {
    text-decoration: line-through;
  }

  u {
    text-decoration: underline;
  }

  // Lists
  ul, ol {
    margin: var(--space-3) 0;
    padding-left: var(--space-6);

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  li {
    margin: var(--space-1) 0;
    line-height: 1.5;
  }

  // Blockquotes
  blockquote {
    margin: var(--space-4) 0;
    padding: var(--space-3) var(--space-4);
    border-left: 4px solid #e5e7eb;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
    font-style: italic;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  // Inline code
  code {
    background: rgba(0, 0, 0, 0.08);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #e11d48;
  }

  // Code blocks
  pre {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin: var(--space-4) 0;
    overflow-x: auto;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }

    code {
      background: none;
      padding: 0;
      color: #334155;
      font-size: 0.875em;
    }
  }

  // Links
  a {
    color: #2563eb;
    text-decoration: underline;
    transition: var(--transition-fast);

    &:hover {
      color: #1d4ed8;
      text-decoration: none;
    }
  }

  // Tables
  table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--space-4) 0;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  th, td {
    padding: var(--space-2) var(--space-3);
    border: 1px solid #e2e8f0;
    text-align: left;
  }

  th {
    background: #f8fafc;
    font-weight: var(--font-weight-semibold);
  }

  // Images
  img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-sm);
    margin: var(--space-2) 0;
  }
}

.message-time {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.7);
  margin-top: var(--space-2);
  text-align: right;
  opacity: 0.8;
  transition: var(--transition-fast);
  font-weight: var(--font-weight-normal);
}

.bot-message .message-time {
  text-align: left;
  color: var(--color-gray-600);
}

.user-message .message-time {
  text-align: right;
  color: var(--color-gray-600);
}
.message:hover .message-time {
  opacity: 1;
}

.input-container {
  padding: var(--space-6) var(--space-8) var(--space-8) var(--space-8);
  background: linear-gradient(to top, #ffffff 0%, #fefefe 100%);
  position: absolute;  /* Changed from fixed to absolute */
  bottom: 0;
  left: 0;             /* Changed from 50% to 0 */
  right: 0;            /* Added right: 0 */
  transform: none;     /* Removed transform */
  width: 100%;
  z-index: 50;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  transition: var(--transition-fast);
  backdrop-filter: blur(10px);

  &.centered {
    position: absolute;  /* Changed from fixed to absolute */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: calc(100% - var(--space-8)); /* Adjust width to account for container padding */
    max-width: 600px;
    background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
    z-index: 50;
    padding: var(--space-8);
    bottom: auto;
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12), 0 8px 24px rgba(0, 0, 0, 0.08);
  }

  &.bottom {
    position: absolute;  /* Changed from fixed to absolute */
    bottom: 0;
    left: 0;             /* Changed from 50% to 0 */
    right: 0;            /* Added right: 0 */
    transform: none;     /* Removed transform */
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    background: linear-gradient(to top, #ffffff 0%, #fefefe 100%);
    width: 100%;
    z-index: 50;
    backdrop-filter: blur(10px);
  }
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--space-4);
  border: 2px solid var(--color-gray-200);
  border-radius: 28px;
  padding: var(--space-4) var(--space-5);
  background: var(--color-white);
  transition: all var(--transition-normal);
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &:focus-within {
    border-color: var(--color-primary);
    background: var(--color-white);
    box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.08), 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  &:hover {
    border-color: var(--color-gray-300);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  }
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  resize: none;
  font-size: var(--font-size-base);
  line-height: 1.6;
  padding: var(--space-2) 0;
  min-height: 28px;
  max-height: 120px;
  font-family: var(--font-family-primary);
  overflow-y: auto;
  transition: var(--transition-fast);
  color: var(--color-gray-900);
  font-weight: var(--font-weight-normal);

  &::placeholder {
    color: var(--color-gray-500);
    opacity: 1;
    font-weight: var(--font-weight-normal);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Clean scrollbar for textarea */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: var(--radius-sm);

    &:hover {
      background: #94a3b8;
    }
  }
}

.send-button {
  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  cursor: pointer;
  flex-shrink: 0;
  transition: all var(--transition-normal);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity var(--transition-fast);
  }

  &:hover:not(:disabled) {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);

    &::before {
      opacity: 1;
    }
  }

  &:active:not(:disabled) {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  }

  &:disabled {
    background: var(--color-gray-400);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &::before {
      display: none;
    }
  }

  svg {
    width: 20px;
    height: 20px;
    transition: var(--transition-fast);
    transform: translateX(1px); /* Slight offset to center the arrow visually */
  }

  &:hover:not(:disabled) svg {
    transform: translateX(1px) scale(1.1);
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) 0;

  span {
    width: 8px;
    height: 8px;
    background: #94a3b8;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

/* Modern auth loading styles */
.auth-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: var(--color-gray-600);
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 50%, #f0f4f8 100%);

  .loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--color-gray-200);
    border-top: 4px solid var(--color-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--space-4);
  }

  p {
    font-size: var(--font-size-lg);
    color: var(--color-gray-600);
    margin: 0;
    font-weight: var(--font-weight-medium);
  }
}

/* Modern enhanced animations */
@keyframes typing {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(0.7) translateY(0);
  }
  30% {
    opacity: 1;
    transform: scale(1.3) translateY(-6px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced mobile responsive design */
@media (max-width: 768px) {
  .chat-container {
    max-width: 100%;
    height: 100vh;
    border-radius: 0;
    border: none;
  }

  .messages-container {
    padding: var(--space-5) var(--space-5) 140px var(--space-5);
  }

  .input-container {
    padding: var(--space-5) var(--space-5) var(--space-6) var(--space-5);
    max-width: 100%;

    &.centered {
      max-width: 100%;
      padding: var(--space-5);
      margin: 0 var(--space-4);
      width: calc(100% - var(--space-8));
    }
  }

  .message {
    margin-bottom: var(--space-5);

    &.user-message .message-content,
    &.bot-message .message-content {
      max-width: 88%;
    }
  }

  .input-wrapper {
    padding: var(--space-1) var(--space-2) var(--space-1) var(--space-4);
    gap: var(--space-2);
  }

  .send-button {
    width: 42px;
    height: 42px;

    svg {
      width: 18px;
      height: 18px;
    }
  }
}

/* Enhanced smooth transitions for better UX */
* {
  transition: color var(--transition-fast),
              background-color var(--transition-fast),
              border-color var(--transition-fast),
              transform var(--transition-fast),
              box-shadow var(--transition-fast);
}

/* Enhanced focus indicators for accessibility */
.send-button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 3px;
}

.message-input:focus {
  box-shadow: none;
}

/* Additional modern enhancements */
.message-content {
  &::selection {
    background: rgba(16, 185, 129, 0.2);
  }
}

.user-message .message-content {
  &::selection {
    background: rgba(255, 255, 255, 0.3);
  }
}

/* Clean animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}