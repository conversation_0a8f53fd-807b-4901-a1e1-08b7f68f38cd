{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./news/news.component\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'News Agent';\n    // Sample news data to demonstrate search and filters\n    this.sampleNews = [{\n      country: 'ukraine',\n      source: 'Kyiv Independent',\n      title: 'Ukraine receives new military aid package',\n      link: 'https://example.com/news1',\n      published: '2024-01-15T10:30:00Z',\n      description: 'NATO allies announce comprehensive military aid package including advanced defense systems.'\n    }, {\n      country: 'poland',\n      source: 'Warsaw Times',\n      title: 'Poland strengthens eastern border security',\n      link: 'https://example.com/news2',\n      published: '2024-01-14T15:45:00Z',\n      description: 'Polish government implements enhanced security protocols along its eastern border.'\n    }, {\n      country: 'russia',\n      source: 'Moscow Herald',\n      title: 'Russian economic indicators show mixed results',\n      link: 'https://example.com/news3',\n      published: '2024-01-13T09:15:00Z',\n      description: 'Latest economic data from Russia reveals varying performance across different sectors.'\n    }, {\n      country: 'belarus',\n      source: 'Minsk Daily',\n      title: 'Belarus announces new agricultural initiatives',\n      link: 'https://example.com/news4',\n      published: '2024-01-12T14:20:00Z',\n      description: 'Government unveils comprehensive agricultural development program.'\n    }, {\n      country: 'ukraine',\n      source: 'Ukrainian Voice',\n      title: 'Reconstruction efforts accelerate in liberated territories',\n      link: 'https://example.com/news5',\n      published: '2024-01-11T11:00:00Z',\n      description: 'International cooperation drives rapid reconstruction of infrastructure.'\n    }];\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 5,\n    vars: 1,\n    consts: [[1, \"app-container\"], [1, \"main-content\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"main\", 1)(2, \"h1\");\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(4, \"app-news\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.title);\n      }\n    },\n    dependencies: [i1.NewsComponent],\n    styles: [\"\\n\\n.app-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  background: transparent;\\n  overflow: hidden;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  background-color: transparent;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.main-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 50%, #8b5cf6 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  margin: 0;\\n  padding: var(--space-8) 0 var(--space-6) 0;\\n  font-size: var(--font-size-3xl);\\n  font-weight: var(--font-weight-bold);\\n  flex-shrink: 0;\\n  letter-spacing: -0.025em;\\n  position: relative;\\n}\\n.main-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: var(--space-4);\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(90deg, #4f46e5 0%, #8b5cf6 100%);\\n  border-radius: 2px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .app-container[_ngcontent-%COMP%] {\\n    height: 100vh;\\n  }\\n}\\n\\n\\n.sr-only[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border: 0;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.text-left[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n\\n.text-right[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.mb-0[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.mb-2[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-2);\\n}\\n\\n.mb-4[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n}\\n\\n.mb-6[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-6);\\n}\\n\\n.mt-0[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n}\\n\\n.mt-2[_ngcontent-%COMP%] {\\n  margin-top: var(--space-2);\\n}\\n\\n.mt-4[_ngcontent-%COMP%] {\\n  margin-top: var(--space-4);\\n}\\n\\n.mt-6[_ngcontent-%COMP%] {\\n  margin-top: var(--space-6);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "sampleNews", "country", "source", "link", "published", "description", "_", "_2", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { NewsItem } from './news/news.component';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = 'News Agent';\n\n  // Sample news data to demonstrate search and filters\n  sampleNews: NewsItem[] = [\n    {\n      country: 'ukraine',\n      source: 'Kyiv Independent',\n      title: 'Ukraine receives new military aid package',\n      link: 'https://example.com/news1',\n      published: '2024-01-15T10:30:00Z',\n      description: 'NATO allies announce comprehensive military aid package including advanced defense systems.'\n    },\n    {\n      country: 'poland',\n      source: 'Warsaw Times',\n      title: 'Poland strengthens eastern border security',\n      link: 'https://example.com/news2',\n      published: '2024-01-14T15:45:00Z',\n      description: 'Polish government implements enhanced security protocols along its eastern border.'\n    },\n    {\n      country: 'russia',\n      source: 'Moscow Herald',\n      title: 'Russian economic indicators show mixed results',\n      link: 'https://example.com/news3',\n      published: '2024-01-13T09:15:00Z',\n      description: 'Latest economic data from Russia reveals varying performance across different sectors.'\n    },\n    {\n      country: 'belarus',\n      source: 'Minsk Daily',\n      title: 'Belarus announces new agricultural initiatives',\n      link: 'https://example.com/news4',\n      published: '2024-01-12T14:20:00Z',\n      description: 'Government unveils comprehensive agricultural development program.'\n    },\n    {\n      country: 'ukraine',\n      source: 'Ukrainian Voice',\n      title: 'Reconstruction efforts accelerate in liberated territories',\n      link: 'https://example.com/news5',\n      published: '2024-01-11T11:00:00Z',\n      description: 'International cooperation drives rapid reconstruction of infrastructure.'\n    }\n  ];\n}\n", "<div class=\"app-container\">\r\n  <main class=\"main-content\">\r\n    <h1>{{ title }}</h1>\r\n    <app-news></app-news>\r\n    <!-- <app-chat></app-chat> -->\r\n  </main>\r\n</div>\r\n"], "mappings": ";;AAQA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,YAAY;IAEpB;IACA,KAAAC,UAAU,GAAe,CACvB;MACEC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,kBAAkB;MAC1BH,KAAK,EAAE,2CAA2C;MAClDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,cAAc;MACtBH,KAAK,EAAE,4CAA4C;MACnDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,eAAe;MACvBH,KAAK,EAAE,gDAAgD;MACvDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,aAAa;MACrBH,KAAK,EAAE,gDAAgD;MACvDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,iBAAiB;MACzBH,KAAK,EAAE,4DAA4D;MACnEI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,CACF;;EACF,QAAAC,CAAA,G;qBA9CYT,YAAY;EAAA;EAAA,QAAAU,EAAA,G;UAAZV,YAAY;IAAAW,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRzBE,EAAA,CAAAC,cAAA,aAA2B;QAEnBD,EAAA,CAAAE,MAAA,GAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpBH,EAAA,CAAAI,SAAA,eAAqB;QAEvBJ,EAAA,CAAAG,YAAA,EAAO;;;QAHDH,EAAA,CAAAK,SAAA,GAAW;QAAXL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAAhB,KAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}