{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = 'http://localhost:8000/api';\n    this.tokenKey = 'token';\n  }\n  // Get token from localStorage\n  getToken() {\n    return localStorage.getItem(this.tokenKey);\n  }\n  // Set token in localStorage\n  setToken(token) {\n    localStorage.setItem(this.tokenKey, token);\n  }\n  // Check if token exists and is valid\n  checkAuthStatus() {\n    const token = this.getToken();\n    if (!token) {\n      return of(false);\n    }\n    const headers = new HttpHeaders({\n      'Authorization': `Token ${token}`\n    });\n    return this.http.get(`${this.baseUrl}/check-auth/`, {\n      headers\n    }).pipe(map(() => true), catchError(() => of(false)));\n  }\n  // Create anonymous user and get token\n  createAnonymousUser() {\n    return this.http.post(`${this.baseUrl}/anonymous-user/`, {}).pipe(map(response => {\n      this.setToken(response.token);\n      return response.token;\n    }));\n  }\n  // Main function to ensure user is authenticated\n  ensureAuthenticated() {\n    return new Observable(observer => {\n      this.checkAuthStatus().subscribe(isValid => {\n        if (isValid) {\n          observer.next(this.getToken());\n          observer.complete();\n        } else {\n          this.createAnonymousUser().subscribe(token => {\n            observer.next(token);\n            observer.complete();\n          }, error => {\n            observer.error(error);\n          });\n        }\n      });\n    });\n  }\n  // Get headers with authentication\n  getHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Authorization': `Token ${token}`\n    });\n  }\n  // Load chat history with pagination\n  loadChatHistory(page = 1, sessionId) {\n    let url = `${this.baseUrl}/chat-history/?page=${page}`;\n    if (sessionId) {\n      url += `&session_id=${sessionId}`;\n    }\n    return this.http.get(url, {\n      headers: this.getHeaders()\n    });\n  }\n  // Send message to chatbot\n  sendMessageToChatbot(message, sessionId) {\n    const payload = {\n      message\n    };\n    if (sessionId) {\n      payload.session_id = sessionId;\n    }\n    return this.http.post(`${this.baseUrl}/chatbot/`, payload, {\n      headers: this.getHeaders()\n    });\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "Observable", "of", "catchError", "map", "AuthService", "constructor", "http", "baseUrl", "<PERSON><PERSON><PERSON>", "getToken", "localStorage", "getItem", "setToken", "token", "setItem", "checkAuthStatus", "headers", "get", "pipe", "createAnonymousUser", "post", "response", "ensureAuthenticated", "observer", "subscribe", "<PERSON><PERSON><PERSON><PERSON>", "next", "complete", "error", "getHeaders", "loadChatHistory", "page", "sessionId", "url", "sendMessageToChatbot", "message", "payload", "session_id", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private baseUrl = 'http://localhost:8000/api';\n  private tokenKey = 'token';\n\n  constructor(private http: HttpClient) { }\n\n  // Get token from localStorage\n  getToken(): string | null {\n    return localStorage.getItem(this.tokenKey);\n  }\n\n  // Set token in localStorage\n  private setToken(token: string): void {\n    localStorage.setItem(this.tokenKey, token);\n  }\n\n  // Check if token exists and is valid\n  checkAuthStatus(): Observable<boolean> {\n    const token = this.getToken();\n    \n    if (!token) {\n      return of(false);\n    }\n\n    const headers = new HttpHeaders({\n      'Authorization': `Token ${token}`\n    });\n\n    return this.http.get(`${this.baseUrl}/check-auth/`, { headers }).pipe(\n      map(() => true),\n      catchError(() => of(false))\n    );\n  }\n\n  // Create anonymous user and get token\n  createAnonymousUser(): Observable<string> {\n    return this.http.post<{ token: string }>(`${this.baseUrl}/anonymous-user/`, {}).pipe(\n      map(response => {\n        this.setToken(response.token);\n        return response.token;\n      })\n    );\n  }\n\n  // Main function to ensure user is authenticated\n  ensureAuthenticated(): Observable<string> {\n    return new Observable(observer => {\n      this.checkAuthStatus().subscribe(isValid => {\n        if (isValid) {\n          observer.next(this.getToken()!);\n          observer.complete();\n        } else {\n          this.createAnonymousUser().subscribe(\n            token => {\n              observer.next(token);\n              observer.complete();\n            },\n            error => {\n              observer.error(error);\n            }\n          );\n        }\n      });\n    });\n  }\n\n  // Get headers with authentication\n  private getHeaders(): HttpHeaders {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Authorization': `Token ${token}`\n    });\n  }\n\n  // Load chat history with pagination\n  loadChatHistory(page: number = 1, sessionId?: number): Observable<any> {\n    let url = `${this.baseUrl}/chat-history/?page=${page}`;\n    if (sessionId) {\n      url += `&session_id=${sessionId}`;\n    }\n    return this.http.get(url, { \n      headers: this.getHeaders() \n    });\n  }\n\n  // Send message to chatbot\n  sendMessageToChatbot(message: string, sessionId?: number): Observable<any> {\n    const payload: any = { message };\n    if (sessionId) {\n      payload.session_id = sessionId;\n    }\n    return this.http.post(`${this.baseUrl}/chatbot/`, \n      payload, \n      { headers: this.getHeaders() }\n    );\n  }\n}"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AACrC,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;AAKhD,OAAM,MAAOC,WAAW;EAItBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHhB,KAAAC,OAAO,GAAG,2BAA2B;IACrC,KAAAC,QAAQ,GAAG,OAAO;EAEc;EAExC;EACAC,QAAQA,CAAA;IACN,OAAOC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACH,QAAQ,CAAC;EAC5C;EAEA;EACQI,QAAQA,CAACC,KAAa;IAC5BH,YAAY,CAACI,OAAO,CAAC,IAAI,CAACN,QAAQ,EAAEK,KAAK,CAAC;EAC5C;EAEA;EACAE,eAAeA,CAAA;IACb,MAAMF,KAAK,GAAG,IAAI,CAACJ,QAAQ,EAAE;IAE7B,IAAI,CAACI,KAAK,EAAE;MACV,OAAOZ,EAAE,CAAC,KAAK,CAAC;;IAGlB,MAAMe,OAAO,GAAG,IAAIjB,WAAW,CAAC;MAC9B,eAAe,EAAE,SAASc,KAAK;KAChC,CAAC;IAEF,OAAO,IAAI,CAACP,IAAI,CAACW,GAAG,CAAC,GAAG,IAAI,CAACV,OAAO,cAAc,EAAE;MAAES;IAAO,CAAE,CAAC,CAACE,IAAI,CACnEf,GAAG,CAAC,MAAM,IAAI,CAAC,EACfD,UAAU,CAAC,MAAMD,EAAE,CAAC,KAAK,CAAC,CAAC,CAC5B;EACH;EAEA;EACAkB,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACb,IAAI,CAACc,IAAI,CAAoB,GAAG,IAAI,CAACb,OAAO,kBAAkB,EAAE,EAAE,CAAC,CAACW,IAAI,CAClFf,GAAG,CAACkB,QAAQ,IAAG;MACb,IAAI,CAACT,QAAQ,CAACS,QAAQ,CAACR,KAAK,CAAC;MAC7B,OAAOQ,QAAQ,CAACR,KAAK;IACvB,CAAC,CAAC,CACH;EACH;EAEA;EACAS,mBAAmBA,CAAA;IACjB,OAAO,IAAItB,UAAU,CAACuB,QAAQ,IAAG;MAC/B,IAAI,CAACR,eAAe,EAAE,CAACS,SAAS,CAACC,OAAO,IAAG;QACzC,IAAIA,OAAO,EAAE;UACXF,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACjB,QAAQ,EAAG,CAAC;UAC/Bc,QAAQ,CAACI,QAAQ,EAAE;SACpB,MAAM;UACL,IAAI,CAACR,mBAAmB,EAAE,CAACK,SAAS,CAClCX,KAAK,IAAG;YACNU,QAAQ,CAACG,IAAI,CAACb,KAAK,CAAC;YACpBU,QAAQ,CAACI,QAAQ,EAAE;UACrB,CAAC,EACDC,KAAK,IAAG;YACNL,QAAQ,CAACK,KAAK,CAACA,KAAK,CAAC;UACvB,CAAC,CACF;;MAEL,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACQC,UAAUA,CAAA;IAChB,MAAMhB,KAAK,GAAG,IAAI,CAACJ,QAAQ,EAAE;IAC7B,OAAO,IAAIV,WAAW,CAAC;MACrB,eAAe,EAAE,SAASc,KAAK;KAChC,CAAC;EACJ;EAEA;EACAiB,eAAeA,CAACC,IAAA,GAAe,CAAC,EAAEC,SAAkB;IAClD,IAAIC,GAAG,GAAG,GAAG,IAAI,CAAC1B,OAAO,uBAAuBwB,IAAI,EAAE;IACtD,IAAIC,SAAS,EAAE;MACbC,GAAG,IAAI,eAAeD,SAAS,EAAE;;IAEnC,OAAO,IAAI,CAAC1B,IAAI,CAACW,GAAG,CAACgB,GAAG,EAAE;MACxBjB,OAAO,EAAE,IAAI,CAACa,UAAU;KACzB,CAAC;EACJ;EAEA;EACAK,oBAAoBA,CAACC,OAAe,EAAEH,SAAkB;IACtD,MAAMI,OAAO,GAAQ;MAAED;IAAO,CAAE;IAChC,IAAIH,SAAS,EAAE;MACbI,OAAO,CAACC,UAAU,GAAGL,SAAS;;IAEhC,OAAO,IAAI,CAAC1B,IAAI,CAACc,IAAI,CAAC,GAAG,IAAI,CAACb,OAAO,WAAW,EAC9C6B,OAAO,EACP;MAAEpB,OAAO,EAAE,IAAI,CAACa,UAAU;IAAE,CAAE,CAC/B;EACH;EAAC,QAAAS,CAAA,G;qBA/FUlC,WAAW,EAAAmC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXvC,WAAW;IAAAwC,OAAA,EAAXxC,WAAW,CAAAyC,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}