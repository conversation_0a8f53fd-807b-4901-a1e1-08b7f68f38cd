{"ast": null, "code": "import { marked } from 'marked';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../anonymous-user/anonymous-user.component\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatComponent_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatComponent_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"div\", 20);\n    i0.ɵɵelementStart(3, \"div\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r8 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c1, message_r8.isUser, !message_r8.isUser));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r6.formatMessageText(message_r8.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 3, message_r8.timestamp, \"short\"));\n  }\n}\nfunction ChatComponent_div_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 19)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11, 12);\n    i0.ɵɵlistener(\"scroll\", function ChatComponent_div_1_div_1_Template_div_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onScroll($event));\n    });\n    i0.ɵɵtemplate(2, ChatComponent_div_1_div_1_div_2_Template, 2, 0, \"div\", 13);\n    i0.ɵɵtemplate(3, ChatComponent_div_1_div_1_div_3_Template, 6, 9, \"div\", 14);\n    i0.ɵɵtemplate(4, ChatComponent_div_1_div_1_div_4_Template, 6, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ChatComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, ChatComponent_div_1_div_1_Template, 5, 3, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\", 5)(4, \"textarea\", 6, 7);\n    i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_div_1_Template_textarea_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.currentMessage = $event);\n    })(\"keydown\", function ChatComponent_div_1_Template_textarea_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onKeyPress($event));\n    })(\"input\", function ChatComponent_div_1_Template_textarea_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.adjustTextareaHeight($event));\n    });\n    i0.ɵɵtext(6, \"      \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_1_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.sendMessage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 9);\n    i0.ɵɵelement(9, \"path\", 10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"centered\", ctx_r0.messages.length === 0)(\"bottom\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.currentMessage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.currentMessage.trim() || ctx_r0.isLoading);\n  }\n}\nfunction ChatComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Initializing chat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ChatComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.messages = [];\n    this.currentMessage = '';\n    this.isLoading = false;\n    this.isAuthenticated = false;\n    this.currentSessionId = null;\n    // Pagination\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.isLoadingHistory = false;\n    // Scroll management\n    this.shouldScrollToBottom = true;\n    this.lastScrollHeight = 0;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n  }\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: token => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory(1, false);\n      },\n      error: error => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n  // Listen for scroll events to load more history\n  onScroll(event) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    const clientHeight = element.clientHeight;\n    // Mark that user has scrolled manually (not programmatic)\n    if (this.initialLoadComplete) {\n      this.userHasScrolled = true;\n    }\n    // Clear existing timeout\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    // Debounce scroll events and check conditions\n    this.scrollTimeout = setTimeout(() => {\n      // Only load more if:\n      // 1. Initial load is complete\n      // 2. User has scrolled manually at least once\n      // 3. User is near the top (scrollTop < 100)\n      // 4. There are more pages to load\n      // 5. Not currently loading\n      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n      const isNearTop = scrollTop < 100;\n      const isNotAtBottom = scrollTop < scrollHeight - clientHeight - 50;\n      if (isNearTop && isNotAtBottom && this.hasNextPage && !this.isLoadingHistory && this.initialLoadComplete && this.userHasScrolled) {\n        this.lastScrollHeight = scrollHeight;\n        this.loadMoreHistory();\n      }\n    }, 100); // 100ms debounce\n  }\n\n  loadChatHistory(page = 1, append = false) {\n    if (!this.isAuthenticated) return;\n    this.isLoadingHistory = true;\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        if (append) {\n          // For pagination - reverse the new messages (since API returns newest first)\n          // and prepend older messages to beginning\n          const reversedNewMessages = [...newMessages].reverse();\n          this.messages = [...reversedNewMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - reverse messages to get chronological order (oldest first)\n          this.messages = [...newMessages].reverse();\n          this.shouldScrollToBottom = true;\n          // Set initial load complete after scroll positioning is done\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        this.isLoadingHistory = false;\n      },\n      error: error => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        // If this was the initial load, still mark it as complete after delay\n        if (!append) {\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n      }\n    });\n  }\n  // Convert Django ChatMessage to frontend Message format\n  convertChatMessageToMessage(chatMessage) {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined,\n      news_articles: chatMessage.news_articles || undefined\n    };\n  }\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n  // Maintain scroll position when loading older messages\n  maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    // Create temporary user message and add it instantly\n    const tempUserMessage = {\n      text: messageToSend,\n      isUser: true,\n      timestamp: new Date()\n    };\n    // Add user message instantly to the chat\n    this.messages.push(tempUserMessage);\n    this.shouldScrollToBottom = true;\n    // Set loading state\n    this.isLoading = true;\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        // Convert backend messages\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n        // Replace the temporary user message with the one from backend\n        const lastMessageIndex = this.messages.length - 1;\n        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n          this.messages[lastMessageIndex] = userMessage;\n        }\n        // Add bot message\n        this.messages.push(botMessage);\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        const errorMessage = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n  clearHistory() {\n    this.messages = [];\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n  refreshHistory() {\n    this.currentPage = 1;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.loadChatHistory();\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  adjustTextareaHeight(event) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n  // Format message text using marked library for markdown\n  formatMessageText(text) {\n    if (!text) return '';\n    try {\n      // Configure marked to be more restrictive for security\n      marked.setOptions({\n        breaks: true,\n        gfm: true // Enable GitHub Flavored Markdown\n      });\n      // Convert markdown to HTML using marked (synchronous)\n      const htmlContent = marked.parse(text);\n      // Basic sanitization - remove script tags and dangerous attributes\n      let sanitizedHtml = htmlContent.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '').replace(/javascript:/gi, '').replace(/on\\w+\\s*=/gi, '');\n      return sanitizedHtml;\n    } catch (error) {\n      console.error('Error formatting message text:', error);\n      // Fallback to plain text with basic line break conversion\n      return text.replace(/\\n/g, '<br>');\n    }\n  }\n  ngOnDestroy() {\n    // Clean up any pending timeouts\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function ChatComponent_Factory(t) {\n    return new (t || ChatComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChatComponent,\n    selectors: [[\"app-chat\"]],\n    viewQuery: function ChatComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n      }\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"chat-container\", 4, \"ngIf\"], [\"class\", \"auth-loading\", 4, \"ngIf\"], [1, \"chat-container\"], [\"class\", \"messages-container\", 3, \"scroll\", 4, \"ngIf\"], [1, \"input-container\"], [1, \"input-wrapper\"], [\"placeholder\", \"Ask anything\", \"rows\", \"1\", 1, \"message-input\", 3, \"ngModel\", \"ngModelChange\", \"keydown\", \"input\"], [\"messageTextarea\", \"\"], [1, \"send-button\", 3, \"disabled\", \"click\"], [\"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M2 21L23 12L2 3V10L17 12L2 14V21Z\", \"fill\", \"currentColor\"], [1, \"messages-container\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"pagination-loading\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"message bot-message\", 4, \"ngIf\"], [1, \"pagination-loading\"], [1, \"loading-spinner\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-text\", 3, \"innerHTML\"], [1, \"message-time\"], [1, \"message\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"auth-loading\"]],\n    template: function ChatComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-anonymous-user\");\n        i0.ɵɵtemplate(1, ChatComponent_div_1_Template, 10, 7, \"div\", 0);\n        i0.ɵɵtemplate(2, ChatComponent_div_2_Template, 4, 0, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.AnonymousUserComponent, i2.DatePipe],\n    styles: [\"\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%; \\n\\n  max-width: 900px;\\n  margin: 0 auto;\\n  background: linear-gradient(to bottom, #ffffff 0%, #fefefe 100%);\\n  position: relative; \\n\\n  overflow: hidden;\\n  font-family: var(--font-family-primary);\\n  border-radius: var(--radius-2xl);\\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);\\n  border: 1px solid rgba(255, 255, 255, 0.8);\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: var(--space-6) var(--space-8) 120px var(--space-8);\\n  background: #f9fafb;\\n  max-height: calc(100vh - 160px);\\n  scroll-behavior: smooth;\\n  position: relative;\\n  z-index: 10;\\n  \\n\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f5f9;\\n  border-radius: var(--radius-md);\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e1;\\n  border-radius: var(--radius-md);\\n  -webkit-transition: var(--transition-fast);\\n  transition: var(--transition-fast);\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #94a3b8;\\n}\\n\\n\\n\\n.pagination-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: var(--space-4) 0;\\n  margin-bottom: var(--space-4);\\n}\\n.pagination-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 2px solid #e2e8f0;\\n  border-top: 2px solid #bdf2bd;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n  display: flex;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.3s ease-out;\\n  position: relative;\\n}\\n.message.user-message[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #d0ceff 0%, #e6e6ff 100%);\\n  color: #ffffff;\\n  max-width: 75%;\\n  border-radius: 20px 20px 6px 20px;\\n  box-shadow: 0 3px 12px rgba(79, 70, 229, 0.25);\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  border: none;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.3);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.15);\\n  color: #059669;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.05);\\n  border-color: rgba(0, 0, 0, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  color: #047857;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #059669;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #047857;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: #059669;\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(0, 0, 0, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.message.bot-message[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  \\n\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\\n  color: var(--color-gray-800);\\n  max-width: 75%;\\n  border-radius: 20px 20px 20px 6px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  border: 1px solid var(--color-gray-200);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\\n  border-color: var(--color-gray-300);\\n  \\n\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover   .message-news[_ngcontent-%COMP%] {\\n  margin-top: var(--space-3);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message.bot-message[_ngcontent-%COMP%] {\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-news[_ngcontent-%COMP%] {\\n  max-width: 80%;\\n  margin-left: 0;\\n  align-self: flex-start;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: var(--space-4) var(--space-5);\\n  word-wrap: break-word;\\n  position: relative;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-base);\\n  line-height: 1.6;\\n  margin: 0;\\n  font-weight: var(--font-weight-normal);\\n  word-break: break-word;\\n}\\n.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--space-3) 0;\\n}\\n.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: var(--space-4) 0 var(--space-2) 0;\\n  font-weight: var(--font-weight-semibold);\\n  line-height: 1.3;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.1em;\\n}\\n.message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-size: 1em;\\n}\\n.message-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   b[_ngcontent-%COMP%] {\\n  font-weight: var(--font-weight-semibold);\\n}\\n.message-text[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-style: italic;\\n}\\n.message-text[_ngcontent-%COMP%]   del[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   strike[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n}\\n.message-text[_ngcontent-%COMP%]   u[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: var(--space-3) 0;\\n  padding-left: var(--space-6);\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: var(--space-1) 0;\\n  line-height: 1.5;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  margin: var(--space-4) 0;\\n  padding: var(--space-3) var(--space-4);\\n  border-left: 4px solid #e5e7eb;\\n  background: rgba(0, 0, 0, 0.02);\\n  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;\\n  font-style: italic;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.08);\\n  padding: 2px 6px;\\n  border-radius: var(--radius-sm);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 0.9em;\\n  color: #e11d48;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: var(--radius-md);\\n  padding: var(--space-4);\\n  margin: var(--space-4) 0;\\n  overflow-x: auto;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: none;\\n  padding: 0;\\n  color: #334155;\\n  font-size: 0.875em;\\n}\\n.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #2563eb;\\n  text-decoration: underline;\\n  transition: var(--transition-fast);\\n}\\n.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #1d4ed8;\\n  text-decoration: none;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  margin: var(--space-4) 0;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: var(--space-2) var(--space-3);\\n  border: 1px solid #e2e8f0;\\n  text-align: left;\\n}\\n.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  font-weight: var(--font-weight-semibold);\\n}\\n.message-text[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  border-radius: var(--radius-sm);\\n  margin: var(--space-2) 0;\\n}\\n\\n.message-time[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-xs);\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-top: var(--space-2);\\n  text-align: right;\\n  opacity: 0.8;\\n  transition: var(--transition-fast);\\n  font-weight: var(--font-weight-normal);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: left;\\n  color: var(--color-gray-600);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: right;\\n  color: var(--color-gray-600);\\n}\\n\\n.message[_ngcontent-%COMP%]:hover   .message-time[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  padding: var(--space-6) var(--space-8) var(--space-8) var(--space-8);\\n  background: linear-gradient(to top, #ffffff 0%, #fefefe 100%);\\n  position: absolute; \\n\\n  bottom: 0;\\n  left: 0; \\n\\n  right: 0; \\n\\n  transform: none; \\n\\n  width: 100%;\\n  z-index: 50;\\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\\n  transition: var(--transition-fast);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.input-container.centered[_ngcontent-%COMP%] {\\n  position: absolute; \\n\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: calc(100% - var(--space-8)); \\n\\n  max-width: 600px;\\n  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);\\n  z-index: 50;\\n  padding: var(--space-8);\\n  bottom: auto;\\n  border-radius: var(--radius-2xl);\\n  border: 1px solid rgba(0, 0, 0, 0.06);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12), 0 8px 24px rgba(0, 0, 0, 0.08);\\n}\\n.input-container.bottom[_ngcontent-%COMP%] {\\n  position: absolute; \\n\\n  bottom: 0;\\n  left: 0; \\n\\n  right: 0; \\n\\n  transform: none; \\n\\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\\n  background: linear-gradient(to top, #ffffff 0%, #fefefe 100%);\\n  width: 100%;\\n  z-index: 50;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.input-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: var(--space-4);\\n  border: 2px solid var(--color-gray-200);\\n  border-radius: 28px;\\n  padding: var(--space-4) var(--space-5);\\n  background: var(--color-white);\\n  transition: all var(--transition-normal);\\n  position: relative;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n}\\n.input-wrapper[_ngcontent-%COMP%]:focus-within {\\n  border-color: var(--color-primary);\\n  background: var(--color-white);\\n  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.08), 0 4px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n.input-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-gray-300);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  background: transparent;\\n  resize: none;\\n  font-size: var(--font-size-base);\\n  line-height: 1.6;\\n  padding: var(--space-2) 0;\\n  min-height: 28px;\\n  max-height: 120px;\\n  font-family: var(--font-family-primary);\\n  overflow-y: auto;\\n  transition: var(--transition-fast);\\n  color: var(--color-gray-900);\\n  font-weight: var(--font-weight-normal);\\n  \\n\\n}\\n.message-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--color-gray-500);\\n  opacity: 1;\\n  font-weight: var(--font-weight-normal);\\n}\\n.message-input[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e1;\\n  border-radius: var(--radius-sm);\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #94a3b8;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--color-white);\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: all var(--transition-normal);\\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.send-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);\\n  border-radius: 50%;\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)::before {\\n  opacity: 1;\\n}\\n.send-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(-1px) scale(1.02);\\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled {\\n  background: var(--color-gray-400);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled::before {\\n  display: none;\\n}\\n.send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  transition: var(--transition-fast);\\n  transform: translateX(1px); \\n\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)   svg[_ngcontent-%COMP%] {\\n  transform: translateX(1px) scale(1.1);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-1);\\n  padding: var(--space-1) 0;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  background: #94a3b8;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n\\n\\n.auth-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  color: var(--color-gray-600);\\n  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 50%, #f0f4f8 100%);\\n}\\n.auth-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border: 4px solid var(--color-gray-200);\\n  border-top: 4px solid var(--color-accent);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: var(--space-4);\\n}\\n.auth-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-lg);\\n  color: var(--color-gray-600);\\n  margin: 0;\\n  font-weight: var(--font-weight-medium);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    opacity: 0.3;\\n    transform: scale(0.7) translateY(0);\\n  }\\n  30% {\\n    opacity: 1;\\n    transform: scale(1.3) translateY(-6px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(40px) scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .chat-container[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    height: 100vh;\\n    border-radius: 0;\\n    border: none;\\n  }\\n  .messages-container[_ngcontent-%COMP%] {\\n    padding: var(--space-5) var(--space-5) 140px var(--space-5);\\n  }\\n  .input-container[_ngcontent-%COMP%] {\\n    padding: var(--space-5) var(--space-5) var(--space-6) var(--space-5);\\n    max-width: 100%;\\n  }\\n  .input-container.centered[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    padding: var(--space-5);\\n    margin: 0 var(--space-4);\\n    width: calc(100% - var(--space-8));\\n  }\\n  .message[_ngcontent-%COMP%] {\\n    margin-bottom: var(--space-5);\\n  }\\n  .message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%], .message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n    max-width: 88%;\\n  }\\n  .input-wrapper[_ngcontent-%COMP%] {\\n    padding: var(--space-1) var(--space-2) var(--space-1) var(--space-4);\\n    gap: var(--space-2);\\n  }\\n  .send-button[_ngcontent-%COMP%] {\\n    width: 42px;\\n    height: 42px;\\n  }\\n  .send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n\\n\\n*[_ngcontent-%COMP%] {\\n  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast), transform var(--transition-fast), box-shadow var(--transition-fast);\\n}\\n\\n\\n\\n.send-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--color-primary);\\n  outline-offset: 3px;\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%]::selection {\\n  background: rgba(16, 185, 129, 0.2);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]::selection {\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n  30% {\\n    transform: translateY(-10px);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["marked", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction2", "_c1", "message_r8", "isUser", "ɵɵadvance", "ctx_r6", "formatMessageText", "text", "ɵɵsanitizeHtml", "ɵɵtextInterpolate", "ɵɵpipeBind2", "timestamp", "ɵɵlistener", "ChatComponent_div_1_div_1_Template_div_scroll_0_listener", "$event", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "onScroll", "ɵɵtemplate", "ChatComponent_div_1_div_1_div_2_Template", "ChatComponent_div_1_div_1_div_3_Template", "ChatComponent_div_1_div_1_div_4_Template", "ctx_r2", "isLoadingHistory", "messages", "isLoading", "ChatComponent_div_1_div_1_Template", "ChatComponent_div_1_Template_textarea_ngModelChange_4_listener", "_r12", "ctx_r11", "currentMessage", "ChatComponent_div_1_Template_textarea_keydown_4_listener", "ctx_r13", "onKeyPress", "ChatComponent_div_1_Template_textarea_input_4_listener", "ctx_r14", "adjustTextareaHeight", "ChatComponent_div_1_Template_button_click_7_listener", "ctx_r15", "sendMessage", "ɵɵnamespaceSVG", "ctx_r0", "length", "ɵɵclassProp", "trim", "ChatComponent", "constructor", "authService", "isAuthenticated", "currentSessionId", "currentPage", "hasNextPage", "shouldScrollToBottom", "lastScrollHeight", "initialLoadComplete", "userHasScrolled", "ngOnInit", "ensureAuthenticated", "subscribe", "next", "token", "console", "log", "loadChatHistory", "error", "ngAfterViewChecked", "scrollToBottom", "event", "element", "target", "scrollTop", "scrollHeight", "clientHeight", "scrollTimeout", "clearTimeout", "setTimeout", "isNearTop", "isNotAtBottom", "loadMoreHistory", "page", "append", "undefined", "response", "newMessages", "results", "map", "msg", "convertChatMessageToMessage", "reversedNewMessages", "reverse", "maintainScrollPosition", "chatMessage", "id", "message", "sender", "Date", "session", "prompt", "model", "news_articles", "messagesContainer", "nativeElement", "newScrollHeight", "scrollDifference", "messageToSend", "tempUserMessage", "push", "sendMessageToChatbot", "userMessage", "user_message", "botMessage", "bot_message", "lastMessageIndex", "errorMessage", "clearHistory", "refreshHistory", "key", "shift<PERSON>ey", "preventDefault", "textarea", "style", "height", "Math", "min", "setOptions", "breaks", "gfm", "htmlContent", "parse", "sanitizedHtml", "replace", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "viewQuery", "ChatComponent_Query", "rf", "ctx", "ChatComponent_div_1_Template", "ChatComponent_div_2_Template"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\chat\\chat.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\chat\\chat.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ViewChild, AfterViewChecked } from '@angular/core';\nimport { AuthService } from '../auth.service'; // Adjust path as needed\nimport { marked } from 'marked';\n\ninterface NewsArticle {\n  id?: number;\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string;\n  description: string;\n  fetched_at?: string;\n  created_at?: string;\n}\n\ninterface Message {\n  id?: number;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n  session?: number;\n  prompt?: number;\n  model?: number;\n  news_articles?: NewsArticle[]; // for bot messages that include news\n}\n\n// Django ChatMessage structure\ninterface ChatMessage {\n  id: number;\n  session: number;\n  sender: 'user' | 'bot';\n  message: string;\n  timestamp: string;\n  prompt: number | null;\n  model: number | null;\n  news_articles?: NewsArticle[] | null;\n}\n\n// Django paginated response\ninterface ChatHistoryResponse {\n  count: number;\n  next: string | null;\n  previous: string | null;\n  results: ChatMessage[];\n}\n\n// Django chatbot response\ninterface ChatbotResponse {\n  user_message: ChatMessage;\n  bot_message: ChatMessage;\n}\n\n@Component({\n  selector: 'app-chat',\n  templateUrl: './chat.component.html',\n  styleUrls: ['./chat.component.scss']\n})\nexport class ChatComponent implements OnInit, OnDestroy, AfterViewChecked {\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  \n  messages: Message[] = [];\n  currentMessage: string = '';\n  isLoading: boolean = false;\n  isAuthenticated: boolean = false;\n  currentSessionId: number | null = null;\n  \n  // Pagination\n  currentPage: number = 1;\n  hasNextPage: boolean = false;\n  isLoadingHistory: boolean = false;\n  \n  // Scroll management\n  private shouldScrollToBottom: boolean = true;\n  private lastScrollHeight: number = 0;\n  private initialLoadComplete: boolean = false;\n  private userHasScrolled: boolean = false;\n  private scrollTimeout: any;\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: (token) => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory(1, false);\n      },\n      error: (error) => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n\n  // Listen for scroll events to load more history\n  onScroll(event: any) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    const clientHeight = element.clientHeight;\n    \n    // Mark that user has scrolled manually (not programmatic)\n    if (this.initialLoadComplete) {\n      this.userHasScrolled = true;\n    }\n    \n    // Clear existing timeout\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    \n    // Debounce scroll events and check conditions\n    this.scrollTimeout = setTimeout(() => {\n      // Only load more if:\n      // 1. Initial load is complete\n      // 2. User has scrolled manually at least once\n      // 3. User is near the top (scrollTop < 100)\n      // 4. There are more pages to load\n      // 5. Not currently loading\n      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n      const isNearTop = scrollTop < 100;\n      const isNotAtBottom = scrollTop < (scrollHeight - clientHeight - 50);\n      \n      if (isNearTop && \n          isNotAtBottom &&\n          this.hasNextPage && \n          !this.isLoadingHistory && \n          this.initialLoadComplete &&\n          this.userHasScrolled) {\n        this.lastScrollHeight = scrollHeight;\n        this.loadMoreHistory();\n      }\n    }, 100); // 100ms debounce\n  }\n\n  loadChatHistory(page: number = 1, append: boolean = false) {\n    if (!this.isAuthenticated) return;\n\n    this.isLoadingHistory = true;\n\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatHistoryResponse) => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        \n        if (append) {\n          // For pagination - reverse the new messages (since API returns newest first)\n          // and prepend older messages to beginning\n          const reversedNewMessages = [...newMessages].reverse();\n          this.messages = [...reversedNewMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - reverse messages to get chronological order (oldest first)\n          this.messages = [...newMessages].reverse();\n          this.shouldScrollToBottom = true;\n          \n          // Set initial load complete after scroll positioning is done\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n        \n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        \n        this.isLoadingHistory = false;\n      },\n      error: (error) => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        \n        // If this was the initial load, still mark it as complete after delay\n        if (!append) {\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n      }\n    });\n  }\n\n  // Convert Django ChatMessage to frontend Message format\n  private convertChatMessageToMessage(chatMessage: ChatMessage): Message {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined,\n      news_articles: chatMessage.news_articles || undefined,\n    };\n  }\n\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n\n  // Maintain scroll position when loading older messages\n  private maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n\n    // Create temporary user message and add it instantly\n    const tempUserMessage: Message = {\n      text: messageToSend,\n      isUser: true,\n      timestamp: new Date()\n    };\n\n    // Add user message instantly to the chat\n    this.messages.push(tempUserMessage);\n    this.shouldScrollToBottom = true;\n\n    // Set loading state\n    this.isLoading = true;\n\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatbotResponse) => {\n        // Convert backend messages\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n\n        // Replace the temporary user message with the one from backend\n        const lastMessageIndex = this.messages.length - 1;\n        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n          this.messages[lastMessageIndex] = userMessage;\n        }\n\n        // Add bot message\n        this.messages.push(botMessage);\n\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        const errorMessage: Message = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n\n  clearHistory() {\n    this.messages = [];\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n\n  refreshHistory() {\n    this.currentPage = 1;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.loadChatHistory();\n  }\n\n  onKeyPress(event: KeyboardEvent) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  adjustTextareaHeight(event: any) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n\n  private scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n\n  // Format message text using marked library for markdown\n  formatMessageText(text: string): string {\n    if (!text) return '';\n\n    try {\n      // Configure marked to be more restrictive for security\n      marked.setOptions({\n        breaks: true, // Convert line breaks to <br>\n        gfm: true // Enable GitHub Flavored Markdown\n      });\n\n      // Convert markdown to HTML using marked (synchronous)\n      const htmlContent = marked.parse(text) as string;\n\n      // Basic sanitization - remove script tags and dangerous attributes\n      let sanitizedHtml = htmlContent\n        .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n        .replace(/javascript:/gi, '')\n        .replace(/on\\w+\\s*=/gi, '');\n\n      return sanitizedHtml;\n    } catch (error) {\n      console.error('Error formatting message text:', error);\n      // Fallback to plain text with basic line break conversion\n      return text.replace(/\\n/g, '<br>');\n    }\n  }\n\n  ngOnDestroy() {\n    // Clean up any pending timeouts\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n}", "<!-- chat.component.html -->\n\n<!-- Anonymous user component runs in background -->\n<app-anonymous-user></app-anonymous-user>\n\n<div class=\"chat-container\" *ngIf=\"isAuthenticated\">\n  <!-- Messages container - only show when there are messages -->\n  <div \n    #messagesContainer\n    class=\"messages-container\" \n    (scroll)=\"onScroll($event)\"\n    *ngIf=\"messages.length > 0\">\n    \n    <!-- Loading indicator for pagination at the top -->\n    <div class=\"pagination-loading\" *ngIf=\"isLoadingHistory\">\n      <div class=\"loading-spinner\"></div>\n    </div>\n    \n    <div class=\"message\"\n         *ngFor=\"let message of messages\"\n         [ngClass]=\"{'user-message': message.isUser, 'bot-message': !message.isUser}\">\n      <div class=\"message-content\">\n        <div class=\"message-text\" [innerHTML]=\"formatMessageText(message.text)\"></div>\n        <div class=\"message-time\">{{ message.timestamp | date:'short' }}</div>\n      </div>\n\n    </div>\n\n    <!-- Loading indicator for current message -->\n    <div class=\"message bot-message\" *ngIf=\"isLoading\">\n      <div class=\"message-content\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input container - always visible -->\n  <div class=\"input-container\" [class.centered]=\"messages.length === 0\" [class.bottom]=\"messages.length > 0\">\n    <div class=\"input-wrapper\">\n      <textarea\n        #messageTextarea\n        [(ngModel)]=\"currentMessage\"\n        (keydown)=\"onKeyPress($event)\"\n        (input)=\"adjustTextareaHeight($event)\"\n        placeholder=\"Ask anything\"\n        class=\"message-input\"\n        rows=\"1\">\n      </textarea>\n      <button\n        (click)=\"sendMessage()\"\n        class=\"send-button\"\n        [disabled]=\"!currentMessage.trim() || isLoading\">\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"currentColor\"/>\n        </svg>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Show loading message while authenticating -->\n<div *ngIf=\"!isAuthenticated\" class=\"auth-loading\">\n  <div class=\"loading-spinner\"></div>\n  <p>Initializing chat...</p>\n</div>"], "mappings": "AAEA,SAASA,MAAM,QAAQ,QAAQ;;;;;;;;;ICY3BC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,cAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;IAENH,EAAA,CAAAC,cAAA,cAEkF;IAE9ED,EAAA,CAAAE,SAAA,cAA8E;IAC9EF,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAI,MAAA,GAAsC;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAHrEH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAC,UAAA,CAAAC,MAAA,GAAAD,UAAA,CAAAC,MAAA,EAA4E;IAEnDT,EAAA,CAAAU,SAAA,GAA6C;IAA7CV,EAAA,CAAAK,UAAA,cAAAM,MAAA,CAAAC,iBAAA,CAAAJ,UAAA,CAAAK,IAAA,GAAAb,EAAA,CAAAc,cAAA,CAA6C;IAC7Cd,EAAA,CAAAU,SAAA,GAAsC;IAAtCV,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAgB,WAAA,OAAAR,UAAA,CAAAS,SAAA,WAAsC;;;;;IAMpEjB,EAAA,CAAAC,cAAA,cAAmD;IAG7CD,EAAA,CAAAE,SAAA,WAAa;IAGfF,EAAA,CAAAG,YAAA,EAAM;;;;;;IA5BZH,EAAA,CAAAC,cAAA,kBAI8B;IAD5BD,EAAA,CAAAkB,UAAA,oBAAAC,yDAAAC,MAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAUxB,EAAA,CAAAyB,WAAA,CAAAF,MAAA,CAAAG,QAAA,CAAAN,MAAA,CAAgB;IAAA,EAAC;IAI3BpB,EAAA,CAAA2B,UAAA,IAAAC,wCAAA,kBAEM;IAEN5B,EAAA,CAAA2B,UAAA,IAAAE,wCAAA,kBAQM;IAGN7B,EAAA,CAAA2B,UAAA,IAAAG,wCAAA,kBAQM;IACR9B,EAAA,CAAAG,YAAA,EAAM;;;;IAxB6BH,EAAA,CAAAU,SAAA,GAAsB;IAAtBV,EAAA,CAAAK,UAAA,SAAA0B,MAAA,CAAAC,gBAAA,CAAsB;IAK9BhC,EAAA,CAAAU,SAAA,GAAW;IAAXV,EAAA,CAAAK,UAAA,YAAA0B,MAAA,CAAAE,QAAA,CAAW;IAUFjC,EAAA,CAAAU,SAAA,GAAe;IAAfV,EAAA,CAAAK,UAAA,SAAA0B,MAAA,CAAAG,SAAA,CAAe;;;;;;IAxBrDlC,EAAA,CAAAC,cAAA,aAAoD;IAElDD,EAAA,CAAA2B,UAAA,IAAAQ,kCAAA,iBA+BM;IAGNnC,EAAA,CAAAC,cAAA,aAA2G;IAIrGD,EAAA,CAAAkB,UAAA,2BAAAkB,+DAAAhB,MAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAgB,IAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAAa,OAAA,CAAAC,cAAA,GAAAnB,MAAA;IAAA,EAA4B,qBAAAoB,yDAAApB,MAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAgB,IAAA;MAAA,MAAAI,OAAA,GAAAzC,EAAA,CAAAwB,aAAA;MAAA,OACjBxB,EAAA,CAAAyB,WAAA,CAAAgB,OAAA,CAAAC,UAAA,CAAAtB,MAAA,CAAkB;IAAA,EADD,mBAAAuB,uDAAAvB,MAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAgB,IAAA;MAAA,MAAAO,OAAA,GAAA5C,EAAA,CAAAwB,aAAA;MAAA,OAEnBxB,EAAA,CAAAyB,WAAA,CAAAmB,OAAA,CAAAC,oBAAA,CAAAzB,MAAA,CAA4B;IAAA,EAFT;IAM9BpB,EAAA,CAAAI,MAAA;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,gBAGmD;IAFjDD,EAAA,CAAAkB,UAAA,mBAAA4B,qDAAA;MAAA9C,EAAA,CAAAqB,aAAA,CAAAgB,IAAA;MAAA,MAAAU,OAAA,GAAA/C,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAsB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvBhD,EAAA,CAAAiD,cAAA,EAA+F;IAA/FjD,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAE,SAAA,eAAiE;IACnEF,EAAA,CAAAG,YAAA,EAAM;;;;IA/CTH,EAAA,CAAAU,SAAA,GAAyB;IAAzBV,EAAA,CAAAK,UAAA,SAAA6C,MAAA,CAAAjB,QAAA,CAAAkB,MAAA,KAAyB;IA8BCnD,EAAA,CAAAU,SAAA,GAAwC;IAAxCV,EAAA,CAAAoD,WAAA,aAAAF,MAAA,CAAAjB,QAAA,CAAAkB,MAAA,OAAwC,WAAAD,MAAA,CAAAjB,QAAA,CAAAkB,MAAA;IAI/DnD,EAAA,CAAAU,SAAA,GAA4B;IAA5BV,EAAA,CAAAK,UAAA,YAAA6C,MAAA,CAAAX,cAAA,CAA4B;IAU5BvC,EAAA,CAAAU,SAAA,GAAgD;IAAhDV,EAAA,CAAAK,UAAA,cAAA6C,MAAA,CAAAX,cAAA,CAAAc,IAAA,MAAAH,MAAA,CAAAhB,SAAA,CAAgD;;;;;IAUxDlC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,2BAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;ADT7B,OAAM,MAAOmD,aAAa;EAqBxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAlB/B,KAAAvB,QAAQ,GAAc,EAAE;IACxB,KAAAM,cAAc,GAAW,EAAE;IAC3B,KAAAL,SAAS,GAAY,KAAK;IAC1B,KAAAuB,eAAe,GAAY,KAAK;IAChC,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAA5B,gBAAgB,GAAY,KAAK;IAEjC;IACQ,KAAA6B,oBAAoB,GAAY,IAAI;IACpC,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,eAAe,GAAY,KAAK;EAGO;EAE/CC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,WAAW,CAACU,mBAAmB,EAAE,CAACC,SAAS,CAAC;MAC/CC,IAAI,EAAGC,KAAK,IAAI;QACdC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,IAAI,CAACd,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACe,eAAe,CAAC,CAAC,EAAE,KAAK,CAAC;MAChC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChB,eAAe,GAAG,KAAK;MAC9B;KACD,CAAC;EACJ;EAEAiB,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACb,oBAAoB,EAAE;MAC7B,IAAI,CAACc,cAAc,EAAE;MACrB,IAAI,CAACd,oBAAoB,GAAG,KAAK;;EAErC;EAEA;EACAnC,QAAQA,CAACkD,KAAU;IACjB,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM;IAC5B,MAAMC,SAAS,GAAGF,OAAO,CAACE,SAAS;IACnC,MAAMC,YAAY,GAAGH,OAAO,CAACG,YAAY;IACzC,MAAMC,YAAY,GAAGJ,OAAO,CAACI,YAAY;IAEzC;IACA,IAAI,IAAI,CAAClB,mBAAmB,EAAE;MAC5B,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B;IACA,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC;IACA,IAAI,CAACA,aAAa,GAAGE,UAAU,CAAC,MAAK;MACnC;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMC,SAAS,GAAGN,SAAS,GAAG,GAAG;MACjC,MAAMO,aAAa,GAAGP,SAAS,GAAIC,YAAY,GAAGC,YAAY,GAAG,EAAG;MAEpE,IAAII,SAAS,IACTC,aAAa,IACb,IAAI,CAAC1B,WAAW,IAChB,CAAC,IAAI,CAAC5B,gBAAgB,IACtB,IAAI,CAAC+B,mBAAmB,IACxB,IAAI,CAACC,eAAe,EAAE;QACxB,IAAI,CAACF,gBAAgB,GAAGkB,YAAY;QACpC,IAAI,CAACO,eAAe,EAAE;;IAE1B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;;EAEAf,eAAeA,CAACgB,IAAA,GAAe,CAAC,EAAEC,MAAA,GAAkB,KAAK;IACvD,IAAI,CAAC,IAAI,CAAChC,eAAe,EAAE;IAE3B,IAAI,CAACzB,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAACwB,WAAW,CAACgB,eAAe,CAACgB,IAAI,EAAE,IAAI,CAAC9B,gBAAgB,IAAIgC,SAAS,CAAC,CAACvB,SAAS,CAAC;MACnFC,IAAI,EAAGuB,QAA6B,IAAI;QACtC,MAAMC,WAAW,GAAGD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,GAAG,IAAI,IAAI,CAACC,2BAA2B,CAACD,GAAG,CAAC,CAAC;QAEtF,IAAIN,MAAM,EAAE;UACV;UACA;UACA,MAAMQ,mBAAmB,GAAG,CAAC,GAAGL,WAAW,CAAC,CAACM,OAAO,EAAE;UACtD,IAAI,CAACjE,QAAQ,GAAG,CAAC,GAAGgE,mBAAmB,EAAE,GAAG,IAAI,CAAChE,QAAQ,CAAC;UAC1D,IAAI,CAACkE,sBAAsB,EAAE;SAC9B,MAAM;UACL;UACA,IAAI,CAAClE,QAAQ,GAAG,CAAC,GAAG2D,WAAW,CAAC,CAACM,OAAO,EAAE;UAC1C,IAAI,CAACrC,oBAAoB,GAAG,IAAI;UAEhC;UACAuB,UAAU,CAAC,MAAK;YACd,IAAI,CAACrB,mBAAmB,GAAG,IAAI;UACjC,CAAC,EAAE,GAAG,CAAC;;QAGT;QACA,IAAI,CAACJ,WAAW,GAAG6B,IAAI;QACvB,IAAI,CAAC5B,WAAW,GAAG,CAAC,CAAC+B,QAAQ,CAACvB,IAAI;QAElC,IAAI,CAACpC,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDyC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACzC,gBAAgB,GAAG,KAAK;QAE7B;QACA,IAAI,CAACyD,MAAM,EAAE;UACXL,UAAU,CAAC,MAAK;YACd,IAAI,CAACrB,mBAAmB,GAAG,IAAI;UACjC,CAAC,EAAE,GAAG,CAAC;;MAEX;KACD,CAAC;EACJ;EAEA;EACQiC,2BAA2BA,CAACI,WAAwB;IAC1D,OAAO;MACLC,EAAE,EAAED,WAAW,CAACC,EAAE;MAClBxF,IAAI,EAAEuF,WAAW,CAACE,OAAO;MACzB7F,MAAM,EAAE2F,WAAW,CAACG,MAAM,KAAK,MAAM;MACrCtF,SAAS,EAAE,IAAIuF,IAAI,CAACJ,WAAW,CAACnF,SAAS,CAAC;MAC1CwF,OAAO,EAAEL,WAAW,CAACK,OAAO;MAC5BC,MAAM,EAAEN,WAAW,CAACM,MAAM,IAAIhB,SAAS;MACvCiB,KAAK,EAAEP,WAAW,CAACO,KAAK,IAAIjB,SAAS;MACrCkB,aAAa,EAAER,WAAW,CAACQ,aAAa,IAAIlB;KAC7C;EACH;EAEA;EACAH,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3B,WAAW,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,EAAE;MAC9C,IAAI,CAACwC,eAAe,CAAC,IAAI,CAACb,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;;EAEpD;EAEA;EACQwC,sBAAsBA,CAAA;IAC5Bf,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACyB,iBAAiB,EAAE;QAC1B,MAAMhC,OAAO,GAAG,IAAI,CAACgC,iBAAiB,CAACC,aAAa;QACpD,MAAMC,eAAe,GAAGlC,OAAO,CAACG,YAAY;QAC5C,MAAMgC,gBAAgB,GAAGD,eAAe,GAAG,IAAI,CAACjD,gBAAgB;QAChEe,OAAO,CAACE,SAAS,GAAGiC,gBAAgB;;IAExC,CAAC,EAAE,EAAE,CAAC;EACR;EAEAhE,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACT,cAAc,CAACc,IAAI,EAAE,IAAI,IAAI,CAACnB,SAAS,IAAI,CAAC,IAAI,CAACuB,eAAe,EAAE;MAC1E;;IAGF;IACA,MAAMwD,aAAa,GAAG,IAAI,CAAC1E,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IAExB;IACA,MAAM2E,eAAe,GAAY;MAC/BrG,IAAI,EAAEoG,aAAa;MACnBxG,MAAM,EAAE,IAAI;MACZQ,SAAS,EAAE,IAAIuF,IAAI;KACpB;IAED;IACA,IAAI,CAACvE,QAAQ,CAACkF,IAAI,CAACD,eAAe,CAAC;IACnC,IAAI,CAACrD,oBAAoB,GAAG,IAAI;IAEhC;IACA,IAAI,CAAC3B,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACsB,WAAW,CAAC4D,oBAAoB,CAACH,aAAa,EAAE,IAAI,CAACvD,gBAAgB,IAAIgC,SAAS,CAAC,CAACvB,SAAS,CAAC;MACjGC,IAAI,EAAGuB,QAAyB,IAAI;QAClC;QACA,MAAM0B,WAAW,GAAG,IAAI,CAACrB,2BAA2B,CAACL,QAAQ,CAAC2B,YAAY,CAAC;QAC3E,MAAMC,UAAU,GAAG,IAAI,CAACvB,2BAA2B,CAACL,QAAQ,CAAC6B,WAAW,CAAC;QAEzE;QACA,MAAMC,gBAAgB,GAAG,IAAI,CAACxF,QAAQ,CAACkB,MAAM,GAAG,CAAC;QACjD,IAAIsE,gBAAgB,IAAI,CAAC,IAAI,IAAI,CAACxF,QAAQ,CAACwF,gBAAgB,CAAC,CAAChH,MAAM,EAAE;UACnE,IAAI,CAACwB,QAAQ,CAACwF,gBAAgB,CAAC,GAAGJ,WAAW;;QAG/C;QACA,IAAI,CAACpF,QAAQ,CAACkF,IAAI,CAACI,UAAU,CAAC;QAE9B;QACA,IAAI,CAAC,IAAI,CAAC7D,gBAAgB,EAAE;UAC1B,IAAI,CAACA,gBAAgB,GAAGiC,QAAQ,CAAC2B,YAAY,CAACb,OAAO;;QAGvD,IAAI,CAACvE,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC2B,oBAAoB,GAAG,IAAI;MAClC,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMiD,YAAY,GAAY;UAC5B7G,IAAI,EAAE,sEAAsE;UAC5EJ,MAAM,EAAE,KAAK;UACbQ,SAAS,EAAE,IAAIuF,IAAI;SACpB;QACD,IAAI,CAACvE,QAAQ,CAACkF,IAAI,CAACO,YAAY,CAAC;QAChC,IAAI,CAACxF,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC2B,oBAAoB,GAAG,IAAI;MAClC;KACD,CAAC;EACJ;EAEA8D,YAAYA,CAAA;IACV,IAAI,CAAC1F,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC0B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEA0C,cAAcA,CAAA;IACZ,IAAI,CAACjE,WAAW,GAAG,CAAC;IACpB,IAAI,CAACI,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAElC,IAAI,CAACV,eAAe,EAAE;EACxB;EAEA9B,UAAUA,CAACkC,KAAoB;IAC7B,IAAIA,KAAK,CAACiD,GAAG,KAAK,OAAO,IAAI,CAACjD,KAAK,CAACkD,QAAQ,EAAE;MAC5ClD,KAAK,CAACmD,cAAc,EAAE;MACtB,IAAI,CAAC/E,WAAW,EAAE;;EAEtB;EAEAH,oBAAoBA,CAAC+B,KAAU;IAC7B,MAAMoD,QAAQ,GAAGpD,KAAK,CAACE,MAAM;IAC7BkD,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;IAC9BF,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAChD,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;EACrE;EAEQL,cAAcA,CAAA;IACpBS,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACyB,iBAAiB,EAAE;QAC1B,MAAMhC,OAAO,GAAG,IAAI,CAACgC,iBAAiB,CAACC,aAAa;QACpDjC,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,EAAE,CAAC;EACR;EAEA;EACApE,iBAAiBA,CAACC,IAAY;IAC5B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,IAAI;MACF;MACAd,MAAM,CAACsI,UAAU,CAAC;QAChBC,MAAM,EAAE,IAAI;QACZC,GAAG,EAAE,IAAI,CAAC;OACX,CAAC;MAEF;MACA,MAAMC,WAAW,GAAGzI,MAAM,CAAC0I,KAAK,CAAC5H,IAAI,CAAW;MAEhD;MACA,IAAI6H,aAAa,GAAGF,WAAW,CAC5BG,OAAO,CAAC,qDAAqD,EAAE,EAAE,CAAC,CAClEA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;MAE7B,OAAOD,aAAa;KACrB,CAAC,OAAOjE,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACA,OAAO5D,IAAI,CAAC8H,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;;EAEtC;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC1D,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAAC,QAAA2D,CAAA,G;qBA/SUvF,aAAa,EAAAtD,EAAA,CAAA8I,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAb3F,aAAa;IAAA4F,SAAA;IAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCvD1BrJ,EAAA,CAAAE,SAAA,yBAAyC;QAEzCF,EAAA,CAAA2B,UAAA,IAAA4H,4BAAA,kBAyDM;QAGNvJ,EAAA,CAAA2B,UAAA,IAAA6H,4BAAA,iBAGM;;;QA/DuBxJ,EAAA,CAAAU,SAAA,GAAqB;QAArBV,EAAA,CAAAK,UAAA,SAAAiJ,GAAA,CAAA7F,eAAA,CAAqB;QA4D5CzD,EAAA,CAAAU,SAAA,GAAsB;QAAtBV,EAAA,CAAAK,UAAA,UAAAiJ,GAAA,CAAA7F,eAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}