{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./chat/chat.component\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'News Agent';\n    // Sample news data to demonstrate search and filters\n    this.sampleNews = [{\n      country: 'ukraine',\n      source: 'Kyiv Independent',\n      title: 'Ukraine receives new military aid package',\n      link: 'https://example.com/news1',\n      published: '2024-01-15T10:30:00Z',\n      description: 'NATO allies announce comprehensive military aid package including advanced defense systems.'\n    }, {\n      country: 'poland',\n      source: 'Warsaw Times',\n      title: 'Poland strengthens eastern border security',\n      link: 'https://example.com/news2',\n      published: '2024-01-14T15:45:00Z',\n      description: 'Polish government implements enhanced security protocols along its eastern border.'\n    }, {\n      country: 'russia',\n      source: 'Moscow Herald',\n      title: 'Russian economic indicators show mixed results',\n      link: 'https://example.com/news3',\n      published: '2024-01-13T09:15:00Z',\n      description: 'Latest economic data from Russia reveals varying performance across different sectors.'\n    }, {\n      country: 'belarus',\n      source: 'Minsk Daily',\n      title: 'Belarus announces new agricultural initiatives',\n      link: 'https://example.com/news4',\n      published: '2024-01-12T14:20:00Z',\n      description: 'Government unveils comprehensive agricultural development program.'\n    }, {\n      country: 'ukraine',\n      source: 'Ukrainian Voice',\n      title: 'Reconstruction efforts accelerate in liberated territories',\n      link: 'https://example.com/news5',\n      published: '2024-01-11T11:00:00Z',\n      description: 'International cooperation drives rapid reconstruction of infrastructure.'\n    }];\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 5,\n    vars: 1,\n    consts: [[1, \"app-container\"], [1, \"main-content\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"main\", 1)(2, \"h1\");\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(4, \"app-chat\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.title);\n      }\n    },\n    dependencies: [i1.ChatComponent],\n    styles: [\"\\n\\n.app-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  background: transparent;\\n  overflow: hidden;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  background-color: transparent;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.main-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 50%, #8b5cf6 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  margin: 0;\\n  padding: var(--space-8) 0 var(--space-6) 0;\\n  font-size: var(--font-size-3xl);\\n  font-weight: var(--font-weight-bold);\\n  flex-shrink: 0;\\n  letter-spacing: -0.025em;\\n  position: relative;\\n}\\n.main-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: var(--space-4);\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(90deg, #4f46e5 0%, #8b5cf6 100%);\\n  border-radius: 2px;\\n}\\n\\n.chat-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0; \\n\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  \\n\\n}\\n.chat-section[_ngcontent-%COMP%]   app-chat[_ngcontent-%COMP%] {\\n  display: block;\\n  height: 100%;\\n}\\n.chat-section[_ngcontent-%COMP%]   app-chat[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%] {\\n  height: 100% !important;\\n  max-width: none !important;\\n  margin: 0 !important;\\n}\\n\\n.news-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0; \\n\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  background: var(--color-white);\\n  border: 1px solid var(--color-gray-200);\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-sm);\\n  max-height: calc(100vh - 120px); \\n\\n  \\n\\n}\\n.news-section[_ngcontent-%COMP%]   app-news[_ngcontent-%COMP%] {\\n  display: block;\\n  height: 100%;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  \\n\\n}\\n.news-section[_ngcontent-%COMP%]   app-news[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.news-section[_ngcontent-%COMP%]   app-news[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-gray-100);\\n  border-radius: var(--radius-md);\\n}\\n.news-section[_ngcontent-%COMP%]   app-news[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-gray-300);\\n  border-radius: var(--radius-md);\\n  -webkit-transition: var(--transition-fast);\\n  transition: var(--transition-fast);\\n}\\n.news-section[_ngcontent-%COMP%]   app-news[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-gray-400);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .app-container[_ngcontent-%COMP%] {\\n    height: 100vh;\\n  }\\n}\\n\\n\\n.sr-only[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border: 0;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.text-left[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n\\n.text-right[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.mb-0[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.mb-2[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-2);\\n}\\n\\n.mb-4[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n}\\n\\n.mb-6[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-6);\\n}\\n\\n.mt-0[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n}\\n\\n.mt-2[_ngcontent-%COMP%] {\\n  margin-top: var(--space-2);\\n}\\n\\n.mt-4[_ngcontent-%COMP%] {\\n  margin-top: var(--space-4);\\n}\\n\\n.mt-6[_ngcontent-%COMP%] {\\n  margin-top: var(--space-6);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "sampleNews", "country", "source", "link", "published", "description", "_", "_2", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { NewsItem } from './news/news.component';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = 'News Agent';\n\n  // Sample news data to demonstrate search and filters\n  sampleNews: NewsItem[] = [\n    {\n      country: 'ukraine',\n      source: 'Kyiv Independent',\n      title: 'Ukraine receives new military aid package',\n      link: 'https://example.com/news1',\n      published: '2024-01-15T10:30:00Z',\n      description: 'NATO allies announce comprehensive military aid package including advanced defense systems.'\n    },\n    {\n      country: 'poland',\n      source: 'Warsaw Times',\n      title: 'Poland strengthens eastern border security',\n      link: 'https://example.com/news2',\n      published: '2024-01-14T15:45:00Z',\n      description: 'Polish government implements enhanced security protocols along its eastern border.'\n    },\n    {\n      country: 'russia',\n      source: 'Moscow Herald',\n      title: 'Russian economic indicators show mixed results',\n      link: 'https://example.com/news3',\n      published: '2024-01-13T09:15:00Z',\n      description: 'Latest economic data from Russia reveals varying performance across different sectors.'\n    },\n    {\n      country: 'belarus',\n      source: 'Minsk Daily',\n      title: 'Belarus announces new agricultural initiatives',\n      link: 'https://example.com/news4',\n      published: '2024-01-12T14:20:00Z',\n      description: 'Government unveils comprehensive agricultural development program.'\n    },\n    {\n      country: 'ukraine',\n      source: 'Ukrainian Voice',\n      title: 'Reconstruction efforts accelerate in liberated territories',\n      link: 'https://example.com/news5',\n      published: '2024-01-11T11:00:00Z',\n      description: 'International cooperation drives rapid reconstruction of infrastructure.'\n    }\n  ];\n}\n", "<div class=\"app-container\">\n  <main class=\"main-content\">\n    <h1>{{ title }}</h1>\n    <!-- <app-news></app-news> -->\n    <app-chat></app-chat>\n  </main>\n</div>\n"], "mappings": ";;AAQA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,YAAY;IAEpB;IACA,KAAAC,UAAU,GAAe,CACvB;MACEC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,kBAAkB;MAC1BH,KAAK,EAAE,2CAA2C;MAClDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,cAAc;MACtBH,KAAK,EAAE,4CAA4C;MACnDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,eAAe;MACvBH,KAAK,EAAE,gDAAgD;MACvDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,aAAa;MACrBH,KAAK,EAAE,gDAAgD;MACvDI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,EACD;MACEJ,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,iBAAiB;MACzBH,KAAK,EAAE,4DAA4D;MACnEI,IAAI,EAAE,2BAA2B;MACjCC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE;KACd,CACF;;EACF,QAAAC,CAAA,G;qBA9CYT,YAAY;EAAA;EAAA,QAAAU,EAAA,G;UAAZV,YAAY;IAAAW,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRzBE,EAAA,CAAAC,cAAA,aAA2B;QAEnBD,EAAA,CAAAE,MAAA,GAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEpBH,EAAA,CAAAI,SAAA,eAAqB;QACvBJ,EAAA,CAAAG,YAAA,EAAO;;;QAHDH,EAAA,CAAAK,SAAA,GAAW;QAAXL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAAhB,KAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}