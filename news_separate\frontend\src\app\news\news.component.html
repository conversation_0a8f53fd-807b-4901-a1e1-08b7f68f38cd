<div class="news-wrapper" [ngClass]="{ 'compact': compact }" *ngIf="news && news.length">
  <h2 class="section-title" *ngIf="showTitle">Latest News</h2>

  <!-- Search and Filter Controls - Only show when not in compact mode -->
  <div class="news-controls" *ngIf="!compact">
    <!-- Search Bar -->
    <div class="search-container">
      <input
        type="text"
        class="search-input"
        placeholder="Search news..."
        [value]="searchTerm"
        (input)="onSearchChange($event)"
      />
    </div>

    <!-- Filter Controls -->
    <div class="filter-controls">
      <select
        class="filter-select"
        [value]="selectedCountry"
        (change)="onCountryChange($event)"
      >
        <option value="">All Countries</option>
        <option *ngFor="let country of availableCountries" [value]="country">
          {{ country | titlecase }}
        </option>
      </select>

      <select
        class="filter-select"
        [value]="selectedSource"
        (change)="onSourceChange($event)"
      >
        <option value="">All Sources</option>
        <option *ngFor="let source of availableSources" [value]="source">
          {{ source }}
        </option>
      </select>

      <button
        class="clear-filters-btn"
        (click)="clearFilters()"
        type="button"
        *ngIf="searchTerm || selectedCountry || selectedSource"
      >
        Clear Filters
      </button>
    </div>
  </div>

  <div class="news-grid">
    <a
      class="news-card"
      *ngFor="let item of filteredNews; trackBy: trackByTitle"
      [href]="item.link"
      target="_blank"
      rel="noopener noreferrer"
    >
      <div class="news-card-header">
        <span class="badge">{{ item.country | titlecase }}</span>
        <span class="source">{{ item.source }}</span>
      </div>

      <h3 class="title">{{ item.title }}</h3>
      <p class="description">{{ item.description }}</p>

      <div class="news-card-footer">
        <span class="published">{{ item.published | date:'medium' }}</span>
        <span class="cta">Read →</span>
      </div>
    </a>
  </div>

  <!-- No Results Message -->
  <div class="no-results" *ngIf="filteredNews.length === 0 && (searchTerm || selectedCountry || selectedSource)">
    <p>No articles found. Try adjusting your search or filters.</p>
    <button class="clear-filters-btn" (click)="clearFilters()" type="button">
      Clear All Filters
    </button>
  </div>
</div>
