/* ===== APP LAYOUT ===== */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: transparent;
  overflow: hidden;
}

.app-header {
  flex-shrink: 0;
  padding: var(--space-4) 0;
  background-color: transparent;

  h1 {
    text-align: center;
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 50%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    padding: var(--space-4) 0 var(--space-2) 0;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    letter-spacing: -0.025em;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: linear-gradient(90deg, #4f46e5 0%, #8b5cf6 100%);
      border-radius: 2px;
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  background-color: transparent;
  gap: var(--space-4);
  padding: 0 var(--space-4) var(--space-4) var(--space-4);
}

.chat-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0; /* Prevents flex item from growing beyond container */
}

.news-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0; /* Prevents flex item from growing beyond container */
}

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .chat-section,
  .news-section {
    flex: 1;
    min-height: 0;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 0 var(--space-2) var(--space-2) var(--space-2);
  }
}

/* ===== UTILITY CLASSES ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }

.mt-0 { margin-top: 0; }
.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }