{"ast": null, "code": "import { marked } from 'marked';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../anonymous-user/anonymous-user.component\";\nimport * as i5 from \"../news/news.component\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatComponent_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_1_div_1_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵelement(1, \"app-news\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"news\", message_r8.news_articles)(\"showTitle\", false)(\"compact\", true);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatComponent_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"div\", 20);\n    i0.ɵɵelementStart(3, \"div\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ChatComponent_div_1_div_1_div_3_div_6_Template, 2, 3, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r8 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c1, message_r8.isUser, !message_r8.isUser));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r6.formatMessageText(message_r8.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 4, message_r8.timestamp, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !message_r8.isUser && message_r8.news_articles && message_r8.news_articles.length);\n  }\n}\nfunction ChatComponent_div_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 19)(2, \"div\", 26);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11, 12);\n    i0.ɵɵlistener(\"scroll\", function ChatComponent_div_1_div_1_Template_div_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onScroll($event));\n    });\n    i0.ɵɵtemplate(2, ChatComponent_div_1_div_1_div_2_Template, 2, 0, \"div\", 13);\n    i0.ɵɵtemplate(3, ChatComponent_div_1_div_1_div_3_Template, 7, 10, \"div\", 14);\n    i0.ɵɵtemplate(4, ChatComponent_div_1_div_1_div_4_Template, 6, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ChatComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, ChatComponent_div_1_div_1_Template, 5, 3, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\", 5)(4, \"textarea\", 6, 7);\n    i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_div_1_Template_textarea_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.currentMessage = $event);\n    })(\"keydown\", function ChatComponent_div_1_Template_textarea_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onKeyPress($event));\n    })(\"input\", function ChatComponent_div_1_Template_textarea_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.adjustTextareaHeight($event));\n    });\n    i0.ɵɵtext(6, \"      \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_1_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.sendMessage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 9);\n    i0.ɵɵelement(9, \"path\", 10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"centered\", ctx_r0.messages.length === 0)(\"bottom\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.currentMessage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.currentMessage.trim() || ctx_r0.isLoading);\n  }\n}\nfunction ChatComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Initializing chat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ChatComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.messages = [];\n    this.currentMessage = '';\n    this.isLoading = false;\n    this.isAuthenticated = false;\n    this.currentSessionId = null;\n    // Pagination\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.isLoadingHistory = false;\n    // Scroll management\n    this.shouldScrollToBottom = true;\n    this.lastScrollHeight = 0;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n  }\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: token => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory(1, false);\n      },\n      error: error => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n  // Listen for scroll events to load more history\n  onScroll(event) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    const clientHeight = element.clientHeight;\n    // Mark that user has scrolled manually (not programmatic)\n    if (this.initialLoadComplete) {\n      this.userHasScrolled = true;\n    }\n    // Clear existing timeout\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    // Debounce scroll events and check conditions\n    this.scrollTimeout = setTimeout(() => {\n      // Only load more if:\n      // 1. Initial load is complete\n      // 2. User has scrolled manually at least once\n      // 3. User is near the top (scrollTop < 100)\n      // 4. There are more pages to load\n      // 5. Not currently loading\n      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n      const isNearTop = scrollTop < 100;\n      const isNotAtBottom = scrollTop < scrollHeight - clientHeight - 50;\n      if (isNearTop && isNotAtBottom && this.hasNextPage && !this.isLoadingHistory && this.initialLoadComplete && this.userHasScrolled) {\n        this.lastScrollHeight = scrollHeight;\n        this.loadMoreHistory();\n      }\n    }, 100); // 100ms debounce\n  }\n\n  loadChatHistory(page = 1, append = false) {\n    if (!this.isAuthenticated) return;\n    this.isLoadingHistory = true;\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        if (append) {\n          // For pagination - reverse the new messages (since API returns newest first)\n          // and prepend older messages to beginning\n          const reversedNewMessages = [...newMessages].reverse();\n          this.messages = [...reversedNewMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - reverse messages to get chronological order (oldest first)\n          this.messages = [...newMessages].reverse();\n          this.shouldScrollToBottom = true;\n          // Set initial load complete after scroll positioning is done\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        this.isLoadingHistory = false;\n      },\n      error: error => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        // If this was the initial load, still mark it as complete after delay\n        if (!append) {\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n      }\n    });\n  }\n  // Convert Django ChatMessage to frontend Message format\n  convertChatMessageToMessage(chatMessage) {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined,\n      news_articles: chatMessage.news_articles || undefined\n    };\n  }\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n  // Maintain scroll position when loading older messages\n  maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    // Create temporary user message and add it instantly\n    const tempUserMessage = {\n      text: messageToSend,\n      isUser: true,\n      timestamp: new Date()\n    };\n    // Add user message instantly to the chat\n    this.messages.push(tempUserMessage);\n    this.shouldScrollToBottom = true;\n    // Set loading state\n    this.isLoading = true;\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        // Convert backend messages\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n        // Replace the temporary user message with the one from backend\n        const lastMessageIndex = this.messages.length - 1;\n        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n          this.messages[lastMessageIndex] = userMessage;\n        }\n        // Add bot message\n        this.messages.push(botMessage);\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        const errorMessage = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n  clearHistory() {\n    this.messages = [];\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n  refreshHistory() {\n    this.currentPage = 1;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.loadChatHistory();\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  adjustTextareaHeight(event) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n  // Format message text using marked library for markdown\n  formatMessageText(text) {\n    if (!text) return '';\n    try {\n      // Configure marked to be more restrictive for security\n      marked.setOptions({\n        breaks: true,\n        gfm: true // Enable GitHub Flavored Markdown\n      });\n      // Convert markdown to HTML using marked (synchronous)\n      const htmlContent = marked.parse(text);\n      // Basic sanitization - remove script tags and dangerous attributes\n      let sanitizedHtml = htmlContent.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '').replace(/javascript:/gi, '').replace(/on\\w+\\s*=/gi, '');\n      return sanitizedHtml;\n    } catch (error) {\n      console.error('Error formatting message text:', error);\n      // Fallback to plain text with basic line break conversion\n      return text.replace(/\\n/g, '<br>');\n    }\n  }\n  ngOnDestroy() {\n    // Clean up any pending timeouts\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function ChatComponent_Factory(t) {\n    return new (t || ChatComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChatComponent,\n    selectors: [[\"app-chat\"]],\n    viewQuery: function ChatComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n      }\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"chat-container\", 4, \"ngIf\"], [\"class\", \"auth-loading\", 4, \"ngIf\"], [1, \"chat-container\"], [\"class\", \"messages-container\", 3, \"scroll\", 4, \"ngIf\"], [1, \"input-container\"], [1, \"input-wrapper\"], [\"placeholder\", \"Ask anything\", \"rows\", \"1\", 1, \"message-input\", 3, \"ngModel\", \"ngModelChange\", \"keydown\", \"input\"], [\"messageTextarea\", \"\"], [1, \"send-button\", 3, \"disabled\", \"click\"], [\"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M2 21L23 12L2 3V10L17 12L2 14V21Z\", \"fill\", \"currentColor\"], [1, \"messages-container\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"pagination-loading\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"message bot-message\", 4, \"ngIf\"], [1, \"pagination-loading\"], [1, \"loading-spinner\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-text\", 3, \"innerHTML\"], [1, \"message-time\"], [\"class\", \"message-news\", 4, \"ngIf\"], [1, \"message-news\"], [3, \"news\", \"showTitle\", \"compact\"], [1, \"message\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"auth-loading\"]],\n    template: function ChatComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-anonymous-user\");\n        i0.ɵɵtemplate(1, ChatComponent_div_1_Template, 10, 7, \"div\", 0);\n        i0.ɵɵtemplate(2, ChatComponent_div_2_Template, 4, 0, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.AnonymousUserComponent, i5.NewsComponent, i2.DatePipe],\n    styles: [\"\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  max-width: 900px;\\n  margin: 0 auto;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  position: relative;\\n  overflow: hidden;\\n  font-family: var(--font-family-primary);\\n  border-radius: var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: var(--space-6) var(--space-8) 120px var(--space-8);\\n  background: #f9fafb;\\n  max-height: calc(100vh - 160px);\\n  scroll-behavior: smooth;\\n  position: relative;\\n  z-index: 10;\\n  \\n\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f5f9;\\n  border-radius: var(--radius-md);\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e1;\\n  border-radius: var(--radius-md);\\n  -webkit-transition: var(--transition-fast);\\n  transition: var(--transition-fast);\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #94a3b8;\\n}\\n\\n\\n\\n.pagination-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: var(--space-4) 0;\\n  margin-bottom: var(--space-4);\\n}\\n.pagination-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 2px solid #e2e8f0;\\n  border-top: 2px solid #bdf2bd;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n  display: flex;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.3s ease-out;\\n  position: relative;\\n}\\n.message.user-message[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: var(--gradient-primary);\\n  color: #ffffff;\\n  max-width: 80%;\\n  border-radius: var(--radius-2xl) var(--radius-2xl) var(--radius-sm) var(--radius-2xl);\\n  box-shadow: var(--shadow-colored);\\n  transition: var(--transition-normal);\\n  position: relative;\\n  border: none;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: var(--shadow-glow);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.15);\\n  color: #059669;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.05);\\n  border-color: rgba(0, 0, 0, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  color: #047857;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #059669;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #047857;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: #059669;\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(0, 0, 0, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.message.bot-message[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  \\n\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: var(--color-gray-800);\\n  max-width: 80%;\\n  border-radius: var(--radius-2xl) var(--radius-2xl) var(--radius-2xl) var(--radius-sm);\\n  box-shadow: var(--shadow-lg);\\n  transition: var(--transition-normal);\\n  position: relative;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: var(--shadow-xl);\\n  border-color: var(--color-primary);\\n  \\n\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover   .message-news[_ngcontent-%COMP%] {\\n  margin-top: var(--space-3);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message.bot-message[_ngcontent-%COMP%] {\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-news[_ngcontent-%COMP%] {\\n  max-width: 80%;\\n  margin-left: 0;\\n  align-self: flex-start;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: var(--space-4) var(--space-5);\\n  word-wrap: break-word;\\n  position: relative;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-base);\\n  line-height: 1.6;\\n  margin: 0;\\n  font-weight: var(--font-weight-normal);\\n  word-break: break-word;\\n}\\n.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--space-3) 0;\\n}\\n.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: var(--space-4) 0 var(--space-2) 0;\\n  font-weight: var(--font-weight-semibold);\\n  line-height: 1.3;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.1em;\\n}\\n.message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-size: 1em;\\n}\\n.message-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   b[_ngcontent-%COMP%] {\\n  font-weight: var(--font-weight-semibold);\\n}\\n.message-text[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-style: italic;\\n}\\n.message-text[_ngcontent-%COMP%]   del[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   strike[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n}\\n.message-text[_ngcontent-%COMP%]   u[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: var(--space-3) 0;\\n  padding-left: var(--space-6);\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: var(--space-1) 0;\\n  line-height: 1.5;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  margin: var(--space-4) 0;\\n  padding: var(--space-3) var(--space-4);\\n  border-left: 4px solid #e5e7eb;\\n  background: rgba(0, 0, 0, 0.02);\\n  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;\\n  font-style: italic;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.08);\\n  padding: 2px 6px;\\n  border-radius: var(--radius-sm);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 0.9em;\\n  color: #e11d48;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: var(--radius-md);\\n  padding: var(--space-4);\\n  margin: var(--space-4) 0;\\n  overflow-x: auto;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: none;\\n  padding: 0;\\n  color: #334155;\\n  font-size: 0.875em;\\n}\\n.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #2563eb;\\n  text-decoration: underline;\\n  transition: var(--transition-fast);\\n}\\n.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #1d4ed8;\\n  text-decoration: none;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  margin: var(--space-4) 0;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: var(--space-2) var(--space-3);\\n  border: 1px solid #e2e8f0;\\n  text-align: left;\\n}\\n.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  font-weight: var(--font-weight-semibold);\\n}\\n.message-text[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  border-radius: var(--radius-sm);\\n  margin: var(--space-2) 0;\\n}\\n\\n.message-time[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-xs);\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-top: var(--space-2);\\n  text-align: right;\\n  opacity: 0.8;\\n  transition: var(--transition-fast);\\n  font-weight: var(--font-weight-normal);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: left;\\n  color: var(--color-gray-600);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: right;\\n  color: var(--color-gray-600);\\n}\\n\\n.message[_ngcontent-%COMP%]:hover   .message-time[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  padding: var(--space-6) var(--space-8) var(--space-8) var(--space-8);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  position: fixed;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 100%;\\n  max-width: 900px;\\n  z-index: 50;\\n  border-top: 1px solid rgba(255, 255, 255, 0.3);\\n  transition: var(--transition-fast);\\n}\\n.input-container.centered[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 100%;\\n  max-width: 600px;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  z-index: 50;\\n  padding: var(--space-8);\\n  bottom: auto;\\n  border-radius: var(--radius-2xl);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  box-shadow: var(--shadow-xl);\\n}\\n.input-container.bottom[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  border-top: 1px solid rgba(255, 255, 255, 0.3);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  width: 100%;\\n  max-width: 900px;\\n  z-index: 50;\\n}\\n\\n.input-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: var(--space-3);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: var(--radius-2xl);\\n  padding: var(--space-4) var(--space-5);\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: var(--transition-normal);\\n  position: relative;\\n  box-shadow: var(--shadow-md);\\n}\\n.input-wrapper[_ngcontent-%COMP%]:focus-within {\\n  border-color: var(--color-primary);\\n  background: rgba(255, 255, 255, 0.95);\\n  box-shadow: var(--shadow-glow);\\n  transform: translateY(-1px);\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  background: transparent;\\n  resize: none;\\n  font-size: var(--font-size-base);\\n  line-height: 1.5;\\n  padding: var(--space-2) 0;\\n  min-height: 20px;\\n  max-height: 100px;\\n  font-family: var(--font-family-primary);\\n  overflow-y: auto;\\n  transition: var(--transition-fast);\\n  color: var(--color-gray-800);\\n  font-weight: var(--font-weight-normal);\\n  \\n\\n}\\n.message-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--color-gray-500);\\n}\\n.message-input[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e1;\\n  border-radius: var(--radius-sm);\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #94a3b8;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  background: var(--gradient-primary);\\n  border: none;\\n  border-radius: var(--radius-xl);\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--color-white);\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: var(--transition-normal);\\n  box-shadow: var(--shadow-md);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.send-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  transition: left var(--transition-normal);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: var(--shadow-colored);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)::before {\\n  left: 100%;\\n}\\n.send-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(-1px) scale(1.02);\\n  box-shadow: var(--shadow-lg);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled {\\n  background: var(--color-gray-400);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: var(--shadow-sm);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled::before {\\n  display: none;\\n}\\n.send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  transition: var(--transition-fast);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)   svg[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-1);\\n  padding: var(--space-1) 0;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  background: #94a3b8;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n\\n\\n.auth-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  color: var(--color-gray-600);\\n  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 50%, #f0f4f8 100%);\\n}\\n.auth-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border: 4px solid var(--color-gray-200);\\n  border-top: 4px solid var(--color-accent);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: var(--space-4);\\n}\\n.auth-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-lg);\\n  color: var(--color-gray-600);\\n  margin: 0;\\n  font-weight: var(--font-weight-medium);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    opacity: 0.3;\\n    transform: scale(0.7) translateY(0);\\n  }\\n  30% {\\n    opacity: 1;\\n    transform: scale(1.3) translateY(-6px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(40px) scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .chat-container[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    height: 100vh;\\n    border-radius: 0;\\n    border: none;\\n  }\\n  .messages-container[_ngcontent-%COMP%] {\\n    padding: var(--space-5) var(--space-5) 140px var(--space-5);\\n  }\\n  .input-container[_ngcontent-%COMP%] {\\n    padding: var(--space-5) var(--space-5) var(--space-6) var(--space-5);\\n    max-width: 100%;\\n  }\\n  .input-container.centered[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    padding: var(--space-5);\\n    margin: 0 var(--space-4);\\n    width: calc(100% - var(--space-8));\\n  }\\n  .message[_ngcontent-%COMP%] {\\n    margin-bottom: var(--space-5);\\n  }\\n  .message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%], .message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n    max-width: 88%;\\n  }\\n  .input-wrapper[_ngcontent-%COMP%] {\\n    padding: var(--space-1) var(--space-2) var(--space-1) var(--space-4);\\n    gap: var(--space-2);\\n  }\\n  .send-button[_ngcontent-%COMP%] {\\n    width: 42px;\\n    height: 42px;\\n  }\\n  .send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n\\n\\n*[_ngcontent-%COMP%] {\\n  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast), transform var(--transition-fast), box-shadow var(--transition-fast);\\n}\\n\\n\\n\\n.send-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--color-primary);\\n  outline-offset: 3px;\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%]::selection {\\n  background: rgba(16, 185, 129, 0.2);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]::selection {\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n  30% {\\n    transform: translateY(-10px);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY2hhdC9jaGF0LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHNEQUFBO0FBRUE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EscUNBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHVDQUFBO0VBQ0EsZ0NBQUE7RUFDQSw0QkFBQTtFQUNBLDBDQUFBO0FBQUY7O0FBR0E7RUFDRSxPQUFBO0VBQ0EsZ0JBQUE7RUFDQSwyREFBQTtFQUNBLG1CQUFBO0VBQ0EsK0JBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUVBLDRCQUFBO0FBREY7QUFFRTtFQUNFLFVBQUE7QUFBSjtBQUdFO0VBQ0UsbUJBQUE7RUFDQSwrQkFBQTtBQURKO0FBSUU7RUFDRSxtQkFBQTtFQUNBLCtCQUFBO0VBQ0EsMENBQUE7RUFBQSxrQ0FBQTtBQUZKO0FBSUk7RUFDRSxtQkFBQTtBQUZOOztBQU9BLDBCQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSx5QkFBQTtFQUNBLDZCQUFBO0FBSkY7QUFNRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EseUJBQUE7RUFDQSw2QkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0NBQUE7QUFKSjs7QUFRQTtFQUNFLDZCQUFBO0VBQ0EsYUFBQTtFQUNBLGlDQUFBO0VBQ0Esa0JBQUE7QUFMRjtBQU9FO0VBQ0UseUJBQUE7QUFMSjtBQU9JO0VBQ0UsbUNBQUE7RUFDQSxjQUFBO0VBQ0EsY0FBQTtFQUNBLHFGQUFBO0VBQ0EsaUNBQUE7RUFDQSxvQ0FBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtBQUxOO0FBT007RUFDRSwyQkFBQTtFQUNBLDhCQUFBO0FBTFI7QUFZTTtFQUNFLCtCQUFBO0VBQ0EsY0FBQTtBQVZSO0FBY007RUFDRSwrQkFBQTtFQUNBLGdDQUFBO0FBWlI7QUFjUTtFQUNFLGNBQUE7QUFaVjtBQWlCTTtFQUNFLGNBQUE7QUFmUjtBQWdCUTtFQUNFLGNBQUE7QUFkVjtBQW1CTTtFQUNFLDBCQUFBO0VBQ0EsK0JBQUE7QUFqQlI7QUFxQk07RUFDRSxnQ0FBQTtBQW5CUjtBQXNCTTtFQUNFLCtCQUFBO0FBcEJSO0FBeUJFO0VBQ0UsMkJBQUE7RUFDQSxzQkFBQTtFQUNBLHVCQUFBO0VBMEJKLHlFQUFBO0FBaERBO0FBd0JJO0VBQ0UscUNBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0VBQ0EsNEJBQUE7RUFDQSxjQUFBO0VBQ0EscUZBQUE7RUFDQSw0QkFBQTtFQUNBLG9DQUFBO0VBQ0Esa0JBQUE7RUFDQSwwQ0FBQTtBQXRCTjtBQXdCTTtFQUNFLDJCQUFBO0VBQ0EsNEJBQUE7RUFDQSxrQ0FBQTtFQUVSLHVEQUFBO0FBdkJBO0FBd0JBO0VBQ0UsMEJBQUE7QUF0QkY7QUE2QkE7RUFDRSxzQkFBQTtFQUNBLHVCQUFBO0FBM0JGO0FBOEJBO0VBQ0UsY0FBQTtFQUNBLGNBQUE7RUFDQSxzQkFBQTtBQTVCRjs7QUFrQ0E7RUFDRSxzQ0FBQTtFQUNBLHFCQUFBO0VBQ0Esa0JBQUE7QUEvQkY7O0FBa0NBO0VBQ0UsZ0NBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7RUFDQSxzQ0FBQTtFQUNBLHNCQUFBO0FBL0JGO0FBa0NFO0VBQ0UsNEJBQUE7QUFoQ0o7QUFpQ0k7RUFDRSxnQkFBQTtBQS9CTjtBQW9DRTtFQUNFLHlDQUFBO0VBQ0Esd0NBQUE7RUFDQSxnQkFBQTtBQWxDSjtBQW9DSTtFQUNFLGFBQUE7QUFsQ047QUFzQ0U7RUFBSyxnQkFBQTtBQW5DUDtBQW9DRTtFQUFLLGdCQUFBO0FBakNQO0FBa0NFO0VBQUssZ0JBQUE7QUEvQlA7QUFnQ0U7RUFBSyxnQkFBQTtBQTdCUDtBQThCRTtFQUFTLGNBQUE7QUEzQlg7QUE4QkU7RUFDRSx3Q0FBQTtBQTVCSjtBQStCRTtFQUNFLGtCQUFBO0FBN0JKO0FBaUNFO0VBQ0UsNkJBQUE7QUEvQko7QUFrQ0U7RUFDRSwwQkFBQTtBQWhDSjtBQW9DRTtFQUNFLHdCQUFBO0VBQ0EsNEJBQUE7QUFsQ0o7QUFvQ0k7RUFDRSxhQUFBO0FBbENOO0FBcUNJO0VBQ0UsZ0JBQUE7QUFuQ047QUF1Q0U7RUFDRSx3QkFBQTtFQUNBLGdCQUFBO0FBckNKO0FBeUNFO0VBQ0Usd0JBQUE7RUFDQSxzQ0FBQTtFQUNBLDhCQUFBO0VBQ0EsK0JBQUE7RUFDQSxvREFBQTtFQUNBLGtCQUFBO0FBdkNKO0FBeUNJO0VBQ0UsYUFBQTtBQXZDTjtBQTBDSTtFQUNFLGdCQUFBO0FBeENOO0FBNkNFO0VBQ0UsK0JBQUE7RUFDQSxnQkFBQTtFQUNBLCtCQUFBO0VBQ0Esd0RBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUEzQ0o7QUErQ0U7RUFDRSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0EsK0JBQUE7RUFDQSx1QkFBQTtFQUNBLHdCQUFBO0VBQ0EsZ0JBQUE7QUE3Q0o7QUErQ0k7RUFDRSxhQUFBO0FBN0NOO0FBZ0RJO0VBQ0UsZ0JBQUE7QUE5Q047QUFpREk7RUFDRSxnQkFBQTtFQUNBLFVBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUEvQ047QUFvREU7RUFDRSxjQUFBO0VBQ0EsMEJBQUE7RUFDQSxrQ0FBQTtBQWxESjtBQW9ESTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtBQWxETjtBQXVERTtFQUNFLFdBQUE7RUFDQSx5QkFBQTtFQUNBLHdCQUFBO0FBckRKO0FBdURJO0VBQ0UsYUFBQTtBQXJETjtBQXdESTtFQUNFLGdCQUFBO0FBdEROO0FBMERFO0VBQ0Usc0NBQUE7RUFDQSx5QkFBQTtFQUNBLGdCQUFBO0FBeERKO0FBMkRFO0VBQ0UsbUJBQUE7RUFDQSx3Q0FBQTtBQXpESjtBQTZERTtFQUNFLGVBQUE7RUFDQSxZQUFBO0VBQ0EsK0JBQUE7RUFDQSx3QkFBQTtBQTNESjs7QUErREE7RUFDRSw4QkFBQTtFQUNBLCtCQUFBO0VBQ0EsMEJBQUE7RUFDQSxpQkFBQTtFQUNBLFlBQUE7RUFDQSxrQ0FBQTtFQUNBLHNDQUFBO0FBNURGOztBQStEQTtFQUNFLGdCQUFBO0VBQ0EsNEJBQUE7QUE1REY7O0FBK0RBO0VBQ0UsaUJBQUE7RUFDQSw0QkFBQTtBQTVERjs7QUE4REE7RUFDRSxVQUFBO0FBM0RGOztBQThEQTtFQUNFLG9FQUFBO0VBQ0EscUNBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0VBQ0EsZUFBQTtFQUNBLFNBQUE7RUFDQSxTQUFBO0VBQ0EsMkJBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0VBQ0EsOENBQUE7RUFDQSxrQ0FBQTtBQTNERjtBQTZERTtFQUNFLGVBQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtFQUNBLGdDQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0VBQ0EscUNBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0VBQ0EsV0FBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLGdDQUFBO0VBQ0EsMENBQUE7RUFDQSw0QkFBQTtBQTNESjtBQThERTtFQUNFLGVBQUE7RUFDQSxTQUFBO0VBQ0EsU0FBQTtFQUNBLDJCQUFBO0VBQ0EsOENBQUE7RUFDQSxxQ0FBQTtFQUNBLG1DQUFBO1VBQUEsMkJBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0FBNURKOztBQWdFQTtFQUNFLGFBQUE7RUFDQSxxQkFBQTtFQUNBLG1CQUFBO0VBQ0EsMENBQUE7RUFDQSxnQ0FBQTtFQUNBLHNDQUFBO0VBQ0Esb0NBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0VBQ0Esb0NBQUE7RUFDQSxrQkFBQTtFQUNBLDRCQUFBO0FBN0RGO0FBK0RFO0VBQ0Usa0NBQUE7RUFDQSxxQ0FBQTtFQUNBLDhCQUFBO0VBQ0EsMkJBQUE7QUE3REo7O0FBaUVBO0VBQ0UsT0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxZQUFBO0VBQ0EsZ0NBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLHVDQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQ0FBQTtFQUNBLDRCQUFBO0VBQ0Esc0NBQUE7RUFXQSxpQ0FBQTtBQXhFRjtBQStERTtFQUNFLDRCQUFBO0FBN0RKO0FBZ0VFO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0FBOURKO0FBa0VFO0VBQ0UsVUFBQTtBQWhFSjtBQW1FRTtFQUNFLHVCQUFBO0FBakVKO0FBb0VFO0VBQ0UsbUJBQUE7RUFDQSwrQkFBQTtBQWxFSjtBQW9FSTtFQUNFLG1CQUFBO0FBbEVOOztBQXVFQTtFQUNFLG1DQUFBO0VBQ0EsWUFBQTtFQUNBLCtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSxvQ0FBQTtFQUNBLDRCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQXBFRjtBQXNFRTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxXQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxzRkFBQTtFQUNBLHlDQUFBO0FBcEVKO0FBdUVFO0VBQ0UsdUNBQUE7RUFDQSxpQ0FBQTtBQXJFSjtBQXVFSTtFQUNFLFVBQUE7QUFyRU47QUF5RUU7RUFDRSx1Q0FBQTtFQUNBLDRCQUFBO0FBdkVKO0FBMEVFO0VBQ0UsaUNBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSw0QkFBQTtBQXhFSjtBQTBFSTtFQUNFLGFBQUE7QUF4RU47QUE0RUU7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtDQUFBO0FBMUVKO0FBNkVFO0VBQ0UscUJBQUE7QUEzRUo7O0FBK0VBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtBQTVFRjtBQThFRTtFQUNFLFVBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLDJDQUFBO0FBNUVKO0FBOEVJO0VBQ0UsbUJBQUE7QUE1RU47QUErRUk7RUFDRSxxQkFBQTtBQTdFTjtBQWdGSTtFQUNFLHFCQUFBO0FBOUVOOztBQW1GQSwrQkFBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSw0QkFBQTtFQUNBLGtHQUFBO0FBaEZGO0FBa0ZFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSx1Q0FBQTtFQUNBLHlDQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQ0FBQTtFQUNBLDZCQUFBO0FBaEZKO0FBbUZFO0VBQ0UsOEJBQUE7RUFDQSw0QkFBQTtFQUNBLFNBQUE7RUFDQSxzQ0FBQTtBQWpGSjs7QUFxRkEsK0JBQUE7QUFDQTtFQUNFO0lBQ0UsWUFBQTtJQUNBLG1DQUFBO0VBbEZGO0VBb0ZBO0lBQ0UsVUFBQTtJQUNBLHNDQUFBO0VBbEZGO0FBQ0Y7QUFxRkE7RUFDRTtJQUNFLFVBQUE7SUFDQSxzQ0FBQTtFQW5GRjtFQXFGQTtJQUNFLFVBQUE7SUFDQSxpQ0FBQTtFQW5GRjtBQUNGO0FBc0ZBO0VBQ0U7SUFBSyx1QkFBQTtFQW5GTDtFQW9GQTtJQUFPLHlCQUFBO0VBakZQO0FBQ0Y7QUFtRkEsc0NBQUE7QUFDQTtFQUNFO0lBQ0UsZUFBQTtJQUNBLGFBQUE7SUFDQSxnQkFBQTtJQUNBLFlBQUE7RUFqRkY7RUFvRkE7SUFDRSwyREFBQTtFQWxGRjtFQXFGQTtJQUNFLG9FQUFBO0lBQ0EsZUFBQTtFQW5GRjtFQXFGRTtJQUNFLGVBQUE7SUFDQSx1QkFBQTtJQUNBLHdCQUFBO0lBQ0Esa0NBQUE7RUFuRko7RUF1RkE7SUFDRSw2QkFBQTtFQXJGRjtFQXVGRTtJQUVFLGNBQUE7RUF0Rko7RUEwRkE7SUFDRSxvRUFBQTtJQUNBLG1CQUFBO0VBeEZGO0VBMkZBO0lBQ0UsV0FBQTtJQUNBLFlBQUE7RUF6RkY7RUEyRkU7SUFDRSxXQUFBO0lBQ0EsWUFBQTtFQXpGSjtBQUNGO0FBNkZBLDhDQUFBO0FBQ0E7RUFDRSwyTEFBQTtBQTNGRjs7QUFrR0EsZ0RBQUE7QUFDQTtFQUNFLHVDQUFBO0VBQ0EsbUJBQUE7QUEvRkY7O0FBa0dBO0VBQ0UsZ0JBQUE7QUEvRkY7O0FBa0dBLG1DQUFBO0FBRUU7RUFDRSxtQ0FBQTtBQWhHSjs7QUFxR0U7RUFDRSxvQ0FBQTtBQWxHSjs7QUFzR0EscUJBQUE7QUFDQTtFQUNFO0lBQ0UsVUFBQTtJQUNBLDJCQUFBO0VBbkdGO0VBcUdBO0lBQ0UsVUFBQTtJQUNBLHdCQUFBO0VBbkdGO0FBQ0Y7QUFzR0E7RUFDRTtJQUNFLHdCQUFBO0lBQ0EsWUFBQTtFQXBHRjtFQXNHQTtJQUNFLDRCQUFBO0lBQ0EsVUFBQTtFQXBHRjtBQUNGO0FBdUdBO0VBQ0U7SUFDRSx1QkFBQTtFQXJHRjtFQXVHQTtJQUNFLHlCQUFBO0VBckdGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBjaGF0LmNvbXBvbmVudC5zY3NzIC0gQ2xlYW4gTW9kZXJuIENoYXQgSW50ZXJmYWNlICovXG5cbi5jaGF0LWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGhlaWdodDogMTAwdmg7XG4gIG1heC13aWR0aDogOTAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpO1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgZm9udC1mYW1pbHk6IHZhcigtLWZvbnQtZmFtaWx5LXByaW1hcnkpO1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMtMnhsKTtcbiAgYm94LXNoYWRvdzogdmFyKC0tc2hhZG93LXhsKTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xufVxuXG4ubWVzc2FnZXMtY29udGFpbmVyIHtcbiAgZmxleDogMTtcbiAgb3ZlcmZsb3cteTogYXV0bztcbiAgcGFkZGluZzogdmFyKC0tc3BhY2UtNikgdmFyKC0tc3BhY2UtOCkgMTIwcHggdmFyKC0tc3BhY2UtOCk7XG4gIGJhY2tncm91bmQ6ICNmOWZhZmI7XG4gIG1heC1oZWlnaHQ6IGNhbGMoMTAwdmggLSAxNjBweCk7XG4gIHNjcm9sbC1iZWhhdmlvcjogc21vb3RoO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHotaW5kZXg6IDEwO1xuXG4gIC8qIENsZWFuIHNjcm9sbGJhciBzdHlsaW5nICovXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcbiAgICB3aWR0aDogNnB4O1xuICB9XG5cbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xuICAgIGJhY2tncm91bmQ6ICNmMWY1Zjk7XG4gICAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLW1kKTtcbiAgfVxuXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgICBiYWNrZ3JvdW5kOiAjY2JkNWUxO1xuICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cy1tZCk7XG4gICAgdHJhbnNpdGlvbjogdmFyKC0tdHJhbnNpdGlvbi1mYXN0KTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogIzk0YTNiODtcbiAgICB9XG4gIH1cbn1cblxuLyogQ2xlYW4gbG9hZGluZyBzcGlubmVyICovXG4ucGFnaW5hdGlvbi1sb2FkaW5nIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHBhZGRpbmc6IHZhcigtLXNwYWNlLTQpIDA7XG4gIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNlLTQpO1xuXG4gIC5sb2FkaW5nLXNwaW5uZXIge1xuICAgIHdpZHRoOiAyNHB4O1xuICAgIGhlaWdodDogMjRweDtcbiAgICBib3JkZXI6IDJweCBzb2xpZCAjZTJlOGYwO1xuICAgIGJvcmRlci10b3A6IDJweCBzb2xpZCAjYmRmMmJkO1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xuICB9XG59XG5cbi5tZXNzYWdlIHtcbiAgbWFyZ2luLWJvdHRvbTogdmFyKC0tc3BhY2UtNCk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFuaW1hdGlvbjogZmFkZUluVXAgMC4zcyBlYXNlLW91dDtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuXG4gICYudXNlci1tZXNzYWdlIHtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xuXG4gICAgLm1lc3NhZ2UtY29udGVudCB7XG4gICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1ncmFkaWVudC1wcmltYXJ5KTtcbiAgICAgIGNvbG9yOiAjZmZmZmZmO1xuICAgICAgbWF4LXdpZHRoOiA4MCU7XG4gICAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMtMnhsKSB2YXIoLS1yYWRpdXMtMnhsKSB2YXIoLS1yYWRpdXMtc20pIHZhcigtLXJhZGl1cy0yeGwpO1xuICAgICAgYm94LXNoYWRvdzogdmFyKC0tc2hhZG93LWNvbG9yZWQpO1xuICAgICAgdHJhbnNpdGlvbjogdmFyKC0tdHJhbnNpdGlvbi1ub3JtYWwpO1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgYm9yZGVyOiBub25lO1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICAgICAgICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3ctZ2xvdyk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gU3BlY2lhbCBzdHlsaW5nIGZvciBmb3JtYXR0ZWQgY29udGVudCBpbiB1c2VyIG1lc3NhZ2VzXG4gICAgLm1lc3NhZ2UtdGV4dCB7XG4gICAgICAvLyBJbmxpbmUgY29kZSBpbiB1c2VyIG1lc3NhZ2VzXG4gICAgICBjb2RlIHtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjE1KTtcbiAgICAgICAgY29sb3I6ICMwNTk2Njk7XG4gICAgICB9XG5cbiAgICAgIC8vIENvZGUgYmxvY2tzIGluIHVzZXIgbWVzc2FnZXNcbiAgICAgIHByZSB7XG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4wNSk7XG4gICAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjEpO1xuXG4gICAgICAgIGNvZGUge1xuICAgICAgICAgIGNvbG9yOiAjMDQ3ODU3O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIExpbmtzIGluIHVzZXIgbWVzc2FnZXNcbiAgICAgIGEge1xuICAgICAgICBjb2xvcjogIzA1OTY2OTtcbiAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgY29sb3I6ICMwNDc4NTc7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gQmxvY2txdW90ZXMgaW4gdXNlciBtZXNzYWdlc1xuICAgICAgYmxvY2txdW90ZSB7XG4gICAgICAgIGJvcmRlci1sZWZ0LWNvbG9yOiAjMDU5NjY5O1xuICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMDUpO1xuICAgICAgfVxuXG4gICAgICAvLyBUYWJsZXMgaW4gdXNlciBtZXNzYWdlc1xuICAgICAgdGgsIHRkIHtcbiAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gICAgICB9XG5cbiAgICAgIHRoIHtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjA1KTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAmLmJvdC1tZXNzYWdlIHtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcblxuICAgIC5tZXNzYWdlLWNvbnRlbnQge1xuICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTtcbiAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcbiAgICAgIGNvbG9yOiB2YXIoLS1jb2xvci1ncmF5LTgwMCk7XG4gICAgICBtYXgtd2lkdGg6IDgwJTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cy0yeGwpIHZhcigtLXJhZGl1cy0yeGwpIHZhcigtLXJhZGl1cy0yeGwpIHZhcigtLXJhZGl1cy1zbSk7XG4gICAgICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3ctbGcpO1xuICAgICAgdHJhbnNpdGlvbjogdmFyKC0tdHJhbnNpdGlvbi1ub3JtYWwpO1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICAgICAgICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3cteGwpO1xuICAgICAgICBib3JkZXItY29sb3I6IHZhcigtLWNvbG9yLXByaW1hcnkpO1xuXG4vKiBOZXdzIGNvbnRhaW5lciBzcGFjaW5nIHRvIHNpdCBjbGVhcmx5IHVuZGVyIGJ1YmJsZSAqL1xuLm1lc3NhZ2UtbmV3cyB7XG4gIG1hcmdpbi10b3A6IHZhcigtLXNwYWNlLTMpO1xufVxuXG4gICAgICB9XG4gICAgfVxuXG4vKiBFbnN1cmUgbmV3cyBibG9jayBzaXRzIGNsZWFybHkgVU5ERVIgdGhlIGJvdCBidWJibGUsIGFsaWduZWQgd2l0aCBpdCAqL1xuLm1lc3NhZ2UuYm90LW1lc3NhZ2Uge1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbn1cblxuLm1lc3NhZ2UtbmV3cyB7XG4gIG1heC13aWR0aDogODAlO1xuICBtYXJnaW4tbGVmdDogMDtcbiAgYWxpZ24tc2VsZjogZmxleC1zdGFydDtcbn1cblxuICB9XG59XG5cbi5tZXNzYWdlLWNvbnRlbnQge1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjZS00KSB2YXIoLS1zcGFjZS01KTtcbiAgd29yZC13cmFwOiBicmVhay13b3JkO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG59XG5cbi5tZXNzYWdlLXRleHQge1xuICBmb250LXNpemU6IHZhcigtLWZvbnQtc2l6ZS1iYXNlKTtcbiAgbGluZS1oZWlnaHQ6IDEuNjtcbiAgbWFyZ2luOiAwO1xuICBmb250LXdlaWdodDogdmFyKC0tZm9udC13ZWlnaHQtbm9ybWFsKTtcbiAgd29yZC1icmVhazogYnJlYWstd29yZDtcblxuICAvLyBQYXJhZ3JhcGggc3R5bGluZ1xuICBwIHtcbiAgICBtYXJnaW46IDAgMCB2YXIoLS1zcGFjZS0zKSAwO1xuICAgICY6bGFzdC1jaGlsZCB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgIH1cbiAgfVxuXG4gIC8vIEhlYWRpbmdzXG4gIGgxLCBoMiwgaDMsIGg0LCBoNSwgaDYge1xuICAgIG1hcmdpbjogdmFyKC0tc3BhY2UtNCkgMCB2YXIoLS1zcGFjZS0yKSAwO1xuICAgIGZvbnQtd2VpZ2h0OiB2YXIoLS1mb250LXdlaWdodC1zZW1pYm9sZCk7XG4gICAgbGluZS1oZWlnaHQ6IDEuMztcblxuICAgICY6Zmlyc3QtY2hpbGQge1xuICAgICAgbWFyZ2luLXRvcDogMDtcbiAgICB9XG4gIH1cblxuICBoMSB7IGZvbnQtc2l6ZTogMS41ZW07IH1cbiAgaDIgeyBmb250LXNpemU6IDEuM2VtOyB9XG4gIGgzIHsgZm9udC1zaXplOiAxLjJlbTsgfVxuICBoNCB7IGZvbnQtc2l6ZTogMS4xZW07IH1cbiAgaDUsIGg2IHsgZm9udC1zaXplOiAxZW07IH1cblxuICAvLyBCb2xkIGFuZCBpdGFsaWMgdGV4dFxuICBzdHJvbmcsIGIge1xuICAgIGZvbnQtd2VpZ2h0OiB2YXIoLS1mb250LXdlaWdodC1zZW1pYm9sZCk7XG4gIH1cblxuICBlbSwgaSB7XG4gICAgZm9udC1zdHlsZTogaXRhbGljO1xuICB9XG5cbiAgLy8gU3RyaWtlLXRocm91Z2ggYW5kIHVuZGVybGluZVxuICBkZWwsIHN0cmlrZSB7XG4gICAgdGV4dC1kZWNvcmF0aW9uOiBsaW5lLXRocm91Z2g7XG4gIH1cblxuICB1IHtcbiAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbiAgfVxuXG4gIC8vIExpc3RzXG4gIHVsLCBvbCB7XG4gICAgbWFyZ2luOiB2YXIoLS1zcGFjZS0zKSAwO1xuICAgIHBhZGRpbmctbGVmdDogdmFyKC0tc3BhY2UtNik7XG5cbiAgICAmOmZpcnN0LWNoaWxkIHtcbiAgICAgIG1hcmdpbi10b3A6IDA7XG4gICAgfVxuXG4gICAgJjpsYXN0LWNoaWxkIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDA7XG4gICAgfVxuICB9XG5cbiAgbGkge1xuICAgIG1hcmdpbjogdmFyKC0tc3BhY2UtMSkgMDtcbiAgICBsaW5lLWhlaWdodDogMS41O1xuICB9XG5cbiAgLy8gQmxvY2txdW90ZXNcbiAgYmxvY2txdW90ZSB7XG4gICAgbWFyZ2luOiB2YXIoLS1zcGFjZS00KSAwO1xuICAgIHBhZGRpbmc6IHZhcigtLXNwYWNlLTMpIHZhcigtLXNwYWNlLTQpO1xuICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgI2U1ZTdlYjtcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMDIpO1xuICAgIGJvcmRlci1yYWRpdXM6IDAgdmFyKC0tcmFkaXVzLXNtKSB2YXIoLS1yYWRpdXMtc20pIDA7XG4gICAgZm9udC1zdHlsZTogaXRhbGljO1xuXG4gICAgJjpmaXJzdC1jaGlsZCB7XG4gICAgICBtYXJnaW4tdG9wOiAwO1xuICAgIH1cblxuICAgICY6bGFzdC1jaGlsZCB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgIH1cbiAgfVxuXG4gIC8vIElubGluZSBjb2RlXG4gIGNvZGUge1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4wOCk7XG4gICAgcGFkZGluZzogMnB4IDZweDtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMtc20pO1xuICAgIGZvbnQtZmFtaWx5OiAnTW9uYWNvJywgJ01lbmxvJywgJ1VidW50dSBNb25vJywgbW9ub3NwYWNlO1xuICAgIGZvbnQtc2l6ZTogMC45ZW07XG4gICAgY29sb3I6ICNlMTFkNDg7XG4gIH1cblxuICAvLyBDb2RlIGJsb2Nrc1xuICBwcmUge1xuICAgIGJhY2tncm91bmQ6ICNmOGZhZmM7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2UyZThmMDtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMtbWQpO1xuICAgIHBhZGRpbmc6IHZhcigtLXNwYWNlLTQpO1xuICAgIG1hcmdpbjogdmFyKC0tc3BhY2UtNCkgMDtcbiAgICBvdmVyZmxvdy14OiBhdXRvO1xuXG4gICAgJjpmaXJzdC1jaGlsZCB7XG4gICAgICBtYXJnaW4tdG9wOiAwO1xuICAgIH1cblxuICAgICY6bGFzdC1jaGlsZCB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgIH1cblxuICAgIGNvZGUge1xuICAgICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICAgIHBhZGRpbmc6IDA7XG4gICAgICBjb2xvcjogIzMzNDE1NTtcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVlbTtcbiAgICB9XG4gIH1cblxuICAvLyBMaW5rc1xuICBhIHtcbiAgICBjb2xvcjogIzI1NjNlYjtcbiAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbiAgICB0cmFuc2l0aW9uOiB2YXIoLS10cmFuc2l0aW9uLWZhc3QpO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBjb2xvcjogIzFkNGVkODtcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICB9XG4gIH1cblxuICAvLyBUYWJsZXNcbiAgdGFibGUge1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7XG4gICAgbWFyZ2luOiB2YXIoLS1zcGFjZS00KSAwO1xuXG4gICAgJjpmaXJzdC1jaGlsZCB7XG4gICAgICBtYXJnaW4tdG9wOiAwO1xuICAgIH1cblxuICAgICY6bGFzdC1jaGlsZCB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgIH1cbiAgfVxuXG4gIHRoLCB0ZCB7XG4gICAgcGFkZGluZzogdmFyKC0tc3BhY2UtMikgdmFyKC0tc3BhY2UtMyk7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2UyZThmMDtcbiAgICB0ZXh0LWFsaWduOiBsZWZ0O1xuICB9XG5cbiAgdGgge1xuICAgIGJhY2tncm91bmQ6ICNmOGZhZmM7XG4gICAgZm9udC13ZWlnaHQ6IHZhcigtLWZvbnQtd2VpZ2h0LXNlbWlib2xkKTtcbiAgfVxuXG4gIC8vIEltYWdlc1xuICBpbWcge1xuICAgIG1heC13aWR0aDogMTAwJTtcbiAgICBoZWlnaHQ6IGF1dG87XG4gICAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLXNtKTtcbiAgICBtYXJnaW46IHZhcigtLXNwYWNlLTIpIDA7XG4gIH1cbn1cblxuLm1lc3NhZ2UtdGltZSB7XG4gIGZvbnQtc2l6ZTogdmFyKC0tZm9udC1zaXplLXhzKTtcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcbiAgbWFyZ2luLXRvcDogdmFyKC0tc3BhY2UtMik7XG4gIHRleHQtYWxpZ246IHJpZ2h0O1xuICBvcGFjaXR5OiAwLjg7XG4gIHRyYW5zaXRpb246IHZhcigtLXRyYW5zaXRpb24tZmFzdCk7XG4gIGZvbnQtd2VpZ2h0OiB2YXIoLS1mb250LXdlaWdodC1ub3JtYWwpO1xufVxuXG4uYm90LW1lc3NhZ2UgLm1lc3NhZ2UtdGltZSB7XG4gIHRleHQtYWxpZ246IGxlZnQ7XG4gIGNvbG9yOiB2YXIoLS1jb2xvci1ncmF5LTYwMCk7XG59XG5cbi51c2VyLW1lc3NhZ2UgLm1lc3NhZ2UtdGltZSB7XG4gIHRleHQtYWxpZ246IHJpZ2h0O1xuICBjb2xvcjogdmFyKC0tY29sb3ItZ3JheS02MDApO1xufVxuLm1lc3NhZ2U6aG92ZXIgLm1lc3NhZ2UtdGltZSB7XG4gIG9wYWNpdHk6IDE7XG59XG5cbi5pbnB1dC1jb250YWluZXIge1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjZS02KSB2YXIoLS1zcGFjZS04KSB2YXIoLS1zcGFjZS04KSB2YXIoLS1zcGFjZS04KTtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xuICBwb3NpdGlvbjogZml4ZWQ7XG4gIGJvdHRvbTogMDtcbiAgbGVmdDogNTAlO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSk7XG4gIHdpZHRoOiAxMDAlO1xuICBtYXgtd2lkdGg6IDkwMHB4O1xuICB6LWluZGV4OiA1MDtcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKTtcbiAgdHJhbnNpdGlvbjogdmFyKC0tdHJhbnNpdGlvbi1mYXN0KTtcblxuICAmLmNlbnRlcmVkIHtcbiAgICBwb3NpdGlvbjogZml4ZWQ7XG4gICAgdG9wOiA1MCU7XG4gICAgbGVmdDogNTAlO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpO1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIG1heC13aWR0aDogNjAwcHg7XG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTtcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XG4gICAgei1pbmRleDogNTA7XG4gICAgcGFkZGluZzogdmFyKC0tc3BhY2UtOCk7XG4gICAgYm90dG9tOiBhdXRvO1xuICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cy0yeGwpO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKTtcbiAgICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3cteGwpO1xuICB9XG5cbiAgJi5ib3R0b20ge1xuICAgIHBvc2l0aW9uOiBmaXhlZDtcbiAgICBib3R0b206IDA7XG4gICAgbGVmdDogNTAlO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKTtcbiAgICBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45NSk7XG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIG1heC13aWR0aDogOTAwcHg7XG4gICAgei1pbmRleDogNTA7XG4gIH1cbn1cblxuLmlucHV0LXdyYXBwZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogZmxleC1lbmQ7XG4gIGdhcDogdmFyKC0tc3BhY2UtMyk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKTtcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLTJ4bCk7XG4gIHBhZGRpbmc6IHZhcigtLXNwYWNlLTQpIHZhcigtLXNwYWNlLTUpO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7XG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcbiAgdHJhbnNpdGlvbjogdmFyKC0tdHJhbnNpdGlvbi1ub3JtYWwpO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy1tZCk7XG5cbiAgJjpmb2N1cy13aXRoaW4ge1xuICAgIGJvcmRlci1jb2xvcjogdmFyKC0tY29sb3ItcHJpbWFyeSk7XG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTtcbiAgICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3ctZ2xvdyk7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICB9XG59XG5cbi5tZXNzYWdlLWlucHV0IHtcbiAgZmxleDogMTtcbiAgYm9yZGVyOiBub25lO1xuICBvdXRsaW5lOiBub25lO1xuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgcmVzaXplOiBub25lO1xuICBmb250LXNpemU6IHZhcigtLWZvbnQtc2l6ZS1iYXNlKTtcbiAgbGluZS1oZWlnaHQ6IDEuNTtcbiAgcGFkZGluZzogdmFyKC0tc3BhY2UtMikgMDtcbiAgbWluLWhlaWdodDogMjBweDtcbiAgbWF4LWhlaWdodDogMTAwcHg7XG4gIGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LWZhbWlseS1wcmltYXJ5KTtcbiAgb3ZlcmZsb3cteTogYXV0bztcbiAgdHJhbnNpdGlvbjogdmFyKC0tdHJhbnNpdGlvbi1mYXN0KTtcbiAgY29sb3I6IHZhcigtLWNvbG9yLWdyYXktODAwKTtcbiAgZm9udC13ZWlnaHQ6IHZhcigtLWZvbnQtd2VpZ2h0LW5vcm1hbCk7XG5cbiAgJjo6cGxhY2Vob2xkZXIge1xuICAgIGNvbG9yOiB2YXIoLS1jb2xvci1ncmF5LTUwMCk7XG4gIH1cblxuICAmOmRpc2FibGVkIHtcbiAgICBvcGFjaXR5OiAwLjU7XG4gICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcbiAgfVxuXG4gIC8qIENsZWFuIHNjcm9sbGJhciBmb3IgdGV4dGFyZWEgKi9cbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXIge1xuICAgIHdpZHRoOiA0cHg7XG4gIH1cblxuICAmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XG4gICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gIH1cblxuICAmOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XG4gICAgYmFja2dyb3VuZDogI2NiZDVlMTtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMtc20pO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiAjOTRhM2I4O1xuICAgIH1cbiAgfVxufVxuXG4uc2VuZC1idXR0b24ge1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1ncmFkaWVudC1wcmltYXJ5KTtcbiAgYm9yZGVyOiBub25lO1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMteGwpO1xuICB3aWR0aDogNDRweDtcbiAgaGVpZ2h0OiA0NHB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgY29sb3I6IHZhcigtLWNvbG9yLXdoaXRlKTtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICBmbGV4LXNocmluazogMDtcbiAgdHJhbnNpdGlvbjogdmFyKC0tdHJhbnNpdGlvbi1ub3JtYWwpO1xuICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3ctbWQpO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG5cbiAgJjo6YmVmb3JlIHtcbiAgICBjb250ZW50OiAnJztcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgdG9wOiAwO1xuICAgIGxlZnQ6IC0xMDAlO1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIGhlaWdodDogMTAwJTtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHRyYW5zcGFyZW50LCByZ2JhKDI1NSwyNTUsMjU1LDAuMyksIHRyYW5zcGFyZW50KTtcbiAgICB0cmFuc2l0aW9uOiBsZWZ0IHZhcigtLXRyYW5zaXRpb24tbm9ybWFsKTtcbiAgfVxuXG4gICY6aG92ZXI6bm90KDpkaXNhYmxlZCkge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KSBzY2FsZSgxLjA1KTtcbiAgICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3ctY29sb3JlZCk7XG5cbiAgICAmOjpiZWZvcmUge1xuICAgICAgbGVmdDogMTAwJTtcbiAgICB9XG4gIH1cblxuICAmOmFjdGl2ZTpub3QoOmRpc2FibGVkKSB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpIHNjYWxlKDEuMDIpO1xuICAgIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy1sZyk7XG4gIH1cblxuICAmOmRpc2FibGVkIHtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1jb2xvci1ncmF5LTQwMCk7XG4gICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcbiAgICB0cmFuc2Zvcm06IG5vbmU7XG4gICAgYm94LXNoYWRvdzogdmFyKC0tc2hhZG93LXNtKTtcblxuICAgICY6OmJlZm9yZSB7XG4gICAgICBkaXNwbGF5OiBub25lO1xuICAgIH1cbiAgfVxuXG4gIHN2ZyB7XG4gICAgd2lkdGg6IDIwcHg7XG4gICAgaGVpZ2h0OiAyMHB4O1xuICAgIHRyYW5zaXRpb246IHZhcigtLXRyYW5zaXRpb24tZmFzdCk7XG4gIH1cblxuICAmOmhvdmVyOm5vdCg6ZGlzYWJsZWQpIHN2ZyB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xuICB9XG59XG5cbi50eXBpbmctaW5kaWNhdG9yIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiB2YXIoLS1zcGFjZS0xKTtcbiAgcGFkZGluZzogdmFyKC0tc3BhY2UtMSkgMDtcblxuICBzcGFuIHtcbiAgICB3aWR0aDogOHB4O1xuICAgIGhlaWdodDogOHB4O1xuICAgIGJhY2tncm91bmQ6ICM5NGEzYjg7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIGFuaW1hdGlvbjogdHlwaW5nIDEuNHMgaW5maW5pdGUgZWFzZS1pbi1vdXQ7XG5cbiAgICAmOm50aC1jaGlsZCgxKSB7XG4gICAgICBhbmltYXRpb24tZGVsYXk6IDBzO1xuICAgIH1cblxuICAgICY6bnRoLWNoaWxkKDIpIHtcbiAgICAgIGFuaW1hdGlvbi1kZWxheTogMC4ycztcbiAgICB9XG5cbiAgICAmOm50aC1jaGlsZCgzKSB7XG4gICAgICBhbmltYXRpb24tZGVsYXk6IDAuNHM7XG4gICAgfVxuICB9XG59XG5cbi8qIE1vZGVybiBhdXRoIGxvYWRpbmcgc3R5bGVzICovXG4uYXV0aC1sb2FkaW5nIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGhlaWdodDogMTAwdmg7XG4gIGNvbG9yOiB2YXIoLS1jb2xvci1ncmF5LTYwMCk7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHZhcigtLWNvbG9yLWdyYXktNTApIDAlLCB2YXIoLS1jb2xvci13aGl0ZSkgNTAlLCAjZjBmNGY4IDEwMCUpO1xuXG4gIC5sb2FkaW5nLXNwaW5uZXIge1xuICAgIHdpZHRoOiA0OHB4O1xuICAgIGhlaWdodDogNDhweDtcbiAgICBib3JkZXI6IDRweCBzb2xpZCB2YXIoLS1jb2xvci1ncmF5LTIwMCk7XG4gICAgYm9yZGVyLXRvcDogNHB4IHNvbGlkIHZhcigtLWNvbG9yLWFjY2VudCk7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIGFuaW1hdGlvbjogc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7XG4gICAgbWFyZ2luLWJvdHRvbTogdmFyKC0tc3BhY2UtNCk7XG4gIH1cblxuICBwIHtcbiAgICBmb250LXNpemU6IHZhcigtLWZvbnQtc2l6ZS1sZyk7XG4gICAgY29sb3I6IHZhcigtLWNvbG9yLWdyYXktNjAwKTtcbiAgICBtYXJnaW46IDA7XG4gICAgZm9udC13ZWlnaHQ6IHZhcigtLWZvbnQtd2VpZ2h0LW1lZGl1bSk7XG4gIH1cbn1cblxuLyogTW9kZXJuIGVuaGFuY2VkIGFuaW1hdGlvbnMgKi9cbkBrZXlmcmFtZXMgdHlwaW5nIHtcbiAgMCUsIDYwJSwgMTAwJSB7XG4gICAgb3BhY2l0eTogMC4zO1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMC43KSB0cmFuc2xhdGVZKDApO1xuICB9XG4gIDMwJSB7XG4gICAgb3BhY2l0eTogMTtcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMykgdHJhbnNsYXRlWSgtNnB4KTtcbiAgfVxufVxuXG5Aa2V5ZnJhbWVzIHNsaWRlSW5VcCB7XG4gIGZyb20ge1xuICAgIG9wYWNpdHk6IDA7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDQwcHgpIHNjYWxlKDAuOSk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApIHNjYWxlKDEpO1xuICB9XG59XG5cbkBrZXlmcmFtZXMgc3BpbiB7XG4gIDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH1cbiAgMTAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH1cbn1cblxuLyogRW5oYW5jZWQgbW9iaWxlIHJlc3BvbnNpdmUgZGVzaWduICovXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLmNoYXQtY29udGFpbmVyIHtcbiAgICBtYXgtd2lkdGg6IDEwMCU7XG4gICAgaGVpZ2h0OiAxMDB2aDtcbiAgICBib3JkZXItcmFkaXVzOiAwO1xuICAgIGJvcmRlcjogbm9uZTtcbiAgfVxuXG4gIC5tZXNzYWdlcy1jb250YWluZXIge1xuICAgIHBhZGRpbmc6IHZhcigtLXNwYWNlLTUpIHZhcigtLXNwYWNlLTUpIDE0MHB4IHZhcigtLXNwYWNlLTUpO1xuICB9XG5cbiAgLmlucHV0LWNvbnRhaW5lciB7XG4gICAgcGFkZGluZzogdmFyKC0tc3BhY2UtNSkgdmFyKC0tc3BhY2UtNSkgdmFyKC0tc3BhY2UtNikgdmFyKC0tc3BhY2UtNSk7XG4gICAgbWF4LXdpZHRoOiAxMDAlO1xuXG4gICAgJi5jZW50ZXJlZCB7XG4gICAgICBtYXgtd2lkdGg6IDEwMCU7XG4gICAgICBwYWRkaW5nOiB2YXIoLS1zcGFjZS01KTtcbiAgICAgIG1hcmdpbjogMCB2YXIoLS1zcGFjZS00KTtcbiAgICAgIHdpZHRoOiBjYWxjKDEwMCUgLSB2YXIoLS1zcGFjZS04KSk7XG4gICAgfVxuICB9XG5cbiAgLm1lc3NhZ2Uge1xuICAgIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNlLTUpO1xuXG4gICAgJi51c2VyLW1lc3NhZ2UgLm1lc3NhZ2UtY29udGVudCxcbiAgICAmLmJvdC1tZXNzYWdlIC5tZXNzYWdlLWNvbnRlbnQge1xuICAgICAgbWF4LXdpZHRoOiA4OCU7XG4gICAgfVxuICB9XG5cbiAgLmlucHV0LXdyYXBwZXIge1xuICAgIHBhZGRpbmc6IHZhcigtLXNwYWNlLTEpIHZhcigtLXNwYWNlLTIpIHZhcigtLXNwYWNlLTEpIHZhcigtLXNwYWNlLTQpO1xuICAgIGdhcDogdmFyKC0tc3BhY2UtMik7XG4gIH1cblxuICAuc2VuZC1idXR0b24ge1xuICAgIHdpZHRoOiA0MnB4O1xuICAgIGhlaWdodDogNDJweDtcblxuICAgIHN2ZyB7XG4gICAgICB3aWR0aDogMThweDtcbiAgICAgIGhlaWdodDogMThweDtcbiAgICB9XG4gIH1cbn1cblxuLyogRW5oYW5jZWQgc21vb3RoIHRyYW5zaXRpb25zIGZvciBiZXR0ZXIgVVggKi9cbioge1xuICB0cmFuc2l0aW9uOiBjb2xvciB2YXIoLS10cmFuc2l0aW9uLWZhc3QpLFxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yIHZhcigtLXRyYW5zaXRpb24tZmFzdCksXG4gICAgICAgICAgICAgIGJvcmRlci1jb2xvciB2YXIoLS10cmFuc2l0aW9uLWZhc3QpLFxuICAgICAgICAgICAgICB0cmFuc2Zvcm0gdmFyKC0tdHJhbnNpdGlvbi1mYXN0KSxcbiAgICAgICAgICAgICAgYm94LXNoYWRvdyB2YXIoLS10cmFuc2l0aW9uLWZhc3QpO1xufVxuXG4vKiBFbmhhbmNlZCBmb2N1cyBpbmRpY2F0b3JzIGZvciBhY2Nlc3NpYmlsaXR5ICovXG4uc2VuZC1idXR0b246Zm9jdXMge1xuICBvdXRsaW5lOiAycHggc29saWQgdmFyKC0tY29sb3ItcHJpbWFyeSk7XG4gIG91dGxpbmUtb2Zmc2V0OiAzcHg7XG59XG5cbi5tZXNzYWdlLWlucHV0OmZvY3VzIHtcbiAgYm94LXNoYWRvdzogbm9uZTtcbn1cblxuLyogQWRkaXRpb25hbCBtb2Rlcm4gZW5oYW5jZW1lbnRzICovXG4ubWVzc2FnZS1jb250ZW50IHtcbiAgJjo6c2VsZWN0aW9uIHtcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDE2LCAxODUsIDEyOSwgMC4yKTtcbiAgfVxufVxuXG4udXNlci1tZXNzYWdlIC5tZXNzYWdlLWNvbnRlbnQge1xuICAmOjpzZWxlY3Rpb24ge1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKTtcbiAgfVxufVxuXG4vKiBDbGVhbiBhbmltYXRpb25zICovXG5Aa2V5ZnJhbWVzIGZhZGVJblVwIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMjBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICB9XG59XG5cbkBrZXlmcmFtZXMgdHlwaW5nIHtcbiAgMCUsIDYwJSwgMTAwJSB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICAgIG9wYWNpdHk6IDAuNDtcbiAgfVxuICAzMCUge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCk7XG4gICAgb3BhY2l0eTogMTtcbiAgfVxufVxuXG5Aa2V5ZnJhbWVzIHNwaW4ge1xuICAwJSB7XG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7XG4gIH1cbiAgMTAwJSB7XG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTtcbiAgfVxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["marked", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "message_r8", "news_articles", "ɵɵtext", "ɵɵtemplate", "ChatComponent_div_1_div_1_div_3_div_6_Template", "ɵɵpureFunction2", "_c1", "isUser", "ctx_r6", "formatMessageText", "text", "ɵɵsanitizeHtml", "ɵɵtextInterpolate", "ɵɵpipeBind2", "timestamp", "length", "ɵɵlistener", "ChatComponent_div_1_div_1_Template_div_scroll_0_listener", "$event", "ɵɵrestoreView", "_r12", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "onScroll", "ChatComponent_div_1_div_1_div_2_Template", "ChatComponent_div_1_div_1_div_3_Template", "ChatComponent_div_1_div_1_div_4_Template", "ctx_r2", "isLoadingHistory", "messages", "isLoading", "ChatComponent_div_1_div_1_Template", "ChatComponent_div_1_Template_textarea_ngModelChange_4_listener", "_r14", "ctx_r13", "currentMessage", "ChatComponent_div_1_Template_textarea_keydown_4_listener", "ctx_r15", "onKeyPress", "ChatComponent_div_1_Template_textarea_input_4_listener", "ctx_r16", "adjustTextareaHeight", "ChatComponent_div_1_Template_button_click_7_listener", "ctx_r17", "sendMessage", "ɵɵnamespaceSVG", "ctx_r0", "ɵɵclassProp", "trim", "ChatComponent", "constructor", "authService", "isAuthenticated", "currentSessionId", "currentPage", "hasNextPage", "shouldScrollToBottom", "lastScrollHeight", "initialLoadComplete", "userHasScrolled", "ngOnInit", "ensureAuthenticated", "subscribe", "next", "token", "console", "log", "loadChatHistory", "error", "ngAfterViewChecked", "scrollToBottom", "event", "element", "target", "scrollTop", "scrollHeight", "clientHeight", "scrollTimeout", "clearTimeout", "setTimeout", "isNearTop", "isNotAtBottom", "loadMoreHistory", "page", "append", "undefined", "response", "newMessages", "results", "map", "msg", "convertChatMessageToMessage", "reversedNewMessages", "reverse", "maintainScrollPosition", "chatMessage", "id", "message", "sender", "Date", "session", "prompt", "model", "messagesContainer", "nativeElement", "newScrollHeight", "scrollDifference", "messageToSend", "tempUserMessage", "push", "sendMessageToChatbot", "userMessage", "user_message", "botMessage", "bot_message", "lastMessageIndex", "errorMessage", "clearHistory", "refreshHistory", "key", "shift<PERSON>ey", "preventDefault", "textarea", "style", "height", "Math", "min", "setOptions", "breaks", "gfm", "htmlContent", "parse", "sanitizedHtml", "replace", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "viewQuery", "ChatComponent_Query", "rf", "ctx", "ChatComponent_div_1_Template", "ChatComponent_div_2_Template"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\chat\\chat.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\chat\\chat.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ViewChild, AfterViewChecked } from '@angular/core';\nimport { AuthService } from '../auth.service'; // Adjust path as needed\nimport { marked } from 'marked';\n\ninterface NewsArticle {\n  id?: number;\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string;\n  description: string;\n  fetched_at?: string;\n  created_at?: string;\n}\n\ninterface Message {\n  id?: number;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n  session?: number;\n  prompt?: number;\n  model?: number;\n  news_articles?: NewsArticle[]; // for bot messages that include news\n}\n\n// Django ChatMessage structure\ninterface ChatMessage {\n  id: number;\n  session: number;\n  sender: 'user' | 'bot';\n  message: string;\n  timestamp: string;\n  prompt: number | null;\n  model: number | null;\n  news_articles?: NewsArticle[] | null;\n}\n\n// Django paginated response\ninterface ChatHistoryResponse {\n  count: number;\n  next: string | null;\n  previous: string | null;\n  results: ChatMessage[];\n}\n\n// Django chatbot response\ninterface ChatbotResponse {\n  user_message: ChatMessage;\n  bot_message: ChatMessage;\n}\n\n@Component({\n  selector: 'app-chat',\n  templateUrl: './chat.component.html',\n  styleUrls: ['./chat.component.scss']\n})\nexport class ChatComponent implements OnInit, OnDestroy, AfterViewChecked {\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  \n  messages: Message[] = [];\n  currentMessage: string = '';\n  isLoading: boolean = false;\n  isAuthenticated: boolean = false;\n  currentSessionId: number | null = null;\n  \n  // Pagination\n  currentPage: number = 1;\n  hasNextPage: boolean = false;\n  isLoadingHistory: boolean = false;\n  \n  // Scroll management\n  private shouldScrollToBottom: boolean = true;\n  private lastScrollHeight: number = 0;\n  private initialLoadComplete: boolean = false;\n  private userHasScrolled: boolean = false;\n  private scrollTimeout: any;\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: (token) => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory(1, false);\n      },\n      error: (error) => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n\n  // Listen for scroll events to load more history\n  onScroll(event: any) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    const clientHeight = element.clientHeight;\n    \n    // Mark that user has scrolled manually (not programmatic)\n    if (this.initialLoadComplete) {\n      this.userHasScrolled = true;\n    }\n    \n    // Clear existing timeout\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    \n    // Debounce scroll events and check conditions\n    this.scrollTimeout = setTimeout(() => {\n      // Only load more if:\n      // 1. Initial load is complete\n      // 2. User has scrolled manually at least once\n      // 3. User is near the top (scrollTop < 100)\n      // 4. There are more pages to load\n      // 5. Not currently loading\n      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n      const isNearTop = scrollTop < 100;\n      const isNotAtBottom = scrollTop < (scrollHeight - clientHeight - 50);\n      \n      if (isNearTop && \n          isNotAtBottom &&\n          this.hasNextPage && \n          !this.isLoadingHistory && \n          this.initialLoadComplete &&\n          this.userHasScrolled) {\n        this.lastScrollHeight = scrollHeight;\n        this.loadMoreHistory();\n      }\n    }, 100); // 100ms debounce\n  }\n\n  loadChatHistory(page: number = 1, append: boolean = false) {\n    if (!this.isAuthenticated) return;\n\n    this.isLoadingHistory = true;\n\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatHistoryResponse) => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        \n        if (append) {\n          // For pagination - reverse the new messages (since API returns newest first)\n          // and prepend older messages to beginning\n          const reversedNewMessages = [...newMessages].reverse();\n          this.messages = [...reversedNewMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - reverse messages to get chronological order (oldest first)\n          this.messages = [...newMessages].reverse();\n          this.shouldScrollToBottom = true;\n          \n          // Set initial load complete after scroll positioning is done\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n        \n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        \n        this.isLoadingHistory = false;\n      },\n      error: (error) => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        \n        // If this was the initial load, still mark it as complete after delay\n        if (!append) {\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n      }\n    });\n  }\n\n  // Convert Django ChatMessage to frontend Message format\n  private convertChatMessageToMessage(chatMessage: ChatMessage): Message {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined,\n      news_articles: chatMessage.news_articles || undefined,\n    };\n  }\n\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n\n  // Maintain scroll position when loading older messages\n  private maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n\n    // Create temporary user message and add it instantly\n    const tempUserMessage: Message = {\n      text: messageToSend,\n      isUser: true,\n      timestamp: new Date()\n    };\n\n    // Add user message instantly to the chat\n    this.messages.push(tempUserMessage);\n    this.shouldScrollToBottom = true;\n\n    // Set loading state\n    this.isLoading = true;\n\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatbotResponse) => {\n        // Convert backend messages\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n\n        // Replace the temporary user message with the one from backend\n        const lastMessageIndex = this.messages.length - 1;\n        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n          this.messages[lastMessageIndex] = userMessage;\n        }\n\n        // Add bot message\n        this.messages.push(botMessage);\n\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        const errorMessage: Message = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n\n  clearHistory() {\n    this.messages = [];\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n\n  refreshHistory() {\n    this.currentPage = 1;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.loadChatHistory();\n  }\n\n  onKeyPress(event: KeyboardEvent) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  adjustTextareaHeight(event: any) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n\n  private scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n\n  // Format message text using marked library for markdown\n  formatMessageText(text: string): string {\n    if (!text) return '';\n\n    try {\n      // Configure marked to be more restrictive for security\n      marked.setOptions({\n        breaks: true, // Convert line breaks to <br>\n        gfm: true // Enable GitHub Flavored Markdown\n      });\n\n      // Convert markdown to HTML using marked (synchronous)\n      const htmlContent = marked.parse(text) as string;\n\n      // Basic sanitization - remove script tags and dangerous attributes\n      let sanitizedHtml = htmlContent\n        .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n        .replace(/javascript:/gi, '')\n        .replace(/on\\w+\\s*=/gi, '');\n\n      return sanitizedHtml;\n    } catch (error) {\n      console.error('Error formatting message text:', error);\n      // Fallback to plain text with basic line break conversion\n      return text.replace(/\\n/g, '<br>');\n    }\n  }\n\n  ngOnDestroy() {\n    // Clean up any pending timeouts\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n}", "<!-- chat.component.html -->\n\n<!-- Anonymous user component runs in background -->\n<app-anonymous-user></app-anonymous-user>\n\n<div class=\"chat-container\" *ngIf=\"isAuthenticated\">\n  <!-- Messages container - only show when there are messages -->\n  <div \n    #messagesContainer\n    class=\"messages-container\" \n    (scroll)=\"onScroll($event)\"\n    *ngIf=\"messages.length > 0\">\n    \n    <!-- Loading indicator for pagination at the top -->\n    <div class=\"pagination-loading\" *ngIf=\"isLoadingHistory\">\n      <div class=\"loading-spinner\"></div>\n    </div>\n    \n    <div class=\"message\"\n         *ngFor=\"let message of messages\"\n         [ngClass]=\"{'user-message': message.isUser, 'bot-message': !message.isUser}\">\n      <div class=\"message-content\">\n        <div class=\"message-text\" [innerHTML]=\"formatMessageText(message.text)\"></div>\n        <div class=\"message-time\">{{ message.timestamp | date:'short' }}</div>\n      </div>\n\n      <!-- Render news articles UNDER the bot message bubble when present -->\n      <div class=\"message-news\"\n           *ngIf=\"!message.isUser && message.news_articles && message.news_articles.length\">\n        <app-news\n          [news]=\"message.news_articles\"\n          [showTitle]=\"false\"\n          [compact]=\"true\"></app-news>\n      </div>\n    </div>\n\n    <!-- Loading indicator for current message -->\n    <div class=\"message bot-message\" *ngIf=\"isLoading\">\n      <div class=\"message-content\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input container - always visible -->\n  <div class=\"input-container\" [class.centered]=\"messages.length === 0\" [class.bottom]=\"messages.length > 0\">\n    <div class=\"input-wrapper\">\n      <textarea\n        #messageTextarea\n        [(ngModel)]=\"currentMessage\"\n        (keydown)=\"onKeyPress($event)\"\n        (input)=\"adjustTextareaHeight($event)\"\n        placeholder=\"Ask anything\"\n        class=\"message-input\"\n        rows=\"1\">\n      </textarea>\n      <button\n        (click)=\"sendMessage()\"\n        class=\"send-button\"\n        [disabled]=\"!currentMessage.trim() || isLoading\">\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"currentColor\"/>\n        </svg>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Show loading message while authenticating -->\n<div *ngIf=\"!isAuthenticated\" class=\"auth-loading\">\n  <div class=\"loading-spinner\"></div>\n  <p>Initializing chat...</p>\n</div>"], "mappings": "AAEA,SAASA,MAAM,QAAQ,QAAQ;;;;;;;;;;ICY3BC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,cAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWJH,EAAA,CAAAC,cAAA,cACsF;IACpFD,EAAA,CAAAE,SAAA,mBAG8B;IAChCF,EAAA,CAAAG,YAAA,EAAM;;;;IAHFH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,UAAA,SAAAC,UAAA,CAAAC,aAAA,CAA8B;;;;;;;;;;;IAZpCP,EAAA,CAAAC,cAAA,cAEkF;IAE9ED,EAAA,CAAAE,SAAA,cAA8E;IAC9EF,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAQ,MAAA,GAAsC;;IAAAR,EAAA,CAAAG,YAAA,EAAM;IAIxEH,EAAA,CAAAS,UAAA,IAAAC,8CAAA,kBAMM;IACRV,EAAA,CAAAG,YAAA,EAAM;;;;;IAdDH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,IAAAC,GAAA,EAAAN,UAAA,CAAAO,MAAA,GAAAP,UAAA,CAAAO,MAAA,EAA4E;IAEnDb,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,UAAA,cAAAS,MAAA,CAAAC,iBAAA,CAAAT,UAAA,CAAAU,IAAA,GAAAhB,EAAA,CAAAiB,cAAA,CAA6C;IAC7CjB,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAmB,WAAA,OAAAb,UAAA,CAAAc,SAAA,WAAsC;IAK5DpB,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAK,UAAA,UAAAC,UAAA,CAAAO,MAAA,IAAAP,UAAA,CAAAC,aAAA,IAAAD,UAAA,CAAAC,aAAA,CAAAc,MAAA,CAA8E;;;;;IAStFrB,EAAA,CAAAC,cAAA,cAAmD;IAG7CD,EAAA,CAAAE,SAAA,WAAa;IAGfF,EAAA,CAAAG,YAAA,EAAM;;;;;;IApCZH,EAAA,CAAAC,cAAA,kBAI8B;IAD5BD,EAAA,CAAAsB,UAAA,oBAAAC,yDAAAC,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAU5B,EAAA,CAAA6B,WAAA,CAAAF,OAAA,CAAAG,QAAA,CAAAN,MAAA,CAAgB;IAAA,EAAC;IAI3BxB,EAAA,CAAAS,UAAA,IAAAsB,wCAAA,kBAEM;IAEN/B,EAAA,CAAAS,UAAA,IAAAuB,wCAAA,mBAgBM;IAGNhC,EAAA,CAAAS,UAAA,IAAAwB,wCAAA,kBAQM;IACRjC,EAAA,CAAAG,YAAA,EAAM;;;;IAhC6BH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,UAAA,SAAA6B,MAAA,CAAAC,gBAAA,CAAsB;IAK9BnC,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,UAAA,YAAA6B,MAAA,CAAAE,QAAA,CAAW;IAkBFpC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,UAAA,SAAA6B,MAAA,CAAAG,SAAA,CAAe;;;;;;IAhCrDrC,EAAA,CAAAC,cAAA,aAAoD;IAElDD,EAAA,CAAAS,UAAA,IAAA6B,kCAAA,iBAuCM;IAGNtC,EAAA,CAAAC,cAAA,aAA2G;IAIrGD,EAAA,CAAAsB,UAAA,2BAAAiB,+DAAAf,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAAY,OAAA,CAAAC,cAAA,GAAAlB,MAAA;IAAA,EAA4B,qBAAAmB,yDAAAnB,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAI,OAAA,GAAA5C,EAAA,CAAA4B,aAAA;MAAA,OACjB5B,EAAA,CAAA6B,WAAA,CAAAe,OAAA,CAAAC,UAAA,CAAArB,MAAA,CAAkB;IAAA,EADD,mBAAAsB,uDAAAtB,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAO,OAAA,GAAA/C,EAAA,CAAA4B,aAAA;MAAA,OAEnB5B,EAAA,CAAA6B,WAAA,CAAAkB,OAAA,CAAAC,oBAAA,CAAAxB,MAAA,CAA4B;IAAA,EAFT;IAM9BxB,EAAA,CAAAQ,MAAA;IAAAR,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,gBAGmD;IAFjDD,EAAA,CAAAsB,UAAA,mBAAA2B,qDAAA;MAAAjD,EAAA,CAAAyB,aAAA,CAAAe,IAAA;MAAA,MAAAU,OAAA,GAAAlD,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAqB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvBnD,EAAA,CAAAoD,cAAA,EAA+F;IAA/FpD,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAE,SAAA,eAAiE;IACnEF,EAAA,CAAAG,YAAA,EAAM;;;;IAvDTH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,UAAA,SAAAgD,MAAA,CAAAjB,QAAA,CAAAf,MAAA,KAAyB;IAsCCrB,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAsD,WAAA,aAAAD,MAAA,CAAAjB,QAAA,CAAAf,MAAA,OAAwC,WAAAgC,MAAA,CAAAjB,QAAA,CAAAf,MAAA;IAI/DrB,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,UAAA,YAAAgD,MAAA,CAAAX,cAAA,CAA4B;IAU5B1C,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,UAAA,cAAAgD,MAAA,CAAAX,cAAA,CAAAa,IAAA,MAAAF,MAAA,CAAAhB,SAAA,CAAgD;;;;;IAUxDrC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAQ,MAAA,2BAAoB;IAAAR,EAAA,CAAAG,YAAA,EAAI;;;ADjB7B,OAAM,MAAOqD,aAAa;EAqBxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAlB/B,KAAAtB,QAAQ,GAAc,EAAE;IACxB,KAAAM,cAAc,GAAW,EAAE;IAC3B,KAAAL,SAAS,GAAY,KAAK;IAC1B,KAAAsB,eAAe,GAAY,KAAK;IAChC,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAA3B,gBAAgB,GAAY,KAAK;IAEjC;IACQ,KAAA4B,oBAAoB,GAAY,IAAI;IACpC,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,eAAe,GAAY,KAAK;EAGO;EAE/CC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,WAAW,CAACU,mBAAmB,EAAE,CAACC,SAAS,CAAC;MAC/CC,IAAI,EAAGC,KAAK,IAAI;QACdC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,IAAI,CAACd,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACe,eAAe,CAAC,CAAC,EAAE,KAAK,CAAC;MAChC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChB,eAAe,GAAG,KAAK;MAC9B;KACD,CAAC;EACJ;EAEAiB,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACb,oBAAoB,EAAE;MAC7B,IAAI,CAACc,cAAc,EAAE;MACrB,IAAI,CAACd,oBAAoB,GAAG,KAAK;;EAErC;EAEA;EACAjC,QAAQA,CAACgD,KAAU;IACjB,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM;IAC5B,MAAMC,SAAS,GAAGF,OAAO,CAACE,SAAS;IACnC,MAAMC,YAAY,GAAGH,OAAO,CAACG,YAAY;IACzC,MAAMC,YAAY,GAAGJ,OAAO,CAACI,YAAY;IAEzC;IACA,IAAI,IAAI,CAAClB,mBAAmB,EAAE;MAC5B,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B;IACA,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC;IACA,IAAI,CAACA,aAAa,GAAGE,UAAU,CAAC,MAAK;MACnC;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMC,SAAS,GAAGN,SAAS,GAAG,GAAG;MACjC,MAAMO,aAAa,GAAGP,SAAS,GAAIC,YAAY,GAAGC,YAAY,GAAG,EAAG;MAEpE,IAAII,SAAS,IACTC,aAAa,IACb,IAAI,CAAC1B,WAAW,IAChB,CAAC,IAAI,CAAC3B,gBAAgB,IACtB,IAAI,CAAC8B,mBAAmB,IACxB,IAAI,CAACC,eAAe,EAAE;QACxB,IAAI,CAACF,gBAAgB,GAAGkB,YAAY;QACpC,IAAI,CAACO,eAAe,EAAE;;IAE1B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;;EAEAf,eAAeA,CAACgB,IAAA,GAAe,CAAC,EAAEC,MAAA,GAAkB,KAAK;IACvD,IAAI,CAAC,IAAI,CAAChC,eAAe,EAAE;IAE3B,IAAI,CAACxB,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAACuB,WAAW,CAACgB,eAAe,CAACgB,IAAI,EAAE,IAAI,CAAC9B,gBAAgB,IAAIgC,SAAS,CAAC,CAACvB,SAAS,CAAC;MACnFC,IAAI,EAAGuB,QAA6B,IAAI;QACtC,MAAMC,WAAW,GAAGD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,GAAG,IAAI,IAAI,CAACC,2BAA2B,CAACD,GAAG,CAAC,CAAC;QAEtF,IAAIN,MAAM,EAAE;UACV;UACA;UACA,MAAMQ,mBAAmB,GAAG,CAAC,GAAGL,WAAW,CAAC,CAACM,OAAO,EAAE;UACtD,IAAI,CAAChE,QAAQ,GAAG,CAAC,GAAG+D,mBAAmB,EAAE,GAAG,IAAI,CAAC/D,QAAQ,CAAC;UAC1D,IAAI,CAACiE,sBAAsB,EAAE;SAC9B,MAAM;UACL;UACA,IAAI,CAACjE,QAAQ,GAAG,CAAC,GAAG0D,WAAW,CAAC,CAACM,OAAO,EAAE;UAC1C,IAAI,CAACrC,oBAAoB,GAAG,IAAI;UAEhC;UACAuB,UAAU,CAAC,MAAK;YACd,IAAI,CAACrB,mBAAmB,GAAG,IAAI;UACjC,CAAC,EAAE,GAAG,CAAC;;QAGT;QACA,IAAI,CAACJ,WAAW,GAAG6B,IAAI;QACvB,IAAI,CAAC5B,WAAW,GAAG,CAAC,CAAC+B,QAAQ,CAACvB,IAAI;QAElC,IAAI,CAACnC,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACxC,gBAAgB,GAAG,KAAK;QAE7B;QACA,IAAI,CAACwD,MAAM,EAAE;UACXL,UAAU,CAAC,MAAK;YACd,IAAI,CAACrB,mBAAmB,GAAG,IAAI;UACjC,CAAC,EAAE,GAAG,CAAC;;MAEX;KACD,CAAC;EACJ;EAEA;EACQiC,2BAA2BA,CAACI,WAAwB;IAC1D,OAAO;MACLC,EAAE,EAAED,WAAW,CAACC,EAAE;MAClBvF,IAAI,EAAEsF,WAAW,CAACE,OAAO;MACzB3F,MAAM,EAAEyF,WAAW,CAACG,MAAM,KAAK,MAAM;MACrCrF,SAAS,EAAE,IAAIsF,IAAI,CAACJ,WAAW,CAAClF,SAAS,CAAC;MAC1CuF,OAAO,EAAEL,WAAW,CAACK,OAAO;MAC5BC,MAAM,EAAEN,WAAW,CAACM,MAAM,IAAIhB,SAAS;MACvCiB,KAAK,EAAEP,WAAW,CAACO,KAAK,IAAIjB,SAAS;MACrCrF,aAAa,EAAE+F,WAAW,CAAC/F,aAAa,IAAIqF;KAC7C;EACH;EAEA;EACAH,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3B,WAAW,IAAI,CAAC,IAAI,CAAC3B,gBAAgB,EAAE;MAC9C,IAAI,CAACuC,eAAe,CAAC,IAAI,CAACb,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;;EAEpD;EAEA;EACQwC,sBAAsBA,CAAA;IAC5Bf,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACwB,iBAAiB,EAAE;QAC1B,MAAM/B,OAAO,GAAG,IAAI,CAAC+B,iBAAiB,CAACC,aAAa;QACpD,MAAMC,eAAe,GAAGjC,OAAO,CAACG,YAAY;QAC5C,MAAM+B,gBAAgB,GAAGD,eAAe,GAAG,IAAI,CAAChD,gBAAgB;QAChEe,OAAO,CAACE,SAAS,GAAGgC,gBAAgB;;IAExC,CAAC,EAAE,EAAE,CAAC;EACR;EAEA9D,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACT,cAAc,CAACa,IAAI,EAAE,IAAI,IAAI,CAAClB,SAAS,IAAI,CAAC,IAAI,CAACsB,eAAe,EAAE;MAC1E;;IAGF;IACA,MAAMuD,aAAa,GAAG,IAAI,CAACxE,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IAExB;IACA,MAAMyE,eAAe,GAAY;MAC/BnG,IAAI,EAAEkG,aAAa;MACnBrG,MAAM,EAAE,IAAI;MACZO,SAAS,EAAE,IAAIsF,IAAI;KACpB;IAED;IACA,IAAI,CAACtE,QAAQ,CAACgF,IAAI,CAACD,eAAe,CAAC;IACnC,IAAI,CAACpD,oBAAoB,GAAG,IAAI;IAEhC;IACA,IAAI,CAAC1B,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACqB,WAAW,CAAC2D,oBAAoB,CAACH,aAAa,EAAE,IAAI,CAACtD,gBAAgB,IAAIgC,SAAS,CAAC,CAACvB,SAAS,CAAC;MACjGC,IAAI,EAAGuB,QAAyB,IAAI;QAClC;QACA,MAAMyB,WAAW,GAAG,IAAI,CAACpB,2BAA2B,CAACL,QAAQ,CAAC0B,YAAY,CAAC;QAC3E,MAAMC,UAAU,GAAG,IAAI,CAACtB,2BAA2B,CAACL,QAAQ,CAAC4B,WAAW,CAAC;QAEzE;QACA,MAAMC,gBAAgB,GAAG,IAAI,CAACtF,QAAQ,CAACf,MAAM,GAAG,CAAC;QACjD,IAAIqG,gBAAgB,IAAI,CAAC,IAAI,IAAI,CAACtF,QAAQ,CAACsF,gBAAgB,CAAC,CAAC7G,MAAM,EAAE;UACnE,IAAI,CAACuB,QAAQ,CAACsF,gBAAgB,CAAC,GAAGJ,WAAW;;QAG/C;QACA,IAAI,CAAClF,QAAQ,CAACgF,IAAI,CAACI,UAAU,CAAC;QAE9B;QACA,IAAI,CAAC,IAAI,CAAC5D,gBAAgB,EAAE;UAC1B,IAAI,CAACA,gBAAgB,GAAGiC,QAAQ,CAAC0B,YAAY,CAACZ,OAAO;;QAGvD,IAAI,CAACtE,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC0B,oBAAoB,GAAG,IAAI;MAClC,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMgD,YAAY,GAAY;UAC5B3G,IAAI,EAAE,sEAAsE;UAC5EH,MAAM,EAAE,KAAK;UACbO,SAAS,EAAE,IAAIsF,IAAI;SACpB;QACD,IAAI,CAACtE,QAAQ,CAACgF,IAAI,CAACO,YAAY,CAAC;QAChC,IAAI,CAACtF,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC0B,oBAAoB,GAAG,IAAI;MAClC;KACD,CAAC;EACJ;EAEA6D,YAAYA,CAAA;IACV,IAAI,CAACxF,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACyB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEAyC,cAAcA,CAAA;IACZ,IAAI,CAAChE,WAAW,GAAG,CAAC;IACpB,IAAI,CAACI,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAElC,IAAI,CAACV,eAAe,EAAE;EACxB;EAEA7B,UAAUA,CAACiC,KAAoB;IAC7B,IAAIA,KAAK,CAACgD,GAAG,KAAK,OAAO,IAAI,CAAChD,KAAK,CAACiD,QAAQ,EAAE;MAC5CjD,KAAK,CAACkD,cAAc,EAAE;MACtB,IAAI,CAAC7E,WAAW,EAAE;;EAEtB;EAEAH,oBAAoBA,CAAC8B,KAAU;IAC7B,MAAMmD,QAAQ,GAAGnD,KAAK,CAACE,MAAM;IAC7BiD,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;IAC9BF,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC/C,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;EACrE;EAEQL,cAAcA,CAAA;IACpBS,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACwB,iBAAiB,EAAE;QAC1B,MAAM/B,OAAO,GAAG,IAAI,CAAC+B,iBAAiB,CAACC,aAAa;QACpDhC,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,EAAE,CAAC;EACR;EAEA;EACAnE,iBAAiBA,CAACC,IAAY;IAC5B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,IAAI;MACF;MACAjB,MAAM,CAACuI,UAAU,CAAC;QAChBC,MAAM,EAAE,IAAI;QACZC,GAAG,EAAE,IAAI,CAAC;OACX,CAAC;MAEF;MACA,MAAMC,WAAW,GAAG1I,MAAM,CAAC2I,KAAK,CAAC1H,IAAI,CAAW;MAEhD;MACA,IAAI2H,aAAa,GAAGF,WAAW,CAC5BG,OAAO,CAAC,qDAAqD,EAAE,EAAE,CAAC,CAClEA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;MAE7B,OAAOD,aAAa;KACrB,CAAC,OAAOhE,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACA,OAAO3D,IAAI,CAAC4H,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;;EAEtC;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACzD,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAAC,QAAA0D,CAAA,G;qBA/SUtF,aAAa,EAAAxD,EAAA,CAAA+I,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAb1F,aAAa;IAAA2F,SAAA;IAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCvD1BtJ,EAAA,CAAAE,SAAA,yBAAyC;QAEzCF,EAAA,CAAAS,UAAA,IAAA+I,4BAAA,kBAiEM;QAGNxJ,EAAA,CAAAS,UAAA,IAAAgJ,4BAAA,iBAGM;;;QAvEuBzJ,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAAK,UAAA,SAAAkJ,GAAA,CAAA5F,eAAA,CAAqB;QAoE5C3D,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAK,UAAA,UAAAkJ,GAAA,CAAA5F,eAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}