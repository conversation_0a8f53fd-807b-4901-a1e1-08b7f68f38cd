{"ast": null, "code": "export function executeSchedule(parentSubscription, scheduler, work, delay = 0, repeat = false) {\n  const scheduleSubscription = scheduler.schedule(function () {\n    work();\n    if (repeat) {\n      parentSubscription.add(this.schedule(null, delay));\n    } else {\n      this.unsubscribe();\n    }\n  }, delay);\n  parentSubscription.add(scheduleSubscription);\n  if (!repeat) {\n    return scheduleSubscription;\n  }\n}", "map": {"version": 3, "names": ["executeSchedule", "parentSubscription", "scheduler", "work", "delay", "repeat", "scheduleSubscription", "schedule", "add", "unsubscribe"], "sources": ["C:/Users/<USER>/PycharmProjects/GenAI/Borys-Slavic-News-Chatbot/frontend/node_modules/rxjs/dist/esm/internal/util/executeSchedule.js"], "sourcesContent": ["export function executeSchedule(parentSubscription, scheduler, work, delay = 0, repeat = false) {\n    const scheduleSubscription = scheduler.schedule(function () {\n        work();\n        if (repeat) {\n            parentSubscription.add(this.schedule(null, delay));\n        }\n        else {\n            this.unsubscribe();\n        }\n    }, delay);\n    parentSubscription.add(scheduleSubscription);\n    if (!repeat) {\n        return scheduleSubscription;\n    }\n}\n"], "mappings": "AAAA,OAAO,SAASA,eAAeA,CAACC,kBAAkB,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,KAAK,EAAE;EAC5F,MAAMC,oBAAoB,GAAGJ,SAAS,CAACK,QAAQ,CAAC,YAAY;IACxDJ,IAAI,CAAC,CAAC;IACN,IAAIE,MAAM,EAAE;MACRJ,kBAAkB,CAACO,GAAG,CAAC,IAAI,CAACD,QAAQ,CAAC,IAAI,EAAEH,KAAK,CAAC,CAAC;IACtD,CAAC,MACI;MACD,IAAI,CAACK,WAAW,CAAC,CAAC;IACtB;EACJ,CAAC,EAAEL,KAAK,CAAC;EACTH,kBAAkB,CAACO,GAAG,CAACF,oBAAoB,CAAC;EAC5C,IAAI,CAACD,MAAM,EAAE;IACT,OAAOC,oBAAoB;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}