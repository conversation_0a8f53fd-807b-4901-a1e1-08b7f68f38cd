{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction NewsComponent_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 5)(1, \"div\", 6)(2, \"span\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 8);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"h3\", 9);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 10);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 11)(12, \"span\", 12);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 13);\n    i0.ɵɵtext(16, \"Read \\u2192\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"href\", item_r2.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 6, item_r2.country));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.source);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 8, item_r2.published, \"medium\"));\n  }\n}\nfunction NewsComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p\");\n    i0.ɵɵtext(2, \"No news available.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class NewsComponent {\n  constructor() {\n    // Static news list\n    this.news = [{\n      country: 'czech',\n      source: 'iDNES.cz',\n      title: 'Czech Republic strengthens ties with NATO allies',\n      link: 'https://idnes.cz/news/czech-nato-123',\n      published: '2025-08-07T10:30:00Z',\n      description: 'Prague announces new defense cooperation agreements with NATO members...',\n      fetched_at: '2025-08-07T11:30:00'\n    }, {\n      country: 'czech',\n      source: 'Czech Radio',\n      title: 'Putin discusses economic cooperation with Czech officials',\n      link: 'https://rozhlas.cz/putin-czech-456',\n      published: '2025-08-07T09:15:00Z',\n      description: 'Russian president meets with Czech representatives for trade talks...',\n      fetched_at: '2025-08-07T11:30:00'\n    }, {\n      country: 'russia',\n      source: 'RT',\n      title: 'Putin announces new economic initiatives',\n      link: 'https://rt.com/putin-economy-654',\n      published: '2025-08-07T12:00:00Z',\n      description: 'Russian president outlines strategic economic development plans...',\n      fetched_at: '2025-08-07T11:30:00'\n    }, {\n      country: 'russia',\n      source: 'TASS',\n      title: 'Ukraine situation discussed in Moscow summit',\n      link: 'https://tass.com/ukraine-summit-987',\n      published: '2025-08-07T11:30:00Z',\n      description: 'High-level meetings address regional security concerns...',\n      fetched_at: '2025-08-07T11:30:00'\n    }];\n  }\n  trackByTitle(index, item) {\n    return item.title;\n  }\n  static #_ = this.ɵfac = function NewsComponent_Factory(t) {\n    return new (t || NewsComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NewsComponent,\n    selectors: [[\"app-news\"]],\n    decls: 6,\n    vars: 3,\n    consts: [[1, \"news-wrapper\"], [1, \"section-title\"], [1, \"news-grid\"], [\"class\", \"news-card\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"news-card\", 3, \"href\"], [1, \"news-card-header\"], [1, \"badge\"], [1, \"source\"], [1, \"title\"], [1, \"description\"], [1, \"news-card-footer\"], [1, \"published\"], [1, \"cta\"], [1, \"empty-state\"]],\n    template: function NewsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1);\n        i0.ɵɵtext(2, \"Latest News\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 2);\n        i0.ɵɵtemplate(4, NewsComponent_a_4_Template, 17, 11, \"a\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, NewsComponent_div_5_Template, 3, 0, \"div\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.news)(\"ngForTrackBy\", ctx.trackByTitle);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.news.length === 0);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i1.TitleCasePipe, i1.DatePipe],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "item_r2", "link", "ɵɵsanitizeUrl", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "country", "source", "title", "description", "ɵɵpipeBind2", "published", "NewsComponent", "constructor", "news", "fetched_at", "trackByTitle", "index", "item", "_", "_2", "selectors", "decls", "vars", "consts", "template", "NewsComponent_Template", "rf", "ctx", "ɵɵtemplate", "NewsComponent_a_4_Template", "NewsComponent_div_5_Template", "length"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\news\\news.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\news\\news.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\ninterface NewsItem {\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string; // ISO string\n  description: string;\n  fetched_at: string;\n}\n\n@Component({\n  selector: 'app-news',\n  templateUrl: './news.component.html',\n  styleUrls: ['./news.component.scss']\n})\nexport class NewsComponent {\n  // Static news list\n  news: NewsItem[] = [\n    {\n      country: 'czech',\n      source: 'iDNES.cz',\n      title: 'Czech Republic strengthens ties with NATO allies',\n      link: 'https://idnes.cz/news/czech-nato-123',\n      published: '2025-08-07T10:30:00Z',\n      description: 'Prague announces new defense cooperation agreements with NATO members...',\n      fetched_at: '2025-08-07T11:30:00',\n    },\n    {\n      country: 'czech',\n      source: 'Czech Radio',\n      title: '<PERSON> discusses economic cooperation with Czech officials',\n      link: 'https://rozhlas.cz/putin-czech-456',\n      published: '2025-08-07T09:15:00Z',\n      description: 'Russian president meets with Czech representatives for trade talks...',\n      fetched_at: '2025-08-07T11:30:00',\n    },\n    {\n      country: 'russia',\n      source: 'RT',\n      title: '<PERSON> announces new economic initiatives',\n      link: 'https://rt.com/putin-economy-654',\n      published: '2025-08-07T12:00:00Z',\n      description: 'Russian president outlines strategic economic development plans...',\n      fetched_at: '2025-08-07T11:30:00',\n    },\n    {\n      country: 'russia',\n      source: 'TASS',\n      title: 'Ukraine situation discussed in Moscow summit',\n      link: 'https://tass.com/ukraine-summit-987',\n      published: '2025-08-07T11:30:00Z',\n      description: 'High-level meetings address regional security concerns...',\n      fetched_at: '2025-08-07T11:30:00',\n    },\n  ];\n\n  trackByTitle(index: number, item: NewsItem) {\n    return item.title;\n  }\n}\n", "<div class=\"news-wrapper\">\n  <h2 class=\"section-title\">Latest News</h2>\n\n  <div class=\"news-grid\">\n    <a\n      class=\"news-card\"\n      *ngFor=\"let item of news; trackBy: trackByTitle\"\n      [href]=\"item.link\"\n      target=\"_blank\"\n      rel=\"noopener noreferrer\"\n    >\n      <div class=\"news-card-header\">\n        <span class=\"badge\">{{ item.country | titlecase }}</span>\n        <span class=\"source\">{{ item.source }}</span>\n      </div>\n\n      <h3 class=\"title\">{{ item.title }}</h3>\n      <p class=\"description\">{{ item.description }}</p>\n\n      <div class=\"news-card-footer\">\n        <span class=\"published\">{{ item.published | date:'medium' }}</span>\n        <span class=\"cta\">Read →</span>\n      </div>\n    </a>\n  </div>\n\n  <div class=\"empty-state\" *ngIf=\"news.length === 0\">\n    <p>No news available.</p>\n  </div>\n</div>\n"], "mappings": ";;;;ICIIA,EAAA,CAAAC,cAAA,WAMC;IAEuBD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG/CH,EAAA,CAAAC,cAAA,YAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEjDH,EAAA,CAAAC,cAAA,eAA8B;IACJD,EAAA,CAAAE,MAAA,IAAoC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,mBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAdjCH,EAAA,CAAAI,UAAA,SAAAC,OAAA,CAAAC,IAAA,EAAAN,EAAA,CAAAO,aAAA,CAAkB;IAKIP,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAU,WAAA,OAAAL,OAAA,CAAAM,OAAA,EAA8B;IAC7BX,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAO,MAAA,CAAiB;IAGtBZ,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAQ,KAAA,CAAgB;IACXb,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAS,WAAA,CAAsB;IAGnBd,EAAA,CAAAQ,SAAA,GAAoC;IAApCR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAe,WAAA,QAAAV,OAAA,CAAAW,SAAA,YAAoC;;;;;IAMlEhB,EAAA,CAAAC,cAAA,cAAmD;IAC9CD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADV7B,OAAM,MAAOc,aAAa;EAL1BC,YAAA;IAME;IACA,KAAAC,IAAI,GAAe,CACjB;MACER,OAAO,EAAE,OAAO;MAChBC,MAAM,EAAE,UAAU;MAClBC,KAAK,EAAE,kDAAkD;MACzDP,IAAI,EAAE,sCAAsC;MAC5CU,SAAS,EAAE,sBAAsB;MACjCF,WAAW,EAAE,0EAA0E;MACvFM,UAAU,EAAE;KACb,EACD;MACET,OAAO,EAAE,OAAO;MAChBC,MAAM,EAAE,aAAa;MACrBC,KAAK,EAAE,2DAA2D;MAClEP,IAAI,EAAE,oCAAoC;MAC1CU,SAAS,EAAE,sBAAsB;MACjCF,WAAW,EAAE,uEAAuE;MACpFM,UAAU,EAAE;KACb,EACD;MACET,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,0CAA0C;MACjDP,IAAI,EAAE,kCAAkC;MACxCU,SAAS,EAAE,sBAAsB;MACjCF,WAAW,EAAE,oEAAoE;MACjFM,UAAU,EAAE;KACb,EACD;MACET,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,8CAA8C;MACrDP,IAAI,EAAE,qCAAqC;MAC3CU,SAAS,EAAE,sBAAsB;MACjCF,WAAW,EAAE,2DAA2D;MACxEM,UAAU,EAAE;KACb,CACF;;EAEDC,YAAYA,CAACC,KAAa,EAAEC,IAAc;IACxC,OAAOA,IAAI,CAACV,KAAK;EACnB;EAAC,QAAAW,CAAA,G;qBA3CUP,aAAa;EAAA;EAAA,QAAAQ,EAAA,G;UAAbR,aAAa;IAAAS,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCjB1BhC,EAAA,CAAAC,cAAA,aAA0B;QACED,EAAA,CAAAE,MAAA,kBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE1CH,EAAA,CAAAC,cAAA,aAAuB;QACrBD,EAAA,CAAAkC,UAAA,IAAAC,0BAAA,iBAmBI;QACNnC,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAkC,UAAA,IAAAE,4BAAA,iBAEM;QACRpC,EAAA,CAAAG,YAAA,EAAM;;;QAvBiBH,EAAA,CAAAQ,SAAA,GAAS;QAATR,EAAA,CAAAI,UAAA,YAAA6B,GAAA,CAAAd,IAAA,CAAS,iBAAAc,GAAA,CAAAZ,YAAA;QAoBJrB,EAAA,CAAAQ,SAAA,GAAuB;QAAvBR,EAAA,CAAAI,UAAA,SAAA6B,GAAA,CAAAd,IAAA,CAAAkB,MAAA,OAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}