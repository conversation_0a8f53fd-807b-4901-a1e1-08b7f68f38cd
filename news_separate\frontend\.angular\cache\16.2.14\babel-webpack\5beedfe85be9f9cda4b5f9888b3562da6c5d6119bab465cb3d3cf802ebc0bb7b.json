{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction NewsComponent_div_0_h2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\", 7);\n    i0.ɵɵtext(1, \"Latest News\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewsComponent_div_0_div_2_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"titlecase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const country_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", country_r8);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, country_r8), \" \");\n  }\n}\nfunction NewsComponent_div_0_div_2_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const source_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", source_r9);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", source_r9, \" \");\n  }\n}\nfunction NewsComponent_div_0_div_2_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function NewsComponent_div_0_div_2_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r10.clearFilters());\n    });\n    i0.ɵɵtext(1, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewsComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"input\", 10);\n    i0.ɵɵlistener(\"input\", function NewsComponent_div_0_div_2_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 11)(4, \"select\", 12);\n    i0.ɵɵlistener(\"change\", function NewsComponent_div_0_div_2_Template_select_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onCountryChange($event));\n    });\n    i0.ɵɵelementStart(5, \"option\", 13);\n    i0.ɵɵtext(6, \"All Countries\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, NewsComponent_div_0_div_2_option_7_Template, 3, 4, \"option\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"select\", 12);\n    i0.ɵɵlistener(\"change\", function NewsComponent_div_0_div_2_Template_select_change_8_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onSourceChange($event));\n    });\n    i0.ɵɵelementStart(9, \"option\", 13);\n    i0.ɵɵtext(10, \"All Sources\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, NewsComponent_div_0_div_2_option_11_Template, 2, 2, \"option\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, NewsComponent_div_0_div_2_button_12_Template, 2, 0, \"button\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.selectedCountry);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableCountries);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r2.selectedSource);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableSources);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm || ctx_r2.selectedCountry || ctx_r2.selectedSource);\n  }\n}\nfunction NewsComponent_div_0_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 18)(1, \"div\", 19)(2, \"span\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 21);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"h3\", 22);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"span\", 25);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 26);\n    i0.ɵɵtext(16, \"Read \\u2192\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"href\", item_r16.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 6, item_r16.country));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r16.source);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r16.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r16.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 8, item_r16.published, \"medium\"));\n  }\n}\nfunction NewsComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"p\");\n    i0.ɵɵtext(2, \"No articles found. Try adjusting your search or filters.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function NewsComponent_div_0_div_5_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.clearFilters());\n    });\n    i0.ɵɵtext(4, \" Clear All Filters \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"compact\": a0\n  };\n};\nfunction NewsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, NewsComponent_div_0_h2_1_Template, 2, 0, \"h2\", 2);\n    i0.ɵɵtemplate(2, NewsComponent_div_0_div_2_Template, 13, 6, \"div\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4);\n    i0.ɵɵtemplate(4, NewsComponent_div_0_a_4_Template, 17, 11, \"a\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, NewsComponent_div_0_div_5_Template, 5, 0, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c0, ctx_r0.compact));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showTitle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.compact);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredNews)(\"ngForTrackBy\", ctx_r0.trackByTitle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filteredNews.length === 0 && (ctx_r0.searchTerm || ctx_r0.selectedCountry || ctx_r0.selectedSource));\n  }\n}\nexport class NewsComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.showTitle = true;\n    this.compact = false;\n    // News data\n    this.news = [];\n    this.loading = false;\n    this.error = '';\n    // Filter properties\n    this.filteredNews = [];\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    // Available filter options\n    this.availableCountries = [];\n    this.availableSources = [];\n  }\n  ngOnInit() {\n    this.loadNews();\n  }\n  loadNews() {\n    this.loading = true;\n    this.error = '';\n    this.authService.ensureAuthenticated().subscribe({\n      next: () => {\n        this.authService.fetchNews('poland').subscribe({\n          next: response => {\n            this.news = response.news || [];\n            this.initializeFilters();\n            this.applyFilters();\n            this.loading = false;\n          },\n          error: error => {\n            this.error = 'Failed to load news';\n            this.loading = false;\n            console.error('Error loading news:', error);\n          }\n        });\n      },\n      error: error => {\n        this.error = 'Authentication failed';\n        this.loading = false;\n        console.error('Auth error:', error);\n      }\n    });\n  }\n  initializeFilters() {\n    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n  }\n  applyFilters() {\n    let filtered = [...this.news];\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(item => item.title.toLowerCase().includes(searchLower) || item.description.toLowerCase().includes(searchLower) || item.source.toLowerCase().includes(searchLower));\n    }\n    // Apply country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(item => item.country === this.selectedCountry);\n    }\n    // Apply source filter\n    if (this.selectedSource) {\n      filtered = filtered.filter(item => item.source === this.selectedSource);\n    }\n    this.filteredNews = filtered;\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n  onCountryChange(event) {\n    this.selectedCountry = event.target.value;\n    this.applyFilters();\n  }\n  onSourceChange(event) {\n    this.selectedSource = event.target.value;\n    this.applyFilters();\n  }\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.applyFilters();\n  }\n  trackByTitle(index, item) {\n    return item.title;\n  }\n  refreshNews() {\n    this.loadNews();\n  }\n  static #_ = this.ɵfac = function NewsComponent_Factory(t) {\n    return new (t || NewsComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NewsComponent,\n    selectors: [[\"app-news\"]],\n    inputs: {\n      showTitle: \"showTitle\",\n      compact: \"compact\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"news-wrapper\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"news-wrapper\", 3, \"ngClass\"], [\"class\", \"section-title\", 4, \"ngIf\"], [\"class\", \"news-controls\", 4, \"ngIf\"], [1, \"news-grid\"], [\"class\", \"news-card\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"section-title\"], [1, \"news-controls\"], [1, \"search-container\"], [\"type\", \"text\", \"placeholder\", \"Search news...\", 1, \"search-input\", 3, \"value\", \"input\"], [1, \"filter-controls\"], [1, \"filter-select\", 3, \"value\", \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"clear-filters-btn\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [3, \"value\"], [\"type\", \"button\", 1, \"clear-filters-btn\", 3, \"click\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"news-card\", 3, \"href\"], [1, \"news-card-header\"], [1, \"badge\"], [1, \"source\"], [1, \"title\"], [1, \"description\"], [1, \"news-card-footer\"], [1, \"published\"], [1, \"cta\"], [1, \"no-results\"]],\n    template: function NewsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NewsComponent_div_0_Template, 6, 8, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.news && ctx.news.length);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i2.TitleCasePipe, i2.DatePipe],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.news-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  line-height: 1.3;\\n  color: var(--color-gray-900);\\n  font-weight: var(--font-weight-semibold);\\n}\\n\\n.news-card[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n  color: var(--color-gray-600);\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%] {\\n  transition: all var(--transition-fast);\\n}\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: var(--shadow-md);\\n}\\n\\n\\n\\n.news-controls[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-6);\\n  padding: var(--space-4);\\n  background: var(--color-white);\\n  border: 1px solid var(--color-gray-200);\\n  border-radius: var(--radius-lg);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  padding: var(--space-3) var(--space-4);\\n  border: 2px solid var(--color-gray-200);\\n  border-radius: var(--radius-lg);\\n  font-size: var(--font-size-base);\\n  font-family: var(--font-family-primary);\\n  transition: border-color var(--transition-fast);\\n}\\n.search-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--color-primary);\\n}\\n.search-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--color-gray-500);\\n}\\n\\n.filter-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: var(--space-3);\\n  align-items: center;\\n}\\n\\n.filter-select[_ngcontent-%COMP%] {\\n  padding: var(--space-2) var(--space-3);\\n  border: 1px solid var(--color-gray-300);\\n  border-radius: var(--radius-md);\\n  font-size: var(--font-size-sm);\\n  background: var(--color-white);\\n  cursor: pointer;\\n  min-width: 120px;\\n}\\n.filter-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--color-primary);\\n}\\n\\n.clear-filters-btn[_ngcontent-%COMP%] {\\n  padding: var(--space-2) var(--space-4);\\n  background: var(--color-gray-100);\\n  border: 1px solid var(--color-gray-300);\\n  border-radius: var(--radius-md);\\n  font-size: var(--font-size-sm);\\n  color: var(--color-gray-700);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n.clear-filters-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-gray-200);\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: var(--space-8);\\n  color: var(--color-gray-600);\\n}\\n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filter-controls[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .filter-select[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "country_r8", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "source_r9", "ɵɵlistener", "NewsComponent_div_0_div_2_button_12_Template_button_click_0_listener", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "clearFilters", "NewsComponent_div_0_div_2_Template_input_input_2_listener", "$event", "_r13", "ctx_r12", "onSearchChange", "NewsComponent_div_0_div_2_Template_select_change_4_listener", "ctx_r14", "onCountryChange", "ɵɵtemplate", "NewsComponent_div_0_div_2_option_7_Template", "NewsComponent_div_0_div_2_Template_select_change_8_listener", "ctx_r15", "onSourceChange", "NewsComponent_div_0_div_2_option_11_Template", "NewsComponent_div_0_div_2_button_12_Template", "ctx_r2", "searchTerm", "selectedCountry", "availableCountries", "selectedSource", "availableSources", "item_r16", "link", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "country", "source", "title", "description", "ɵɵpipeBind2", "published", "NewsComponent_div_0_div_5_Template_button_click_3_listener", "_r18", "ctx_r17", "NewsComponent_div_0_h2_1_Template", "NewsComponent_div_0_div_2_Template", "NewsComponent_div_0_a_4_Template", "NewsComponent_div_0_div_5_Template", "ɵɵpureFunction1", "_c0", "ctx_r0", "compact", "showTitle", "filteredNews", "trackByTitle", "length", "NewsComponent", "constructor", "authService", "news", "loading", "error", "ngOnInit", "loadNews", "ensureAuthenticated", "subscribe", "next", "fetchNews", "response", "initializeFilters", "applyFilters", "console", "Set", "map", "item", "sort", "filtered", "trim", "searchLower", "toLowerCase", "filter", "includes", "event", "target", "value", "index", "refreshNews", "_", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "NewsComponent_Template", "rf", "ctx", "NewsComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\news\\news.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\news\\news.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';\nimport { AuthService } from '../auth.service';\n\nexport interface NewsItem {\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string; // ISO string\n  description: string;\n  fetched_at?: string;\n  // allow extra fields (e.g., id, created_at) from backend without strict typing\n  [key: string]: any;\n}\n\n@Component({\n  selector: 'app-news',\n  templateUrl: './news.component.html',\n  styleUrls: ['./news.component.scss']\n})\nexport class NewsComponent implements OnInit {\n  @Input() showTitle: boolean = true;\n  @Input() compact: boolean = false;\n\n  // News data\n  news: NewsItem[] = [];\n  loading: boolean = false;\n  error: string = '';\n\n  // Filter properties\n  filteredNews: NewsItem[] = [];\n  searchTerm: string = '';\n  selectedCountry: string = '';\n  selectedSource: string = '';\n\n  // Available filter options\n  availableCountries: string[] = [];\n  availableSources: string[] = [];\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit() {\n    this.loadNews();\n  }\n\n  loadNews() {\n    this.loading = true;\n    this.error = '';\n    \n    this.authService.ensureAuthenticated().subscribe({\n      next: () => {\n        this.authService.fetchNews('poland').subscribe({\n          next: (response) => {\n            this.news = response.news || [];\n            this.initializeFilters();\n            this.applyFilters();\n            this.loading = false;\n          },\n          error: (error) => {\n            this.error = 'Failed to load news';\n            this.loading = false;\n            console.error('Error loading news:', error);\n          }\n        });\n      },\n      error: (error) => {\n        this.error = 'Authentication failed';\n        this.loading = false;\n        console.error('Auth error:', error);\n      }\n    });\n  }\n\n  initializeFilters() {\n    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n  }\n\n  applyFilters() {\n    let filtered = [...this.news];\n\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(item =>\n        item.title.toLowerCase().includes(searchLower) ||\n        item.description.toLowerCase().includes(searchLower) ||\n        item.source.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(item => item.country === this.selectedCountry);\n    }\n\n    // Apply source filter\n    if (this.selectedSource) {\n      filtered = filtered.filter(item => item.source === this.selectedSource);\n    }\n\n    this.filteredNews = filtered;\n  }\n\n  onSearchChange(event: any) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n\n  onCountryChange(event: any) {\n    this.selectedCountry = event.target.value;\n    this.applyFilters();\n  }\n\n  onSourceChange(event: any) {\n    this.selectedSource = event.target.value;\n    this.applyFilters();\n  }\n\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.applyFilters();\n  }\n\n  trackByTitle(index: number, item: NewsItem) {\n    return item.title;\n  }\n\n  refreshNews() {\n    this.loadNews();\n  }\n}", "<div class=\"news-wrapper\" [ngClass]=\"{ 'compact': compact }\" *ngIf=\"news && news.length\">\n  <h2 class=\"section-title\" *ngIf=\"showTitle\">Latest News</h2>\n\n  <!-- Search and Filter Controls - Only show when not in compact mode -->\n  <div class=\"news-controls\" *ngIf=\"!compact\">\n    <!-- Search Bar -->\n    <div class=\"search-container\">\n      <input\n        type=\"text\"\n        class=\"search-input\"\n        placeholder=\"Search news...\"\n        [value]=\"searchTerm\"\n        (input)=\"onSearchChange($event)\"\n      />\n    </div>\n\n    <!-- Filter Controls -->\n    <div class=\"filter-controls\">\n      <select\n        class=\"filter-select\"\n        [value]=\"selectedCountry\"\n        (change)=\"onCountryChange($event)\"\n      >\n        <option value=\"\">All Countries</option>\n        <option *ngFor=\"let country of availableCountries\" [value]=\"country\">\n          {{ country | titlecase }}\n        </option>\n      </select>\n\n      <select\n        class=\"filter-select\"\n        [value]=\"selectedSource\"\n        (change)=\"onSourceChange($event)\"\n      >\n        <option value=\"\">All Sources</option>\n        <option *ngFor=\"let source of availableSources\" [value]=\"source\">\n          {{ source }}\n        </option>\n      </select>\n\n      <button\n        class=\"clear-filters-btn\"\n        (click)=\"clearFilters()\"\n        type=\"button\"\n        *ngIf=\"searchTerm || selectedCountry || selectedSource\"\n      >\n        Clear Filters\n      </button>\n    </div>\n  </div>\n\n  <div class=\"news-grid\">\n    <a\n      class=\"news-card\"\n      *ngFor=\"let item of filteredNews; trackBy: trackByTitle\"\n      [href]=\"item.link\"\n      target=\"_blank\"\n      rel=\"noopener noreferrer\"\n    >\n      <div class=\"news-card-header\">\n        <span class=\"badge\">{{ item.country | titlecase }}</span>\n        <span class=\"source\">{{ item.source }}</span>\n      </div>\n\n      <h3 class=\"title\">{{ item.title }}</h3>\n      <p class=\"description\">{{ item.description }}</p>\n\n      <div class=\"news-card-footer\">\n        <span class=\"published\">{{ item.published | date:'medium' }}</span>\n        <span class=\"cta\">Read →</span>\n      </div>\n    </a>\n  </div>\n\n  <!-- No Results Message -->\n  <div class=\"no-results\" *ngIf=\"filteredNews.length === 0 && (searchTerm || selectedCountry || selectedSource)\">\n    <p>No articles found. Try adjusting your search or filters.</p>\n    <button class=\"clear-filters-btn\" (click)=\"clearFilters()\" type=\"button\">\n      Clear All Filters\n    </button>\n  </div>\n</div>\n"], "mappings": ";;;;;;ICCEA,EAAA,CAAAC,cAAA,YAA4C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAuBtDH,EAAA,CAAAC,cAAA,iBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF0CH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAiB;IAClEL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAQ,WAAA,OAAAH,UAAA,OACF;;;;;IASAL,EAAA,CAAAC,cAAA,iBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAK,SAAA,CAAgB;IAC9DT,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,SAAA,MACF;;;;;;IAGFT,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAU,UAAA,mBAAAC,qEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,OAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAIxBjB,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA3CbH,EAAA,CAAAC,cAAA,aAA4C;IAQtCD,EAAA,CAAAU,UAAA,mBAAAQ,0DAAAC,MAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAK,OAAA,CAAAC,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IALlCnB,EAAA,CAAAG,YAAA,EAME;IAIJH,EAAA,CAAAC,cAAA,cAA6B;IAIzBD,EAAA,CAAAU,UAAA,oBAAAa,4DAAAJ,MAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,IAAA;MAAA,MAAAI,OAAA,GAAAxB,EAAA,CAAAe,aAAA;MAAA,OAAUf,EAAA,CAAAgB,WAAA,CAAAQ,OAAA,CAAAC,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC;IAElCnB,EAAA,CAAAC,cAAA,iBAAiB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAA0B,UAAA,IAAAC,2CAAA,qBAES;IACX3B,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAU,UAAA,oBAAAkB,4DAAAT,MAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,IAAA;MAAA,MAAAS,OAAA,GAAA7B,EAAA,CAAAe,aAAA;MAAA,OAAUf,EAAA,CAAAgB,WAAA,CAAAa,OAAA,CAAAC,cAAA,CAAAX,MAAA,CAAsB;IAAA,EAAC;IAEjCnB,EAAA,CAAAC,cAAA,iBAAiB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAA0B,UAAA,KAAAK,4CAAA,qBAES;IACX/B,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAA0B,UAAA,KAAAM,4CAAA,qBAOS;IACXhC,EAAA,CAAAG,YAAA,EAAM;;;;IArCFH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,UAAA6B,MAAA,CAAAC,UAAA,CAAoB;IASpBlC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,UAAA6B,MAAA,CAAAE,eAAA,CAAyB;IAIGnC,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAI,UAAA,YAAA6B,MAAA,CAAAG,kBAAA,CAAqB;IAOjDpC,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,UAAA6B,MAAA,CAAAI,cAAA,CAAwB;IAIGrC,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAA6B,MAAA,CAAAK,gBAAA,CAAmB;IAS7CtC,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAI,UAAA,SAAA6B,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAE,eAAA,IAAAF,MAAA,CAAAI,cAAA,CAAqD;;;;;IAQ1DrC,EAAA,CAAAC,cAAA,YAMC;IAEuBD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG/CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEjDH,EAAA,CAAAC,cAAA,eAA8B;IACJD,EAAA,CAAAE,MAAA,IAAoC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,mBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAdjCH,EAAA,CAAAI,UAAA,SAAAmC,QAAA,CAAAC,IAAA,EAAAxC,EAAA,CAAAyC,aAAA,CAAkB;IAKIzC,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAAQ,WAAA,OAAA+B,QAAA,CAAAI,OAAA,EAA8B;IAC7B3C,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAA0C,iBAAA,CAAAH,QAAA,CAAAK,MAAA,CAAiB;IAGtB5C,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAA0C,iBAAA,CAAAH,QAAA,CAAAM,KAAA,CAAgB;IACX7C,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAA0C,iBAAA,CAAAH,QAAA,CAAAO,WAAA,CAAsB;IAGnB9C,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA+C,WAAA,QAAAR,QAAA,CAAAS,SAAA,YAAoC;;;;;;IAOlEhD,EAAA,CAAAC,cAAA,cAA+G;IAC1GD,EAAA,CAAAE,MAAA,+DAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/DH,EAAA,CAAAC,cAAA,iBAAyE;IAAvCD,EAAA,CAAAU,UAAA,mBAAAuC,2DAAA;MAAAjD,EAAA,CAAAY,aAAA,CAAAsC,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAmC,OAAA,CAAAlC,YAAA,EAAc;IAAA,EAAC;IACxDjB,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;IA/EbH,EAAA,CAAAC,cAAA,aAAyF;IACvFD,EAAA,CAAA0B,UAAA,IAAA0B,iCAAA,gBAA4D;IAG5DpD,EAAA,CAAA0B,UAAA,IAAA2B,kCAAA,kBA6CM;IAENrD,EAAA,CAAAC,cAAA,aAAuB;IACrBD,EAAA,CAAA0B,UAAA,IAAA4B,gCAAA,iBAmBI;IACNtD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA0B,UAAA,IAAA6B,kCAAA,iBAKM;IACRvD,EAAA,CAAAG,YAAA,EAAM;;;;IAjFoBH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAwD,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,OAAA,EAAkC;IAC/B3D,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAI,UAAA,SAAAsD,MAAA,CAAAE,SAAA,CAAe;IAGd5D,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,UAAAsD,MAAA,CAAAC,OAAA,CAAc;IAkDrB3D,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAI,UAAA,YAAAsD,MAAA,CAAAG,YAAA,CAAiB,iBAAAH,MAAA,CAAAI,YAAA;IAqBb9D,EAAA,CAAAM,SAAA,GAAoF;IAApFN,EAAA,CAAAI,UAAA,SAAAsD,MAAA,CAAAG,YAAA,CAAAE,MAAA,WAAAL,MAAA,CAAAxB,UAAA,IAAAwB,MAAA,CAAAvB,eAAA,IAAAuB,MAAA,CAAArB,cAAA,EAAoF;;;ADvD/G,OAAM,MAAO2B,aAAa;EAmBxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAlBtB,KAAAN,SAAS,GAAY,IAAI;IACzB,KAAAD,OAAO,GAAY,KAAK;IAEjC;IACA,KAAAQ,IAAI,GAAe,EAAE;IACrB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,KAAK,GAAW,EAAE;IAElB;IACA,KAAAR,YAAY,GAAe,EAAE;IAC7B,KAAA3B,UAAU,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAE,cAAc,GAAW,EAAE;IAE3B;IACA,KAAAD,kBAAkB,GAAa,EAAE;IACjC,KAAAE,gBAAgB,GAAa,EAAE;EAEgB;EAE/CgC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAA,QAAQA,CAAA;IACN,IAAI,CAACH,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,EAAE;IAEf,IAAI,CAACH,WAAW,CAACM,mBAAmB,EAAE,CAACC,SAAS,CAAC;MAC/CC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACR,WAAW,CAACS,SAAS,CAAC,QAAQ,CAAC,CAACF,SAAS,CAAC;UAC7CC,IAAI,EAAGE,QAAQ,IAAI;YACjB,IAAI,CAACT,IAAI,GAAGS,QAAQ,CAACT,IAAI,IAAI,EAAE;YAC/B,IAAI,CAACU,iBAAiB,EAAE;YACxB,IAAI,CAACC,YAAY,EAAE;YACnB,IAAI,CAACV,OAAO,GAAG,KAAK;UACtB,CAAC;UACDC,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACA,KAAK,GAAG,qBAAqB;YAClC,IAAI,CAACD,OAAO,GAAG,KAAK;YACpBW,OAAO,CAACV,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;UAC7C;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,uBAAuB;QACpC,IAAI,CAACD,OAAO,GAAG,KAAK;QACpBW,OAAO,CAACV,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;KACD,CAAC;EACJ;EAEAQ,iBAAiBA,CAAA;IACf,IAAI,CAACzC,kBAAkB,GAAG,CAAC,GAAG,IAAI4C,GAAG,CAAC,IAAI,CAACb,IAAI,CAACc,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACvC,OAAO,CAAC,CAAC,CAAC,CAACwC,IAAI,EAAE;IAClF,IAAI,CAAC7C,gBAAgB,GAAG,CAAC,GAAG,IAAI0C,GAAG,CAAC,IAAI,CAACb,IAAI,CAACc,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACtC,MAAM,CAAC,CAAC,CAAC,CAACuC,IAAI,EAAE;EACjF;EAEAL,YAAYA,CAAA;IACV,IAAIM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACjB,IAAI,CAAC;IAE7B;IACA,IAAI,IAAI,CAACjC,UAAU,CAACmD,IAAI,EAAE,EAAE;MAC1B,MAAMC,WAAW,GAAG,IAAI,CAACpD,UAAU,CAACqD,WAAW,EAAE;MACjDH,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACN,IAAI,IAC7BA,IAAI,CAACrC,KAAK,CAAC0C,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC9CJ,IAAI,CAACpC,WAAW,CAACyC,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IACpDJ,IAAI,CAACtC,MAAM,CAAC2C,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CAChD;;IAGH;IACA,IAAI,IAAI,CAACnD,eAAe,EAAE;MACxBiD,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACvC,OAAO,KAAK,IAAI,CAACR,eAAe,CAAC;;IAG3E;IACA,IAAI,IAAI,CAACE,cAAc,EAAE;MACvB+C,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACtC,MAAM,KAAK,IAAI,CAACP,cAAc,CAAC;;IAGzE,IAAI,CAACwB,YAAY,GAAGuB,QAAQ;EAC9B;EAEA9D,cAAcA,CAACoE,KAAU;IACvB,IAAI,CAACxD,UAAU,GAAGwD,KAAK,CAACC,MAAM,CAACC,KAAK;IACpC,IAAI,CAACd,YAAY,EAAE;EACrB;EAEArD,eAAeA,CAACiE,KAAU;IACxB,IAAI,CAACvD,eAAe,GAAGuD,KAAK,CAACC,MAAM,CAACC,KAAK;IACzC,IAAI,CAACd,YAAY,EAAE;EACrB;EAEAhD,cAAcA,CAAC4D,KAAU;IACvB,IAAI,CAACrD,cAAc,GAAGqD,KAAK,CAACC,MAAM,CAACC,KAAK;IACxC,IAAI,CAACd,YAAY,EAAE;EACrB;EAEA7D,YAAYA,CAAA;IACV,IAAI,CAACiB,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACyC,YAAY,EAAE;EACrB;EAEAhB,YAAYA,CAAC+B,KAAa,EAAEX,IAAc;IACxC,OAAOA,IAAI,CAACrC,KAAK;EACnB;EAEAiD,WAAWA,CAAA;IACT,IAAI,CAACvB,QAAQ,EAAE;EACjB;EAAC,QAAAwB,CAAA,G;qBAhHU/B,aAAa,EAAAhE,EAAA,CAAAgG,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAbnC,aAAa;IAAAoC,SAAA;IAAAC,MAAA;MAAAzC,SAAA;MAAAD,OAAA;IAAA;IAAA2C,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCpB1B3G,EAAA,CAAA0B,UAAA,IAAAmF,4BAAA,iBAiFM;;;QAjFwD7G,EAAA,CAAAI,UAAA,SAAAwG,GAAA,CAAAzC,IAAA,IAAAyC,GAAA,CAAAzC,IAAA,CAAAJ,MAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}