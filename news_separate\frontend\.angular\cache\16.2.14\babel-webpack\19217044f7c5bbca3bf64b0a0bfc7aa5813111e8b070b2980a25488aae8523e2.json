{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction NewsComponent_div_0_h2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\", 5);\n    i0.ɵɵtext(1, \"Latest News\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewsComponent_div_0_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 6)(1, \"div\", 7)(2, \"span\", 8);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 9);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"h3\", 10);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 11);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 12)(12, \"span\", 13);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 14);\n    i0.ɵɵtext(16, \"Read \\u2192\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"href\", item_r3.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 6, item_r3.country));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.source);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 8, item_r3.published, \"medium\"));\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"compact\": a0\n  };\n};\nfunction NewsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, NewsComponent_div_0_h2_1_Template, 2, 0, \"h2\", 2);\n    i0.ɵɵelementStart(2, \"div\", 3);\n    i0.ɵɵtemplate(3, NewsComponent_div_0_a_3_Template, 17, 11, \"a\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r0.compact));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showTitle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.news)(\"ngForTrackBy\", ctx_r0.trackByTitle);\n  }\n}\nexport class NewsComponent {\n  constructor() {\n    this.news = [];\n    this.showTitle = true;\n    this.compact = false;\n    this.showFilters = true; // New input to control filter visibility\n    // Filter and search properties\n    this.filteredNews = [];\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.sortBy = 'date'; // 'date', 'title', 'source'\n    this.sortOrder = 'desc'; // 'asc', 'desc'\n    // Available filter options\n    this.availableCountries = [];\n    this.availableSources = [];\n  }\n  ngOnInit() {\n    this.initializeFilters();\n    this.applyFilters();\n  }\n  ngOnChanges(changes) {\n    if (changes['news']) {\n      this.initializeFilters();\n      this.applyFilters();\n    }\n  }\n  initializeFilters() {\n    // Extract unique countries and sources\n    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n  }\n  applyFilters() {\n    let filtered = [...this.news];\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(item => item.title.toLowerCase().includes(searchLower) || item.description.toLowerCase().includes(searchLower) || item.source.toLowerCase().includes(searchLower));\n    }\n    // Apply country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(item => item.country === this.selectedCountry);\n    }\n    // Apply source filter\n    if (this.selectedSource) {\n      filtered = filtered.filter(item => item.source === this.selectedSource);\n    }\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let comparison = 0;\n      switch (this.sortBy) {\n        case 'date':\n          comparison = new Date(a.published).getTime() - new Date(b.published).getTime();\n          break;\n        case 'title':\n          comparison = a.title.localeCompare(b.title);\n          break;\n        case 'source':\n          comparison = a.source.localeCompare(b.source);\n          break;\n      }\n      return this.sortOrder === 'desc' ? -comparison : comparison;\n    });\n    this.filteredNews = filtered;\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n  onCountryChange(event) {\n    this.selectedCountry = event.target.value;\n    this.applyFilters();\n  }\n  onSourceChange(event) {\n    this.selectedSource = event.target.value;\n    this.applyFilters();\n  }\n  onSortChange(event) {\n    const [sortBy, sortOrder] = event.target.value.split('-');\n    this.sortBy = sortBy;\n    this.sortOrder = sortOrder;\n    this.applyFilters();\n  }\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.sortBy = 'date';\n    this.sortOrder = 'desc';\n    this.applyFilters();\n  }\n  trackByTitle(index, item) {\n    return item.title;\n  }\n  static #_ = this.ɵfac = function NewsComponent_Factory(t) {\n    return new (t || NewsComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NewsComponent,\n    selectors: [[\"app-news\"]],\n    inputs: {\n      news: \"news\",\n      showTitle: \"showTitle\",\n      compact: \"compact\",\n      showFilters: \"showFilters\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"news-wrapper\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"news-wrapper\", 3, \"ngClass\"], [\"class\", \"section-title\", 4, \"ngIf\"], [1, \"news-grid\"], [\"class\", \"news-card\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"section-title\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"news-card\", 3, \"href\"], [1, \"news-card-header\"], [1, \"badge\"], [1, \"source\"], [1, \"title\"], [1, \"description\"], [1, \"news-card-footer\"], [1, \"published\"], [1, \"cta\"]],\n    template: function NewsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NewsComponent_div_0_Template, 4, 6, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.news && ctx.news.length);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.TitleCasePipe, i1.DatePipe],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n\\n\\n.news-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  line-height: 1.3;\\n  color: var(--color-gray-900);\\n  font-weight: var(--font-weight-semibold);\\n}\\n\\n.news-card[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n  color: var(--color-gray-600);\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%] {\\n  transition: all var(--transition-fast);\\n}\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: var(--shadow-md);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbmV3cy9uZXdzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsY0FBQTtBQUNGOztBQUVBLGlGQUFBO0FBQ0E7RUFDRSxnQkFBQTtFQUNBLDRCQUFBO0VBQ0Esd0NBQUE7QUFDRjs7QUFFQTtFQUNFLG9CQUFBO0VBQ0EscUJBQUE7RUFDQSw0QkFBQTtFQUNBLGdCQUFBO0VBQ0EsNEJBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUVBLGtEQUFBO0FBQ0E7RUFDRSxzQ0FBQTtBQUNGO0FBQ0U7RUFDRSwyQkFBQTtFQUNBLDRCQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbn1cclxuXHJcbi8qIEtlZXAgY29tcG9uZW50LXNwZWNpZmljIG92ZXJyaWRlcyBtaW5pbWFsOyBtb3N0IHN0eWxlcyBpbiBnbG9iYWwgc3R5bGVzLnNjc3MgKi9cclxuLm5ld3MtY2FyZCAudGl0bGUge1xyXG4gIGxpbmUtaGVpZ2h0OiAxLjM7XHJcbiAgY29sb3I6IHZhcigtLWNvbG9yLWdyYXktOTAwKTtcclxuICBmb250LXdlaWdodDogdmFyKC0tZm9udC13ZWlnaHQtc2VtaWJvbGQpO1xyXG59XHJcblxyXG4ubmV3cy1jYXJkIC5kZXNjcmlwdGlvbiB7XHJcbiAgZGlzcGxheTogLXdlYmtpdC1ib3g7XHJcbiAgLXdlYmtpdC1saW5lLWNsYW1wOiAzO1xyXG4gIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICBjb2xvcjogdmFyKC0tY29sb3ItZ3JheS02MDApO1xyXG4gIGxpbmUtaGVpZ2h0OiAxLjU7XHJcbn1cclxuXHJcbi8qIEVuaGFuY2VkIGhvdmVyIGVmZmVjdHMgZm9yIG5ld3MgY2FyZHMgaW4gY2hhdCAqL1xyXG4ubmV3cy13cmFwcGVyLmNvbXBhY3QgLm5ld3MtY2FyZCB7XHJcbiAgdHJhbnNpdGlvbjogYWxsIHZhcigtLXRyYW5zaXRpb24tZmFzdCk7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xyXG4gICAgYm94LXNoYWRvdzogdmFyKC0tc2hhZG93LW1kKTtcclxuICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "item_r3", "link", "ɵɵsanitizeUrl", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "country", "source", "title", "description", "ɵɵpipeBind2", "published", "ɵɵtemplate", "NewsComponent_div_0_h2_1_Template", "NewsComponent_div_0_a_3_Template", "ɵɵpureFunction1", "_c0", "ctx_r0", "compact", "showTitle", "news", "trackByTitle", "NewsComponent", "constructor", "showFilters", "filteredNews", "searchTerm", "selectedCountry", "selectedSource", "sortBy", "sortOrder", "availableCountries", "availableSources", "ngOnInit", "initializeFilters", "applyFilters", "ngOnChanges", "changes", "Set", "map", "item", "sort", "filtered", "trim", "searchLower", "toLowerCase", "filter", "includes", "a", "b", "comparison", "Date", "getTime", "localeCompare", "onSearchChange", "event", "target", "value", "onCountryChange", "onSourceChange", "onSortChange", "split", "clearFilters", "index", "_", "_2", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "NewsComponent_Template", "rf", "ctx", "NewsComponent_div_0_Template", "length"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\news\\news.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\frontend\\src\\app\\news\\news.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';\n\nexport interface NewsItem {\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string; // ISO string\n  description: string;\n  fetched_at?: string;\n  // allow extra fields (e.g., id, created_at) from backend without strict typing\n  [key: string]: any;\n}\n\n@Component({\n  selector: 'app-news',\n  templateUrl: './news.component.html',\n  styleUrls: ['./news.component.scss']\n})\nexport class NewsComponent implements OnInit, OnChanges {\n  @Input() news: NewsItem[] = [];\n  @Input() showTitle: boolean = true;\n  @Input() compact: boolean = false;\n  @Input() showFilters: boolean = true; // New input to control filter visibility\n\n  // Filter and search properties\n  filteredNews: NewsItem[] = [];\n  searchTerm: string = '';\n  selectedCountry: string = '';\n  selectedSource: string = '';\n  sortBy: string = 'date'; // 'date', 'title', 'source'\n  sortOrder: string = 'desc'; // 'asc', 'desc'\n\n  // Available filter options\n  availableCountries: string[] = [];\n  availableSources: string[] = [];\n\n  ngOnInit() {\n    this.initializeFilters();\n    this.applyFilters();\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (changes['news']) {\n      this.initializeFilters();\n      this.applyFilters();\n    }\n  }\n\n  initializeFilters() {\n    // Extract unique countries and sources\n    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n  }\n\n  applyFilters() {\n    let filtered = [...this.news];\n\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(item =>\n        item.title.toLowerCase().includes(searchLower) ||\n        item.description.toLowerCase().includes(searchLower) ||\n        item.source.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(item => item.country === this.selectedCountry);\n    }\n\n    // Apply source filter\n    if (this.selectedSource) {\n      filtered = filtered.filter(item => item.source === this.selectedSource);\n    }\n\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let comparison = 0;\n\n      switch (this.sortBy) {\n        case 'date':\n          comparison = new Date(a.published).getTime() - new Date(b.published).getTime();\n          break;\n        case 'title':\n          comparison = a.title.localeCompare(b.title);\n          break;\n        case 'source':\n          comparison = a.source.localeCompare(b.source);\n          break;\n      }\n\n      return this.sortOrder === 'desc' ? -comparison : comparison;\n    });\n\n    this.filteredNews = filtered;\n  }\n\n  onSearchChange(event: any) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n\n  onCountryChange(event: any) {\n    this.selectedCountry = event.target.value;\n    this.applyFilters();\n  }\n\n  onSourceChange(event: any) {\n    this.selectedSource = event.target.value;\n    this.applyFilters();\n  }\n\n  onSortChange(event: any) {\n    const [sortBy, sortOrder] = event.target.value.split('-');\n    this.sortBy = sortBy;\n    this.sortOrder = sortOrder;\n    this.applyFilters();\n  }\n\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.sortBy = 'date';\n    this.sortOrder = 'desc';\n    this.applyFilters();\n  }\n\n  trackByTitle(index: number, item: NewsItem) {\n    return item.title;\n  }\n}\n", "<div class=\"news-wrapper\" [ngClass]=\"{ 'compact': compact }\" *ngIf=\"news && news.length\">\n  <h2 class=\"section-title\" *ngIf=\"showTitle\">Latest News</h2>\n\n  <div class=\"news-grid\">\n    <a\n      class=\"news-card\"\n      *ngFor=\"let item of news; trackBy: trackByTitle\"\n      [href]=\"item.link\"\n      target=\"_blank\"\n      rel=\"noopener noreferrer\"\n    >\n      <div class=\"news-card-header\">\n        <span class=\"badge\">{{ item.country | titlecase }}</span>\n        <span class=\"source\">{{ item.source }}</span>\n      </div>\n\n      <h3 class=\"title\">{{ item.title }}</h3>\n      <p class=\"description\">{{ item.description }}</p>\n\n      <div class=\"news-card-footer\">\n        <span class=\"published\">{{ item.published | date:'medium' }}</span>\n        <span class=\"cta\">Read →</span>\n      </div>\n    </a>\n  </div>\n</div>\n"], "mappings": ";;;;ICCEA,EAAA,CAAAC,cAAA,YAA4C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG1DH,EAAA,CAAAC,cAAA,WAMC;IAEuBD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG/CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEjDH,EAAA,CAAAC,cAAA,eAA8B;IACJD,EAAA,CAAAE,MAAA,IAAoC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,mBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAdjCH,EAAA,CAAAI,UAAA,SAAAC,OAAA,CAAAC,IAAA,EAAAN,EAAA,CAAAO,aAAA,CAAkB;IAKIP,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAU,WAAA,OAAAL,OAAA,CAAAM,OAAA,EAA8B;IAC7BX,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAO,MAAA,CAAiB;IAGtBZ,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAQ,KAAA,CAAgB;IACXb,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAS,WAAA,CAAsB;IAGnBd,EAAA,CAAAQ,SAAA,GAAoC;IAApCR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAe,WAAA,QAAAV,OAAA,CAAAW,SAAA,YAAoC;;;;;;;;;;IApBpEhB,EAAA,CAAAC,cAAA,aAAyF;IACvFD,EAAA,CAAAiB,UAAA,IAAAC,iCAAA,gBAA4D;IAE5DlB,EAAA,CAAAC,cAAA,aAAuB;IACrBD,EAAA,CAAAiB,UAAA,IAAAE,gCAAA,iBAmBI;IACNnB,EAAA,CAAAG,YAAA,EAAM;;;;IAxBkBH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAoB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,OAAA,EAAkC;IAC/BvB,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAI,UAAA,SAAAkB,MAAA,CAAAE,SAAA,CAAe;IAKrBxB,EAAA,CAAAQ,SAAA,GAAS;IAATR,EAAA,CAAAI,UAAA,YAAAkB,MAAA,CAAAG,IAAA,CAAS,iBAAAH,MAAA,CAAAI,YAAA;;;ADahC,OAAM,MAAOC,aAAa;EAL1BC,YAAA;IAMW,KAAAH,IAAI,GAAe,EAAE;IACrB,KAAAD,SAAS,GAAY,IAAI;IACzB,KAAAD,OAAO,GAAY,KAAK;IACxB,KAAAM,WAAW,GAAY,IAAI,CAAC,CAAC;IAEtC;IACA,KAAAC,YAAY,GAAe,EAAE;IAC7B,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,MAAM,GAAW,MAAM,CAAC,CAAC;IACzB,KAAAC,SAAS,GAAW,MAAM,CAAC,CAAC;IAE5B;IACA,KAAAC,kBAAkB,GAAa,EAAE;IACjC,KAAAC,gBAAgB,GAAa,EAAE;;EAE/BC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,MAAM,CAAC,EAAE;MACnB,IAAI,CAACH,iBAAiB,EAAE;MACxB,IAAI,CAACC,YAAY,EAAE;;EAEvB;EAEAD,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACH,kBAAkB,GAAG,CAAC,GAAG,IAAIO,GAAG,CAAC,IAAI,CAAClB,IAAI,CAACmB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAClC,OAAO,CAAC,CAAC,CAAC,CAACmC,IAAI,EAAE;IAClF,IAAI,CAACT,gBAAgB,GAAG,CAAC,GAAG,IAAIM,GAAG,CAAC,IAAI,CAAClB,IAAI,CAACmB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACjC,MAAM,CAAC,CAAC,CAAC,CAACkC,IAAI,EAAE;EACjF;EAEAN,YAAYA,CAAA;IACV,IAAIO,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACtB,IAAI,CAAC;IAE7B;IACA,IAAI,IAAI,CAACM,UAAU,CAACiB,IAAI,EAAE,EAAE;MAC1B,MAAMC,WAAW,GAAG,IAAI,CAAClB,UAAU,CAACmB,WAAW,EAAE;MACjDH,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACN,IAAI,IAC7BA,IAAI,CAAChC,KAAK,CAACqC,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IAC9CJ,IAAI,CAAC/B,WAAW,CAACoC,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,IACpDJ,IAAI,CAACjC,MAAM,CAACsC,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CAChD;;IAGH;IACA,IAAI,IAAI,CAACjB,eAAe,EAAE;MACxBe,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACN,IAAI,IAAIA,IAAI,CAAClC,OAAO,KAAK,IAAI,CAACqB,eAAe,CAAC;;IAG3E;IACA,IAAI,IAAI,CAACC,cAAc,EAAE;MACvBc,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACjC,MAAM,KAAK,IAAI,CAACqB,cAAc,CAAC;;IAGzE;IACAc,QAAQ,CAACD,IAAI,CAAC,CAACO,CAAC,EAAEC,CAAC,KAAI;MACrB,IAAIC,UAAU,GAAG,CAAC;MAElB,QAAQ,IAAI,CAACrB,MAAM;QACjB,KAAK,MAAM;UACTqB,UAAU,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACrC,SAAS,CAAC,CAACyC,OAAO,EAAE,GAAG,IAAID,IAAI,CAACF,CAAC,CAACtC,SAAS,CAAC,CAACyC,OAAO,EAAE;UAC9E;QACF,KAAK,OAAO;UACVF,UAAU,GAAGF,CAAC,CAACxC,KAAK,CAAC6C,aAAa,CAACJ,CAAC,CAACzC,KAAK,CAAC;UAC3C;QACF,KAAK,QAAQ;UACX0C,UAAU,GAAGF,CAAC,CAACzC,MAAM,CAAC8C,aAAa,CAACJ,CAAC,CAAC1C,MAAM,CAAC;UAC7C;;MAGJ,OAAO,IAAI,CAACuB,SAAS,KAAK,MAAM,GAAG,CAACoB,UAAU,GAAGA,UAAU;IAC7D,CAAC,CAAC;IAEF,IAAI,CAACzB,YAAY,GAAGiB,QAAQ;EAC9B;EAEAY,cAAcA,CAACC,KAAU;IACvB,IAAI,CAAC7B,UAAU,GAAG6B,KAAK,CAACC,MAAM,CAACC,KAAK;IACpC,IAAI,CAACtB,YAAY,EAAE;EACrB;EAEAuB,eAAeA,CAACH,KAAU;IACxB,IAAI,CAAC5B,eAAe,GAAG4B,KAAK,CAACC,MAAM,CAACC,KAAK;IACzC,IAAI,CAACtB,YAAY,EAAE;EACrB;EAEAwB,cAAcA,CAACJ,KAAU;IACvB,IAAI,CAAC3B,cAAc,GAAG2B,KAAK,CAACC,MAAM,CAACC,KAAK;IACxC,IAAI,CAACtB,YAAY,EAAE;EACrB;EAEAyB,YAAYA,CAACL,KAAU;IACrB,MAAM,CAAC1B,MAAM,EAAEC,SAAS,CAAC,GAAGyB,KAAK,CAACC,MAAM,CAACC,KAAK,CAACI,KAAK,CAAC,GAAG,CAAC;IACzD,IAAI,CAAChC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACK,YAAY,EAAE;EACrB;EAEA2B,YAAYA,CAAA;IACV,IAAI,CAACpC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,MAAM,GAAG,MAAM;IACpB,IAAI,CAACC,SAAS,GAAG,MAAM;IACvB,IAAI,CAACK,YAAY,EAAE;EACrB;EAEAd,YAAYA,CAAC0C,KAAa,EAAEvB,IAAc;IACxC,OAAOA,IAAI,CAAChC,KAAK;EACnB;EAAC,QAAAwD,CAAA,G;qBAlHU1C,aAAa;EAAA;EAAA,QAAA2C,EAAA,G;UAAb3C,aAAa;IAAA4C,SAAA;IAAAC,MAAA;MAAA/C,IAAA;MAAAD,SAAA;MAAAD,OAAA;MAAAM,WAAA;IAAA;IAAA4C,QAAA,GAAAzE,EAAA,CAAA0E,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnB1BhF,EAAA,CAAAiB,UAAA,IAAAiE,4BAAA,iBAyBM;;;QAzBwDlF,EAAA,CAAAI,UAAA,SAAA6E,GAAA,CAAAxD,IAAA,IAAAwD,GAAA,CAAAxD,IAAA,CAAA0D,MAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}