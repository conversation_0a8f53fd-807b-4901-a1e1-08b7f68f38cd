{"ast": null, "code": "'use strict';\n\n/**\n * @license Angular v<unknown>\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n(function (global) {\n  const performance = global['performance'];\n  function mark(name) {\n    performance && performance['mark'] && performance['mark'](name);\n  }\n  function performanceMeasure(name, label) {\n    performance && performance['measure'] && performance['measure'](name, label);\n  }\n  mark('Zone');\n  // Initialize before it's accessed below.\n  // __Zone_symbol_prefix global can be used to override the default zone\n  // symbol prefix with a custom one if needed.\n  const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n  function __symbol__(name) {\n    return symbolPrefix + name;\n  }\n  const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n  if (global['Zone']) {\n    // if global['Zone'] already exists (maybe zone.js was already loaded or\n    // some other lib also registered a global object named Zone), we may need\n    // to throw an error, but sometimes user may not want this error.\n    // For example,\n    // we have two web pages, page1 includes zone.js, page2 doesn't.\n    // and the 1st time user load page1 and page2, everything work fine,\n    // but when user load page2 again, error occurs because global['Zone'] already exists.\n    // so we add a flag to let user choose whether to throw this error or not.\n    // By default, if existing Zone is from zone.js, we will not throw the error.\n    if (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function') {\n      throw new Error('Zone already loaded.');\n    } else {\n      return global['Zone'];\n    }\n  }\n  class Zone {\n    // tslint:disable-next-line:require-internal-with-underscore\n    static #_ = this.__symbol__ = __symbol__;\n    static assertZonePatched() {\n      if (global['Promise'] !== patches['ZoneAwarePromise']) {\n        throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' + 'has been overwritten.\\n' + 'Most likely cause is that a Promise polyfill has been loaded ' + 'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' + 'If you must load one, do so before loading zone.js.)');\n      }\n    }\n    static get root() {\n      let zone = Zone.current;\n      while (zone.parent) {\n        zone = zone.parent;\n      }\n      return zone;\n    }\n    static get current() {\n      return _currentZoneFrame.zone;\n    }\n    static get currentTask() {\n      return _currentTask;\n    }\n    // tslint:disable-next-line:require-internal-with-underscore\n    static __load_patch(name, fn, ignoreDuplicate = false) {\n      if (patches.hasOwnProperty(name)) {\n        // `checkDuplicate` option is defined from global variable\n        // so it works for all modules.\n        // `ignoreDuplicate` can work for the specified module\n        if (!ignoreDuplicate && checkDuplicate) {\n          throw Error('Already loaded patch: ' + name);\n        }\n      } else if (!global['__Zone_disable_' + name]) {\n        const perfName = 'Zone:' + name;\n        mark(perfName);\n        patches[name] = fn(global, Zone, _api);\n        performanceMeasure(perfName, perfName);\n      }\n    }\n    get parent() {\n      return this._parent;\n    }\n    get name() {\n      return this._name;\n    }\n    constructor(parent, zoneSpec) {\n      this._parent = parent;\n      this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n      this._properties = zoneSpec && zoneSpec.properties || {};\n      this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n    }\n    get(key) {\n      const zone = this.getZoneWith(key);\n      if (zone) return zone._properties[key];\n    }\n    getZoneWith(key) {\n      let current = this;\n      while (current) {\n        if (current._properties.hasOwnProperty(key)) {\n          return current;\n        }\n        current = current._parent;\n      }\n      return null;\n    }\n    fork(zoneSpec) {\n      if (!zoneSpec) throw new Error('ZoneSpec required!');\n      return this._zoneDelegate.fork(this, zoneSpec);\n    }\n    wrap(callback, source) {\n      if (typeof callback !== 'function') {\n        throw new Error('Expecting function got: ' + callback);\n      }\n      const _callback = this._zoneDelegate.intercept(this, callback, source);\n      const zone = this;\n      return function () {\n        return zone.runGuarded(_callback, this, arguments, source);\n      };\n    }\n    run(callback, applyThis, applyArgs, source) {\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n      try {\n        return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n      } finally {\n        _currentZoneFrame = _currentZoneFrame.parent;\n      }\n    }\n    runGuarded(callback, applyThis = null, applyArgs, source) {\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n      try {\n        try {\n          return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n        } catch (error) {\n          if (this._zoneDelegate.handleError(this, error)) {\n            throw error;\n          }\n        }\n      } finally {\n        _currentZoneFrame = _currentZoneFrame.parent;\n      }\n    }\n    runTask(task, applyThis, applyArgs) {\n      if (task.zone != this) {\n        throw new Error('A task can only be run in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n      }\n      // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n      // will run in notScheduled(canceled) state, we should not try to\n      // run such kind of task but just return\n      if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n        return;\n      }\n      const reEntryGuard = task.state != running;\n      reEntryGuard && task._transitionTo(running, scheduled);\n      task.runCount++;\n      const previousTask = _currentTask;\n      _currentTask = task;\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n      try {\n        if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n          task.cancelFn = undefined;\n        }\n        try {\n          return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n        } catch (error) {\n          if (this._zoneDelegate.handleError(this, error)) {\n            throw error;\n          }\n        }\n      } finally {\n        // if the task's state is notScheduled or unknown, then it has already been cancelled\n        // we should not reset the state to scheduled\n        if (task.state !== notScheduled && task.state !== unknown) {\n          if (task.type == eventTask || task.data && task.data.isPeriodic) {\n            reEntryGuard && task._transitionTo(scheduled, running);\n          } else {\n            task.runCount = 0;\n            this._updateTaskCount(task, -1);\n            reEntryGuard && task._transitionTo(notScheduled, running, notScheduled);\n          }\n        }\n        _currentZoneFrame = _currentZoneFrame.parent;\n        _currentTask = previousTask;\n      }\n    }\n    scheduleTask(task) {\n      if (task.zone && task.zone !== this) {\n        // check if the task was rescheduled, the newZone\n        // should not be the children of the original zone\n        let newZone = this;\n        while (newZone) {\n          if (newZone === task.zone) {\n            throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n          }\n          newZone = newZone.parent;\n        }\n      }\n      task._transitionTo(scheduling, notScheduled);\n      const zoneDelegates = [];\n      task._zoneDelegates = zoneDelegates;\n      task._zone = this;\n      try {\n        task = this._zoneDelegate.scheduleTask(this, task);\n      } catch (err) {\n        // should set task's state to unknown when scheduleTask throw error\n        // because the err may from reschedule, so the fromState maybe notScheduled\n        task._transitionTo(unknown, scheduling, notScheduled);\n        // TODO: @JiaLiPassion, should we check the result from handleError?\n        this._zoneDelegate.handleError(this, err);\n        throw err;\n      }\n      if (task._zoneDelegates === zoneDelegates) {\n        // we have to check because internally the delegate can reschedule the task.\n        this._updateTaskCount(task, 1);\n      }\n      if (task.state == scheduling) {\n        task._transitionTo(scheduled, scheduling);\n      }\n      return task;\n    }\n    scheduleMicroTask(source, callback, data, customSchedule) {\n      return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n    }\n    scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n      return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n    }\n    scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n      return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n    }\n    cancelTask(task) {\n      if (task.zone != this) throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n      if (task.state !== scheduled && task.state !== running) {\n        return;\n      }\n      task._transitionTo(canceling, scheduled, running);\n      try {\n        this._zoneDelegate.cancelTask(this, task);\n      } catch (err) {\n        // if error occurs when cancelTask, transit the state to unknown\n        task._transitionTo(unknown, canceling);\n        this._zoneDelegate.handleError(this, err);\n        throw err;\n      }\n      this._updateTaskCount(task, -1);\n      task._transitionTo(notScheduled, canceling);\n      task.runCount = 0;\n      return task;\n    }\n    _updateTaskCount(task, count) {\n      const zoneDelegates = task._zoneDelegates;\n      if (count == -1) {\n        task._zoneDelegates = null;\n      }\n      for (let i = 0; i < zoneDelegates.length; i++) {\n        zoneDelegates[i]._updateTaskCount(task.type, count);\n      }\n    }\n  }\n  const DELEGATE_ZS = {\n    name: '',\n    onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n    onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n    onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n    onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task)\n  };\n  class _ZoneDelegate {\n    constructor(zone, parentDelegate, zoneSpec) {\n      this._taskCounts = {\n        'microTask': 0,\n        'macroTask': 0,\n        'eventTask': 0\n      };\n      this.zone = zone;\n      this._parentDelegate = parentDelegate;\n      this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n      this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n      this._forkCurrZone = zoneSpec && (zoneSpec.onFork ? this.zone : parentDelegate._forkCurrZone);\n      this._interceptZS = zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n      this._interceptDlgt = zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n      this._interceptCurrZone = zoneSpec && (zoneSpec.onIntercept ? this.zone : parentDelegate._interceptCurrZone);\n      this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n      this._invokeDlgt = zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n      this._invokeCurrZone = zoneSpec && (zoneSpec.onInvoke ? this.zone : parentDelegate._invokeCurrZone);\n      this._handleErrorZS = zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n      this._handleErrorDlgt = zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n      this._handleErrorCurrZone = zoneSpec && (zoneSpec.onHandleError ? this.zone : parentDelegate._handleErrorCurrZone);\n      this._scheduleTaskZS = zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n      this._scheduleTaskDlgt = zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n      this._scheduleTaskCurrZone = zoneSpec && (zoneSpec.onScheduleTask ? this.zone : parentDelegate._scheduleTaskCurrZone);\n      this._invokeTaskZS = zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n      this._invokeTaskDlgt = zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n      this._invokeTaskCurrZone = zoneSpec && (zoneSpec.onInvokeTask ? this.zone : parentDelegate._invokeTaskCurrZone);\n      this._cancelTaskZS = zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n      this._cancelTaskDlgt = zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n      this._cancelTaskCurrZone = zoneSpec && (zoneSpec.onCancelTask ? this.zone : parentDelegate._cancelTaskCurrZone);\n      this._hasTaskZS = null;\n      this._hasTaskDlgt = null;\n      this._hasTaskDlgtOwner = null;\n      this._hasTaskCurrZone = null;\n      const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n      const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n      if (zoneSpecHasTask || parentHasTask) {\n        // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n        // a case all task related interceptors must go through this ZD. We can't short circuit it.\n        this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n        this._hasTaskDlgt = parentDelegate;\n        this._hasTaskDlgtOwner = this;\n        this._hasTaskCurrZone = zone;\n        if (!zoneSpec.onScheduleTask) {\n          this._scheduleTaskZS = DELEGATE_ZS;\n          this._scheduleTaskDlgt = parentDelegate;\n          this._scheduleTaskCurrZone = this.zone;\n        }\n        if (!zoneSpec.onInvokeTask) {\n          this._invokeTaskZS = DELEGATE_ZS;\n          this._invokeTaskDlgt = parentDelegate;\n          this._invokeTaskCurrZone = this.zone;\n        }\n        if (!zoneSpec.onCancelTask) {\n          this._cancelTaskZS = DELEGATE_ZS;\n          this._cancelTaskDlgt = parentDelegate;\n          this._cancelTaskCurrZone = this.zone;\n        }\n      }\n    }\n    fork(targetZone, zoneSpec) {\n      return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) : new Zone(targetZone, zoneSpec);\n    }\n    intercept(targetZone, callback, source) {\n      return this._interceptZS ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) : callback;\n    }\n    invoke(targetZone, callback, applyThis, applyArgs, source) {\n      return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) : callback.apply(applyThis, applyArgs);\n    }\n    handleError(targetZone, error) {\n      return this._handleErrorZS ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) : true;\n    }\n    scheduleTask(targetZone, task) {\n      let returnTask = task;\n      if (this._scheduleTaskZS) {\n        if (this._hasTaskZS) {\n          returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n        }\n        // clang-format off\n        returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n        // clang-format on\n        if (!returnTask) returnTask = task;\n      } else {\n        if (task.scheduleFn) {\n          task.scheduleFn(task);\n        } else if (task.type == microTask) {\n          scheduleMicroTask(task);\n        } else {\n          throw new Error('Task is missing scheduleFn.');\n        }\n      }\n      return returnTask;\n    }\n    invokeTask(targetZone, task, applyThis, applyArgs) {\n      return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) : task.callback.apply(applyThis, applyArgs);\n    }\n    cancelTask(targetZone, task) {\n      let value;\n      if (this._cancelTaskZS) {\n        value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n      } else {\n        if (!task.cancelFn) {\n          throw Error('Task is not cancelable');\n        }\n        value = task.cancelFn(task);\n      }\n      return value;\n    }\n    hasTask(targetZone, isEmpty) {\n      // hasTask should not throw error so other ZoneDelegate\n      // can still trigger hasTask callback\n      try {\n        this._hasTaskZS && this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n      } catch (err) {\n        this.handleError(targetZone, err);\n      }\n    }\n    // tslint:disable-next-line:require-internal-with-underscore\n    _updateTaskCount(type, count) {\n      const counts = this._taskCounts;\n      const prev = counts[type];\n      const next = counts[type] = prev + count;\n      if (next < 0) {\n        throw new Error('More tasks executed then were scheduled.');\n      }\n      if (prev == 0 || next == 0) {\n        const isEmpty = {\n          microTask: counts['microTask'] > 0,\n          macroTask: counts['macroTask'] > 0,\n          eventTask: counts['eventTask'] > 0,\n          change: type\n        };\n        this.hasTask(this.zone, isEmpty);\n      }\n    }\n  }\n  class ZoneTask {\n    constructor(type, source, callback, options, scheduleFn, cancelFn) {\n      // tslint:disable-next-line:require-internal-with-underscore\n      this._zone = null;\n      this.runCount = 0;\n      // tslint:disable-next-line:require-internal-with-underscore\n      this._zoneDelegates = null;\n      // tslint:disable-next-line:require-internal-with-underscore\n      this._state = 'notScheduled';\n      this.type = type;\n      this.source = source;\n      this.data = options;\n      this.scheduleFn = scheduleFn;\n      this.cancelFn = cancelFn;\n      if (!callback) {\n        throw new Error('callback is not defined');\n      }\n      this.callback = callback;\n      const self = this;\n      // TODO: @JiaLiPassion options should have interface\n      if (type === eventTask && options && options.useG) {\n        this.invoke = ZoneTask.invokeTask;\n      } else {\n        this.invoke = function () {\n          return ZoneTask.invokeTask.call(global, self, this, arguments);\n        };\n      }\n    }\n    static invokeTask(task, target, args) {\n      if (!task) {\n        task = this;\n      }\n      _numberOfNestedTaskFrames++;\n      try {\n        task.runCount++;\n        return task.zone.runTask(task, target, args);\n      } finally {\n        if (_numberOfNestedTaskFrames == 1) {\n          drainMicroTaskQueue();\n        }\n        _numberOfNestedTaskFrames--;\n      }\n    }\n    get zone() {\n      return this._zone;\n    }\n    get state() {\n      return this._state;\n    }\n    cancelScheduleRequest() {\n      this._transitionTo(notScheduled, scheduling);\n    }\n    // tslint:disable-next-line:require-internal-with-underscore\n    _transitionTo(toState, fromState1, fromState2) {\n      if (this._state === fromState1 || this._state === fromState2) {\n        this._state = toState;\n        if (toState == notScheduled) {\n          this._zoneDelegates = null;\n        }\n      } else {\n        throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? ' or \\'' + fromState2 + '\\'' : ''}, was '${this._state}'.`);\n      }\n    }\n    toString() {\n      if (this.data && typeof this.data.handleId !== 'undefined') {\n        return this.data.handleId.toString();\n      } else {\n        return Object.prototype.toString.call(this);\n      }\n    }\n    // add toJSON method to prevent cyclic error when\n    // call JSON.stringify(zoneTask)\n    toJSON() {\n      return {\n        type: this.type,\n        state: this.state,\n        source: this.source,\n        zone: this.zone.name,\n        runCount: this.runCount\n      };\n    }\n  }\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  MICROTASK QUEUE\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  const symbolSetTimeout = __symbol__('setTimeout');\n  const symbolPromise = __symbol__('Promise');\n  const symbolThen = __symbol__('then');\n  let _microTaskQueue = [];\n  let _isDrainingMicrotaskQueue = false;\n  let nativeMicroTaskQueuePromise;\n  function nativeScheduleMicroTask(func) {\n    if (!nativeMicroTaskQueuePromise) {\n      if (global[symbolPromise]) {\n        nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n      }\n    }\n    if (nativeMicroTaskQueuePromise) {\n      let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n      if (!nativeThen) {\n        // native Promise is not patchable, we need to use `then` directly\n        // issue 1078\n        nativeThen = nativeMicroTaskQueuePromise['then'];\n      }\n      nativeThen.call(nativeMicroTaskQueuePromise, func);\n    } else {\n      global[symbolSetTimeout](func, 0);\n    }\n  }\n  function scheduleMicroTask(task) {\n    // if we are not running in any task, and there has not been anything scheduled\n    // we must bootstrap the initial task creation by manually scheduling the drain\n    if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n      // We are not running in Task, so we need to kickstart the microtask queue.\n      nativeScheduleMicroTask(drainMicroTaskQueue);\n    }\n    task && _microTaskQueue.push(task);\n  }\n  function drainMicroTaskQueue() {\n    if (!_isDrainingMicrotaskQueue) {\n      _isDrainingMicrotaskQueue = true;\n      while (_microTaskQueue.length) {\n        const queue = _microTaskQueue;\n        _microTaskQueue = [];\n        for (let i = 0; i < queue.length; i++) {\n          const task = queue[i];\n          try {\n            task.zone.runTask(task, null, null);\n          } catch (error) {\n            _api.onUnhandledError(error);\n          }\n        }\n      }\n      _api.microtaskDrainDone();\n      _isDrainingMicrotaskQueue = false;\n    }\n  }\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  BOOTSTRAP\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  const NO_ZONE = {\n    name: 'NO ZONE'\n  };\n  const notScheduled = 'notScheduled',\n    scheduling = 'scheduling',\n    scheduled = 'scheduled',\n    running = 'running',\n    canceling = 'canceling',\n    unknown = 'unknown';\n  const microTask = 'microTask',\n    macroTask = 'macroTask',\n    eventTask = 'eventTask';\n  const patches = {};\n  const _api = {\n    symbol: __symbol__,\n    currentZoneFrame: () => _currentZoneFrame,\n    onUnhandledError: noop,\n    microtaskDrainDone: noop,\n    scheduleMicroTask: scheduleMicroTask,\n    showUncaughtError: () => !Zone[__symbol__('ignoreConsoleErrorUncaughtError')],\n    patchEventTarget: () => [],\n    patchOnProperties: noop,\n    patchMethod: () => noop,\n    bindArguments: () => [],\n    patchThen: () => noop,\n    patchMacroTask: () => noop,\n    patchEventPrototype: () => noop,\n    isIEOrEdge: () => false,\n    getGlobalObjects: () => undefined,\n    ObjectDefineProperty: () => noop,\n    ObjectGetOwnPropertyDescriptor: () => undefined,\n    ObjectCreate: () => undefined,\n    ArraySlice: () => [],\n    patchClass: () => noop,\n    wrapWithCurrentZone: () => noop,\n    filterProperties: () => [],\n    attachOriginToPatched: () => noop,\n    _redefineProperty: () => noop,\n    patchCallbacks: () => noop,\n    nativeScheduleMicroTask: nativeScheduleMicroTask\n  };\n  let _currentZoneFrame = {\n    parent: null,\n    zone: new Zone(null, null)\n  };\n  let _currentTask = null;\n  let _numberOfNestedTaskFrames = 0;\n  function noop() {}\n  performanceMeasure('Zone', 'Zone');\n  return global['Zone'] = Zone;\n})(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n/// <reference types=\"node\"/>\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = Zone.__symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = Zone.__symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = Zone.__symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n  return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n  return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = Zone.__symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = isWindowExists && internalWindow || typeof self === 'object' && self || global;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (typeof args[i] === 'function') {\n      args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n    }\n  }\n  return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n  const source = prototype.constructor['name'];\n  for (let i = 0; i < fnNames.length; i++) {\n    const name = fnNames[i];\n    const delegate = prototype[name];\n    if (delegate) {\n      const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n      if (!isPropertyWritable(prototypeDesc)) {\n        continue;\n      }\n      prototype[name] = (delegate => {\n        const patched = function () {\n          return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n        };\n        attachOriginToPatched(patched, delegate);\n        return patched;\n      })(delegate);\n    }\n  }\n}\nfunction isPropertyWritable(propertyDesc) {\n  if (!propertyDesc) {\n    return true;\n  }\n  if (propertyDesc.writable === false) {\n    return false;\n  }\n  return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = !('nw' in _global) && typeof _global.process !== 'undefined' && {}.toString.call(_global.process) === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' && {}.toString.call(_global.process) === '[object process]' && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst wrapFn = function (event) {\n  // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n  // event will be undefined, so we need to use window.event\n  event = event || _global.event;\n  if (!event) {\n    return;\n  }\n  let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n  }\n  const target = this || event.target || _global;\n  const listener = target[eventNameSymbol];\n  let result;\n  if (isBrowser && target === internalWindow && event.type === 'error') {\n    // window.onerror have different signature\n    // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n    // and onerror callback will prevent default when callback return true\n    const errorEvent = event;\n    result = listener && listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n    if (result === true) {\n      event.preventDefault();\n    }\n  } else {\n    result = listener && listener.apply(this, arguments);\n    if (result != undefined && !result) {\n      event.preventDefault();\n    }\n  }\n  return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n  let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n  if (!desc && prototype) {\n    // when patch window object, use prototype to check prop exist or not\n    const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n    if (prototypeDesc) {\n      desc = {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  }\n  // if the descriptor not exists or is not configurable\n  // just return\n  if (!desc || !desc.configurable) {\n    return;\n  }\n  const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n  if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n    return;\n  }\n  // A property descriptor cannot have getter/setter and be writable\n  // deleting the writable and value properties avoids this error:\n  //\n  // TypeError: property descriptors must not specify a value or be writable when a\n  // getter or setter has been specified\n  delete desc.writable;\n  delete desc.value;\n  const originalDescGet = desc.get;\n  const originalDescSet = desc.set;\n  // slice(2) cuz 'onclick' -> 'click', etc\n  const eventName = prop.slice(2);\n  let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n  }\n  desc.set = function (newValue) {\n    // in some of windows's onproperty callback, this is undefined\n    // so we need to check it\n    let target = this;\n    if (!target && obj === _global) {\n      target = _global;\n    }\n    if (!target) {\n      return;\n    }\n    const previousValue = target[eventNameSymbol];\n    if (typeof previousValue === 'function') {\n      target.removeEventListener(eventName, wrapFn);\n    }\n    // issue #978, when onload handler was added before loading zone.js\n    // we should remove it with originalDescSet\n    originalDescSet && originalDescSet.call(target, null);\n    target[eventNameSymbol] = newValue;\n    if (typeof newValue === 'function') {\n      target.addEventListener(eventName, wrapFn, false);\n    }\n  };\n  // The getter would return undefined for unassigned properties but the default value of an\n  // unassigned property is null\n  desc.get = function () {\n    // in some of windows's onproperty callback, this is undefined\n    // so we need to check it\n    let target = this;\n    if (!target && obj === _global) {\n      target = _global;\n    }\n    if (!target) {\n      return null;\n    }\n    const listener = target[eventNameSymbol];\n    if (listener) {\n      return listener;\n    } else if (originalDescGet) {\n      // result will be null when use inline event attribute,\n      // such as <button onclick=\"func();\">OK</button>\n      // because the onclick function is internal raw uncompiled handler\n      // the onclick will be evaluated when first time event was triggered or\n      // the property is accessed, https://github.com/angular/zone.js/issues/525\n      // so we should use original native get to retrieve the handler\n      let value = originalDescGet.call(this);\n      if (value) {\n        desc.set.call(this, value);\n        if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n          target.removeAttribute(prop);\n        }\n        return value;\n      }\n    }\n    return null;\n  };\n  ObjectDefineProperty(obj, prop, desc);\n  obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n  if (properties) {\n    for (let i = 0; i < properties.length; i++) {\n      patchProperty(obj, 'on' + properties[i], prototype);\n    }\n  } else {\n    const onProperties = [];\n    for (const prop in obj) {\n      if (prop.slice(0, 2) == 'on') {\n        onProperties.push(prop);\n      }\n    }\n    for (let j = 0; j < onProperties.length; j++) {\n      patchProperty(obj, onProperties[j], prototype);\n    }\n  }\n}\nconst originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n  const OriginalClass = _global[className];\n  if (!OriginalClass) return;\n  // keep original class in global\n  _global[zoneSymbol(className)] = OriginalClass;\n  _global[className] = function () {\n    const a = bindArguments(arguments, className);\n    switch (a.length) {\n      case 0:\n        this[originalInstanceKey] = new OriginalClass();\n        break;\n      case 1:\n        this[originalInstanceKey] = new OriginalClass(a[0]);\n        break;\n      case 2:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n        break;\n      case 3:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n        break;\n      case 4:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n        break;\n      default:\n        throw new Error('Arg list too long.');\n    }\n  };\n  // attach original delegate to patched function\n  attachOriginToPatched(_global[className], OriginalClass);\n  const instance = new OriginalClass(function () {});\n  let prop;\n  for (prop in instance) {\n    // https://bugs.webkit.org/show_bug.cgi?id=44721\n    if (className === 'XMLHttpRequest' && prop === 'responseBlob') continue;\n    (function (prop) {\n      if (typeof instance[prop] === 'function') {\n        _global[className].prototype[prop] = function () {\n          return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n        };\n      } else {\n        ObjectDefineProperty(_global[className].prototype, prop, {\n          set: function (fn) {\n            if (typeof fn === 'function') {\n              this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n              // keep callback in wrapped function so we can\n              // use it in Function.prototype.toString to return\n              // the native one.\n              attachOriginToPatched(this[originalInstanceKey][prop], fn);\n            } else {\n              this[originalInstanceKey][prop] = fn;\n            }\n          },\n          get: function () {\n            return this[originalInstanceKey][prop];\n          }\n        });\n      }\n    })(prop);\n  }\n  for (prop in OriginalClass) {\n    if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n      _global[className][prop] = OriginalClass[prop];\n    }\n  }\n}\nfunction patchMethod(target, name, patchFn) {\n  let proto = target;\n  while (proto && !proto.hasOwnProperty(name)) {\n    proto = ObjectGetPrototypeOf(proto);\n  }\n  if (!proto && target[name]) {\n    // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n    proto = target;\n  }\n  const delegateName = zoneSymbol(name);\n  let delegate = null;\n  if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n    delegate = proto[delegateName] = proto[name];\n    // check whether proto[name] is writable\n    // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n    const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n    if (isPropertyWritable(desc)) {\n      const patchDelegate = patchFn(delegate, delegateName, name);\n      proto[name] = function () {\n        return patchDelegate(this, arguments);\n      };\n      attachOriginToPatched(proto[name], delegate);\n    }\n  }\n  return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n  let setNative = null;\n  function scheduleTask(task) {\n    const data = task.data;\n    data.args[data.cbIdx] = function () {\n      task.invoke.apply(this, arguments);\n    };\n    setNative.apply(data.target, data.args);\n    return task;\n  }\n  setNative = patchMethod(obj, funcName, delegate => function (self, args) {\n    const meta = metaCreator(self, args);\n    if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n      return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(self, args);\n    }\n  });\n}\nfunction attachOriginToPatched(patched, original) {\n  patched[zoneSymbol('OriginalDelegate')] = original;\n}\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\nfunction isIE() {\n  try {\n    const ua = internalWindow.navigator.userAgent;\n    if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n      return true;\n    }\n  } catch (error) {}\n  return false;\n}\nfunction isIEOrEdge() {\n  if (isDetectedIEOrEdge) {\n    return ieOrEdge;\n  }\n  isDetectedIEOrEdge = true;\n  try {\n    const ua = internalWindow.navigator.userAgent;\n    if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n      ieOrEdge = true;\n    }\n  } catch (error) {}\n  return ieOrEdge;\n}\nZone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n  const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n  const ObjectDefineProperty = Object.defineProperty;\n  function readableObjectToString(obj) {\n    if (obj && obj.toString === Object.prototype.toString) {\n      const className = obj.constructor && obj.constructor.name;\n      return (className ? className : '') + ': ' + JSON.stringify(obj);\n    }\n    return obj ? obj.toString() : Object.prototype.toString.call(obj);\n  }\n  const __symbol__ = api.symbol;\n  const _uncaughtPromiseErrors = [];\n  const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] === true;\n  const symbolPromise = __symbol__('Promise');\n  const symbolThen = __symbol__('then');\n  const creationTrace = '__creationTrace__';\n  api.onUnhandledError = e => {\n    if (api.showUncaughtError()) {\n      const rejection = e && e.rejection;\n      if (rejection) {\n        console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n      } else {\n        console.error(e);\n      }\n    }\n  };\n  api.microtaskDrainDone = () => {\n    while (_uncaughtPromiseErrors.length) {\n      const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n      try {\n        uncaughtPromiseError.zone.runGuarded(() => {\n          if (uncaughtPromiseError.throwOriginal) {\n            throw uncaughtPromiseError.rejection;\n          }\n          throw uncaughtPromiseError;\n        });\n      } catch (error) {\n        handleUnhandledRejection(error);\n      }\n    }\n  };\n  const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n  function handleUnhandledRejection(e) {\n    api.onUnhandledError(e);\n    try {\n      const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n      if (typeof handler === 'function') {\n        handler.call(this, e);\n      }\n    } catch (err) {}\n  }\n  function isThenable(value) {\n    return value && value.then;\n  }\n  function forwardResolution(value) {\n    return value;\n  }\n  function forwardRejection(rejection) {\n    return ZoneAwarePromise.reject(rejection);\n  }\n  const symbolState = __symbol__('state');\n  const symbolValue = __symbol__('value');\n  const symbolFinally = __symbol__('finally');\n  const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n  const symbolParentPromiseState = __symbol__('parentPromiseState');\n  const source = 'Promise.then';\n  const UNRESOLVED = null;\n  const RESOLVED = true;\n  const REJECTED = false;\n  const REJECTED_NO_CATCH = 0;\n  function makeResolver(promise, state) {\n    return v => {\n      try {\n        resolvePromise(promise, state, v);\n      } catch (err) {\n        resolvePromise(promise, false, err);\n      }\n      // Do not return value or you will break the Promise spec.\n    };\n  }\n\n  const once = function () {\n    let wasCalled = false;\n    return function wrapper(wrappedFunction) {\n      return function () {\n        if (wasCalled) {\n          return;\n        }\n        wasCalled = true;\n        wrappedFunction.apply(null, arguments);\n      };\n    };\n  };\n  const TYPE_ERROR = 'Promise resolved with itself';\n  const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n  // Promise Resolution\n  function resolvePromise(promise, state, value) {\n    const onceWrapper = once();\n    if (promise === value) {\n      throw new TypeError(TYPE_ERROR);\n    }\n    if (promise[symbolState] === UNRESOLVED) {\n      // should only get value.then once based on promise spec.\n      let then = null;\n      try {\n        if (typeof value === 'object' || typeof value === 'function') {\n          then = value && value.then;\n        }\n      } catch (err) {\n        onceWrapper(() => {\n          resolvePromise(promise, false, err);\n        })();\n        return promise;\n      }\n      // if (value instanceof ZoneAwarePromise) {\n      if (state !== REJECTED && value instanceof ZoneAwarePromise && value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) && value[symbolState] !== UNRESOLVED) {\n        clearRejectedNoCatch(value);\n        resolvePromise(promise, value[symbolState], value[symbolValue]);\n      } else if (state !== REJECTED && typeof then === 'function') {\n        try {\n          then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n        } catch (err) {\n          onceWrapper(() => {\n            resolvePromise(promise, false, err);\n          })();\n        }\n      } else {\n        promise[symbolState] = state;\n        const queue = promise[symbolValue];\n        promise[symbolValue] = value;\n        if (promise[symbolFinally] === symbolFinally) {\n          // the promise is generated by Promise.prototype.finally\n          if (state === RESOLVED) {\n            // the state is resolved, should ignore the value\n            // and use parent promise value\n            promise[symbolState] = promise[symbolParentPromiseState];\n            promise[symbolValue] = promise[symbolParentPromiseValue];\n          }\n        }\n        // record task information in value when error occurs, so we can\n        // do some additional work such as render longStackTrace\n        if (state === REJECTED && value instanceof Error) {\n          // check if longStackTraceZone is here\n          const trace = Zone.currentTask && Zone.currentTask.data && Zone.currentTask.data[creationTrace];\n          if (trace) {\n            // only keep the long stack trace into error when in longStackTraceZone\n            ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n              configurable: true,\n              enumerable: false,\n              writable: true,\n              value: trace\n            });\n          }\n        }\n        for (let i = 0; i < queue.length;) {\n          scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n        }\n        if (queue.length == 0 && state == REJECTED) {\n          promise[symbolState] = REJECTED_NO_CATCH;\n          let uncaughtPromiseError = value;\n          try {\n            // Here we throws a new Error to print more readable error log\n            // and if the value is not an error, zone.js builds an `Error`\n            // Object here to attach the stack information.\n            throw new Error('Uncaught (in promise): ' + readableObjectToString(value) + (value && value.stack ? '\\n' + value.stack : ''));\n          } catch (err) {\n            uncaughtPromiseError = err;\n          }\n          if (isDisableWrappingUncaughtPromiseRejection) {\n            // If disable wrapping uncaught promise reject\n            // use the value instead of wrapping it.\n            uncaughtPromiseError.throwOriginal = true;\n          }\n          uncaughtPromiseError.rejection = value;\n          uncaughtPromiseError.promise = promise;\n          uncaughtPromiseError.zone = Zone.current;\n          uncaughtPromiseError.task = Zone.currentTask;\n          _uncaughtPromiseErrors.push(uncaughtPromiseError);\n          api.scheduleMicroTask(); // to make sure that it is running\n        }\n      }\n    }\n    // Resolving an already resolved promise is a noop.\n    return promise;\n  }\n  const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n  function clearRejectedNoCatch(promise) {\n    if (promise[symbolState] === REJECTED_NO_CATCH) {\n      // if the promise is rejected no catch status\n      // and queue.length > 0, means there is a error handler\n      // here to handle the rejected promise, we should trigger\n      // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n      // eventHandler\n      try {\n        const handler = Zone[REJECTION_HANDLED_HANDLER];\n        if (handler && typeof handler === 'function') {\n          handler.call(this, {\n            rejection: promise[symbolValue],\n            promise: promise\n          });\n        }\n      } catch (err) {}\n      promise[symbolState] = REJECTED;\n      for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n        if (promise === _uncaughtPromiseErrors[i].promise) {\n          _uncaughtPromiseErrors.splice(i, 1);\n        }\n      }\n    }\n  }\n  function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n    clearRejectedNoCatch(promise);\n    const promiseState = promise[symbolState];\n    const delegate = promiseState ? typeof onFulfilled === 'function' ? onFulfilled : forwardResolution : typeof onRejected === 'function' ? onRejected : forwardRejection;\n    zone.scheduleMicroTask(source, () => {\n      try {\n        const parentPromiseValue = promise[symbolValue];\n        const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n        if (isFinallyPromise) {\n          // if the promise is generated from finally call, keep parent promise's state and value\n          chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n          chainPromise[symbolParentPromiseState] = promiseState;\n        }\n        // should not pass value to finally callback\n        const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ? [] : [parentPromiseValue]);\n        resolvePromise(chainPromise, true, value);\n      } catch (error) {\n        // if error occurs, should always return this error\n        resolvePromise(chainPromise, false, error);\n      }\n    }, chainPromise);\n  }\n  const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n  const noop = function () {};\n  const AggregateError = global.AggregateError;\n  class ZoneAwarePromise {\n    static toString() {\n      return ZONE_AWARE_PROMISE_TO_STRING;\n    }\n    static resolve(value) {\n      return resolvePromise(new this(null), RESOLVED, value);\n    }\n    static reject(error) {\n      return resolvePromise(new this(null), REJECTED, error);\n    }\n    static any(values) {\n      if (!values || typeof values[Symbol.iterator] !== 'function') {\n        return Promise.reject(new AggregateError([], 'All promises were rejected'));\n      }\n      const promises = [];\n      let count = 0;\n      try {\n        for (let v of values) {\n          count++;\n          promises.push(ZoneAwarePromise.resolve(v));\n        }\n      } catch (err) {\n        return Promise.reject(new AggregateError([], 'All promises were rejected'));\n      }\n      if (count === 0) {\n        return Promise.reject(new AggregateError([], 'All promises were rejected'));\n      }\n      let finished = false;\n      const errors = [];\n      return new ZoneAwarePromise((resolve, reject) => {\n        for (let i = 0; i < promises.length; i++) {\n          promises[i].then(v => {\n            if (finished) {\n              return;\n            }\n            finished = true;\n            resolve(v);\n          }, err => {\n            errors.push(err);\n            count--;\n            if (count === 0) {\n              finished = true;\n              reject(new AggregateError(errors, 'All promises were rejected'));\n            }\n          });\n        }\n      });\n    }\n    static race(values) {\n      let resolve;\n      let reject;\n      let promise = new this((res, rej) => {\n        resolve = res;\n        reject = rej;\n      });\n      function onResolve(value) {\n        resolve(value);\n      }\n      function onReject(error) {\n        reject(error);\n      }\n      for (let value of values) {\n        if (!isThenable(value)) {\n          value = this.resolve(value);\n        }\n        value.then(onResolve, onReject);\n      }\n      return promise;\n    }\n    static all(values) {\n      return ZoneAwarePromise.allWithCallback(values);\n    }\n    static allSettled(values) {\n      const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n      return P.allWithCallback(values, {\n        thenCallback: value => ({\n          status: 'fulfilled',\n          value\n        }),\n        errorCallback: err => ({\n          status: 'rejected',\n          reason: err\n        })\n      });\n    }\n    static allWithCallback(values, callback) {\n      let resolve;\n      let reject;\n      let promise = new this((res, rej) => {\n        resolve = res;\n        reject = rej;\n      });\n      // Start at 2 to prevent prematurely resolving if .then is called immediately.\n      let unresolvedCount = 2;\n      let valueIndex = 0;\n      const resolvedValues = [];\n      for (let value of values) {\n        if (!isThenable(value)) {\n          value = this.resolve(value);\n        }\n        const curValueIndex = valueIndex;\n        try {\n          value.then(value => {\n            resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n            unresolvedCount--;\n            if (unresolvedCount === 0) {\n              resolve(resolvedValues);\n            }\n          }, err => {\n            if (!callback) {\n              reject(err);\n            } else {\n              resolvedValues[curValueIndex] = callback.errorCallback(err);\n              unresolvedCount--;\n              if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n              }\n            }\n          });\n        } catch (thenErr) {\n          reject(thenErr);\n        }\n        unresolvedCount++;\n        valueIndex++;\n      }\n      // Make the unresolvedCount zero-based again.\n      unresolvedCount -= 2;\n      if (unresolvedCount === 0) {\n        resolve(resolvedValues);\n      }\n      return promise;\n    }\n    constructor(executor) {\n      const promise = this;\n      if (!(promise instanceof ZoneAwarePromise)) {\n        throw new Error('Must be an instanceof Promise.');\n      }\n      promise[symbolState] = UNRESOLVED;\n      promise[symbolValue] = []; // queue;\n      try {\n        const onceWrapper = once();\n        executor && executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n      } catch (error) {\n        resolvePromise(promise, false, error);\n      }\n    }\n    get [Symbol.toStringTag]() {\n      return 'Promise';\n    }\n    get [Symbol.species]() {\n      return ZoneAwarePromise;\n    }\n    then(onFulfilled, onRejected) {\n      // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n      // may be an object without a prototype (created through `Object.create(null)`); thus\n      // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n      // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n      // object and copies promise properties into that object (within the `getOrCreateLoad`\n      // function). The zone.js then checks if the resolved value has the `then` method and invokes\n      // it with the `value` context. Otherwise, this will throw an error: `TypeError: Cannot read\n      // properties of undefined (reading 'Symbol(Symbol.species)')`.\n      let C = this.constructor?.[Symbol.species];\n      if (!C || typeof C !== 'function') {\n        C = this.constructor || ZoneAwarePromise;\n      }\n      const chainPromise = new C(noop);\n      const zone = Zone.current;\n      if (this[symbolState] == UNRESOLVED) {\n        this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n      } else {\n        scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n      }\n      return chainPromise;\n    }\n    catch(onRejected) {\n      return this.then(null, onRejected);\n    }\n    finally(onFinally) {\n      // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n      let C = this.constructor?.[Symbol.species];\n      if (!C || typeof C !== 'function') {\n        C = ZoneAwarePromise;\n      }\n      const chainPromise = new C(noop);\n      chainPromise[symbolFinally] = symbolFinally;\n      const zone = Zone.current;\n      if (this[symbolState] == UNRESOLVED) {\n        this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n      } else {\n        scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n      }\n      return chainPromise;\n    }\n  }\n  // Protect against aggressive optimizers dropping seemingly unused properties.\n  // E.g. Closure Compiler in advanced mode.\n  ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n  ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n  ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n  ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n  const NativePromise = global[symbolPromise] = global['Promise'];\n  global['Promise'] = ZoneAwarePromise;\n  const symbolThenPatched = __symbol__('thenPatched');\n  function patchThen(Ctor) {\n    const proto = Ctor.prototype;\n    const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n    if (prop && (prop.writable === false || !prop.configurable)) {\n      // check Ctor.prototype.then propertyDescriptor is writable or not\n      // in meteor env, writable is false, we should ignore such case\n      return;\n    }\n    const originalThen = proto.then;\n    // Keep a reference to the original method.\n    proto[symbolThen] = originalThen;\n    Ctor.prototype.then = function (onResolve, onReject) {\n      const wrapped = new ZoneAwarePromise((resolve, reject) => {\n        originalThen.call(this, resolve, reject);\n      });\n      return wrapped.then(onResolve, onReject);\n    };\n    Ctor[symbolThenPatched] = true;\n  }\n  api.patchThen = patchThen;\n  function zoneify(fn) {\n    return function (self, args) {\n      let resultPromise = fn.apply(self, args);\n      if (resultPromise instanceof ZoneAwarePromise) {\n        return resultPromise;\n      }\n      let ctor = resultPromise.constructor;\n      if (!ctor[symbolThenPatched]) {\n        patchThen(ctor);\n      }\n      return resultPromise;\n    };\n  }\n  if (NativePromise) {\n    patchThen(NativePromise);\n    patchMethod(global, 'fetch', delegate => zoneify(delegate));\n  }\n  // This is not part of public API, but it is useful for tests, so we expose it.\n  Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n  return ZoneAwarePromise;\n});\n\n// override Function.prototype.toString to make zone.js patched function\n// look like native function\nZone.__load_patch('toString', global => {\n  // patch Func.prototype.toString to let them look like native\n  const originalFunctionToString = Function.prototype.toString;\n  const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n  const PROMISE_SYMBOL = zoneSymbol('Promise');\n  const ERROR_SYMBOL = zoneSymbol('Error');\n  const newFunctionToString = function toString() {\n    if (typeof this === 'function') {\n      const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n      if (originalDelegate) {\n        if (typeof originalDelegate === 'function') {\n          return originalFunctionToString.call(originalDelegate);\n        } else {\n          return Object.prototype.toString.call(originalDelegate);\n        }\n      }\n      if (this === Promise) {\n        const nativePromise = global[PROMISE_SYMBOL];\n        if (nativePromise) {\n          return originalFunctionToString.call(nativePromise);\n        }\n      }\n      if (this === Error) {\n        const nativeError = global[ERROR_SYMBOL];\n        if (nativeError) {\n          return originalFunctionToString.call(nativeError);\n        }\n      }\n    }\n    return originalFunctionToString.call(this);\n  };\n  newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n  Function.prototype.toString = newFunctionToString;\n  // patch Object.prototype.toString to let them look like native\n  const originalObjectToString = Object.prototype.toString;\n  const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n  Object.prototype.toString = function () {\n    if (typeof Promise === 'function' && this instanceof Promise) {\n      return PROMISE_OBJECT_TO_STRING;\n    }\n    return originalObjectToString.call(this);\n  };\n});\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nlet passiveSupported = false;\nif (typeof window !== 'undefined') {\n  try {\n    const options = Object.defineProperty({}, 'passive', {\n      get: function () {\n        passiveSupported = true;\n      }\n    });\n    // Note: We pass the `options` object as the event handler too. This is not compatible with the\n    // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n    // without an actual handler.\n    window.addEventListener('test', options, options);\n    window.removeEventListener('test', options, options);\n  } catch (err) {\n    passiveSupported = false;\n  }\n}\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n  useG: true\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n  const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n  const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n  const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n  const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n  zoneSymbolEventNames[eventName] = {};\n  zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n  zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n  const ADD_EVENT_LISTENER = patchOptions && patchOptions.add || ADD_EVENT_LISTENER_STR;\n  const REMOVE_EVENT_LISTENER = patchOptions && patchOptions.rm || REMOVE_EVENT_LISTENER_STR;\n  const LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.listeners || 'eventListeners';\n  const REMOVE_ALL_LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.rmAll || 'removeAllListeners';\n  const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n  const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n  const PREPEND_EVENT_LISTENER = 'prependListener';\n  const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n  const invokeTask = function (task, target, event) {\n    // for better performance, check isRemoved which is set\n    // by removeEventListener\n    if (task.isRemoved) {\n      return;\n    }\n    const delegate = task.callback;\n    if (typeof delegate === 'object' && delegate.handleEvent) {\n      // create the bind version of handleEvent when invoke\n      task.callback = event => delegate.handleEvent(event);\n      task.originalDelegate = delegate;\n    }\n    // invoke static task.invoke\n    // need to try/catch error here, otherwise, the error in one event listener\n    // will break the executions of the other event listeners. Also error will\n    // not remove the event listener when `once` options is true.\n    let error;\n    try {\n      task.invoke(task, target, [event]);\n    } catch (err) {\n      error = err;\n    }\n    const options = task.options;\n    if (options && typeof options === 'object' && options.once) {\n      // if options.once is true, after invoke once remove listener here\n      // only browser need to do this, nodejs eventEmitter will cal removeListener\n      // inside EventEmitter.once\n      const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n      target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n    }\n    return error;\n  };\n  function globalCallback(context, event, isCapture) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n      return;\n    }\n    // event.target is needed for Samsung TV and SourceBuffer\n    // || global is needed https://github.com/angular/zone.js/issues/190\n    const target = context || event.target || _global;\n    const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n    if (tasks) {\n      const errors = [];\n      // invoke all tasks which attached to current target with given event.type and capture = false\n      // for performance concern, if task.length === 1, just invoke\n      if (tasks.length === 1) {\n        const err = invokeTask(tasks[0], target, event);\n        err && errors.push(err);\n      } else {\n        // https://github.com/angular/zone.js/issues/836\n        // copy the tasks array before invoke, to avoid\n        // the callback will remove itself or other listener\n        const copyTasks = tasks.slice();\n        for (let i = 0; i < copyTasks.length; i++) {\n          if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n            break;\n          }\n          const err = invokeTask(copyTasks[i], target, event);\n          err && errors.push(err);\n        }\n      }\n      // Since there is only one error, we don't need to schedule microTask\n      // to throw the error.\n      if (errors.length === 1) {\n        throw errors[0];\n      } else {\n        for (let i = 0; i < errors.length; i++) {\n          const err = errors[i];\n          api.nativeScheduleMicroTask(() => {\n            throw err;\n          });\n        }\n      }\n    }\n  }\n  // global shared zoneAwareCallback to handle all event callback with capture = false\n  const globalZoneAwareCallback = function (event) {\n    return globalCallback(this, event, false);\n  };\n  // global shared zoneAwareCallback to handle all event callback with capture = true\n  const globalZoneAwareCaptureCallback = function (event) {\n    return globalCallback(this, event, true);\n  };\n  function patchEventTargetMethods(obj, patchOptions) {\n    if (!obj) {\n      return false;\n    }\n    let useGlobalCallback = true;\n    if (patchOptions && patchOptions.useG !== undefined) {\n      useGlobalCallback = patchOptions.useG;\n    }\n    const validateHandler = patchOptions && patchOptions.vh;\n    let checkDuplicate = true;\n    if (patchOptions && patchOptions.chkDup !== undefined) {\n      checkDuplicate = patchOptions.chkDup;\n    }\n    let returnTarget = false;\n    if (patchOptions && patchOptions.rt !== undefined) {\n      returnTarget = patchOptions.rt;\n    }\n    let proto = obj;\n    while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n      proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && obj[ADD_EVENT_LISTENER]) {\n      // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n      proto = obj;\n    }\n    if (!proto) {\n      return false;\n    }\n    if (proto[zoneSymbolAddEventListener]) {\n      return false;\n    }\n    const eventNameToString = patchOptions && patchOptions.eventNameToString;\n    // a shared global taskData to pass data for scheduleEventTask\n    // so we do not need to create a new object just for pass some data\n    const taskData = {};\n    const nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n    const nativeRemoveEventListener = proto[zoneSymbol(REMOVE_EVENT_LISTENER)] = proto[REMOVE_EVENT_LISTENER];\n    const nativeListeners = proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] = proto[LISTENERS_EVENT_LISTENER];\n    const nativeRemoveAllListeners = proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] = proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n    let nativePrependEventListener;\n    if (patchOptions && patchOptions.prepend) {\n      nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] = proto[patchOptions.prepend];\n    }\n    /**\n     * This util function will build an option object with passive option\n     * to handle all possible input from the user.\n     */\n    function buildEventListenerOptions(options, passive) {\n      if (!passiveSupported && typeof options === 'object' && options) {\n        // doesn't support passive but user want to pass an object as options.\n        // this will not work on some old browser, so we just pass a boolean\n        // as useCapture parameter\n        return !!options.capture;\n      }\n      if (!passiveSupported || !passive) {\n        return options;\n      }\n      if (typeof options === 'boolean') {\n        return {\n          capture: options,\n          passive: true\n        };\n      }\n      if (!options) {\n        return {\n          passive: true\n        };\n      }\n      if (typeof options === 'object' && options.passive !== false) {\n        return {\n          ...options,\n          passive: true\n        };\n      }\n      return options;\n    }\n    const customScheduleGlobal = function (task) {\n      // if there is already a task for the eventName + capture,\n      // just return, because we use the shared globalZoneAwareCallback here.\n      if (taskData.isExisting) {\n        return;\n      }\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n    };\n    const customCancelGlobal = function (task) {\n      // if task is not marked as isRemoved, this call is directly\n      // from Zone.prototype.cancelTask, we should remove the task\n      // from tasksList of target first\n      if (!task.isRemoved) {\n        const symbolEventNames = zoneSymbolEventNames[task.eventName];\n        let symbolEventName;\n        if (symbolEventNames) {\n          symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n        }\n        const existingTasks = symbolEventName && task.target[symbolEventName];\n        if (existingTasks) {\n          for (let i = 0; i < existingTasks.length; i++) {\n            const existingTask = existingTasks[i];\n            if (existingTask === task) {\n              existingTasks.splice(i, 1);\n              // set isRemoved to data for faster invokeTask check\n              task.isRemoved = true;\n              if (existingTasks.length === 0) {\n                // all tasks for the eventName + capture have gone,\n                // remove globalZoneAwareCallback and remove the task cache from target\n                task.allRemoved = true;\n                task.target[symbolEventName] = null;\n              }\n              break;\n            }\n          }\n        }\n      }\n      // if all tasks for the eventName + capture have gone,\n      // we will really remove the global event callback,\n      // if not, return\n      if (!task.allRemoved) {\n        return;\n      }\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n    };\n    const customScheduleNonGlobal = function (task) {\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n    const customSchedulePrepend = function (task) {\n      return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n    const customCancelNonGlobal = function (task) {\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n    };\n    const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n    const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n    const compareTaskCallbackVsDelegate = function (task, delegate) {\n      const typeOfDelegate = typeof delegate;\n      return typeOfDelegate === 'function' && task.callback === delegate || typeOfDelegate === 'object' && task.originalDelegate === delegate;\n    };\n    const compare = patchOptions && patchOptions.diff ? patchOptions.diff : compareTaskCallbackVsDelegate;\n    const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n    const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n    const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n      return function () {\n        const target = this || _global;\n        let eventName = arguments[0];\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n        let delegate = arguments[1];\n        if (!delegate) {\n          return nativeListener.apply(this, arguments);\n        }\n        if (isNode && eventName === 'uncaughtException') {\n          // don't patch uncaughtException of nodejs to prevent endless loop\n          return nativeListener.apply(this, arguments);\n        }\n        // don't create the bind delegate function for handleEvent\n        // case here to improve addEventListener performance\n        // we will create the bind delegate when invoke\n        let isHandleEvent = false;\n        if (typeof delegate !== 'function') {\n          if (!delegate.handleEvent) {\n            return nativeListener.apply(this, arguments);\n          }\n          isHandleEvent = true;\n        }\n        if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n          return;\n        }\n        const passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n        const options = buildEventListenerOptions(arguments[2], passive);\n        if (unpatchedEvents) {\n          // check unpatched list\n          for (let i = 0; i < unpatchedEvents.length; i++) {\n            if (eventName === unpatchedEvents[i]) {\n              if (passive) {\n                return nativeListener.call(target, eventName, delegate, options);\n              } else {\n                return nativeListener.apply(this, arguments);\n              }\n            }\n          }\n        }\n        const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n        const once = options && typeof options === 'object' ? options.once : false;\n        const zone = Zone.current;\n        let symbolEventNames = zoneSymbolEventNames[eventName];\n        if (!symbolEventNames) {\n          prepareEventNames(eventName, eventNameToString);\n          symbolEventNames = zoneSymbolEventNames[eventName];\n        }\n        const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n        let existingTasks = target[symbolEventName];\n        let isExisting = false;\n        if (existingTasks) {\n          // already have task registered\n          isExisting = true;\n          if (checkDuplicate) {\n            for (let i = 0; i < existingTasks.length; i++) {\n              if (compare(existingTasks[i], delegate)) {\n                // same callback, same capture, same event name, just return\n                return;\n              }\n            }\n          }\n        } else {\n          existingTasks = target[symbolEventName] = [];\n        }\n        let source;\n        const constructorName = target.constructor['name'];\n        const targetSource = globalSources[constructorName];\n        if (targetSource) {\n          source = targetSource[eventName];\n        }\n        if (!source) {\n          source = constructorName + addSource + (eventNameToString ? eventNameToString(eventName) : eventName);\n        }\n        // do not create a new object as task.data to pass those things\n        // just use the global shared one\n        taskData.options = options;\n        if (once) {\n          // if addEventListener with once options, we don't pass it to\n          // native addEventListener, instead we keep the once setting\n          // and handle ourselves.\n          taskData.options.once = false;\n        }\n        taskData.target = target;\n        taskData.capture = capture;\n        taskData.eventName = eventName;\n        taskData.isExisting = isExisting;\n        const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n        // keep taskData into data to allow onScheduleEventTask to access the task information\n        if (data) {\n          data.taskData = taskData;\n        }\n        const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n        // should clear taskData.target to avoid memory leak\n        // issue, https://github.com/angular/angular/issues/20442\n        taskData.target = null;\n        // need to clear up taskData because it is a global object\n        if (data) {\n          data.taskData = null;\n        }\n        // have to save those information to task in case\n        // application may call task.zone.cancelTask() directly\n        if (once) {\n          options.once = true;\n        }\n        if (!(!passiveSupported && typeof task.options === 'boolean')) {\n          // if not support passive, and we pass an option object\n          // to addEventListener, we should save the options to task\n          task.options = options;\n        }\n        task.target = target;\n        task.capture = capture;\n        task.eventName = eventName;\n        if (isHandleEvent) {\n          // save original delegate for compare to check duplicate\n          task.originalDelegate = delegate;\n        }\n        if (!prepend) {\n          existingTasks.push(task);\n        } else {\n          existingTasks.unshift(task);\n        }\n        if (returnTarget) {\n          return target;\n        }\n      };\n    };\n    proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n    if (nativePrependEventListener) {\n      proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n    }\n    proto[REMOVE_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n      const options = arguments[2];\n      const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n      const delegate = arguments[1];\n      if (!delegate) {\n        return nativeRemoveEventListener.apply(this, arguments);\n      }\n      if (validateHandler && !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n        return;\n      }\n      const symbolEventNames = zoneSymbolEventNames[eventName];\n      let symbolEventName;\n      if (symbolEventNames) {\n        symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n      }\n      const existingTasks = symbolEventName && target[symbolEventName];\n      if (existingTasks) {\n        for (let i = 0; i < existingTasks.length; i++) {\n          const existingTask = existingTasks[i];\n          if (compare(existingTask, delegate)) {\n            existingTasks.splice(i, 1);\n            // set isRemoved to data for faster invokeTask check\n            existingTask.isRemoved = true;\n            if (existingTasks.length === 0) {\n              // all tasks for the eventName + capture have gone,\n              // remove globalZoneAwareCallback and remove the task cache from target\n              existingTask.allRemoved = true;\n              target[symbolEventName] = null;\n              // in the target, we have an event listener which is added by on_property\n              // such as target.onclick = function() {}, so we need to clear this internal\n              // property too if all delegates all removed\n              if (typeof eventName === 'string') {\n                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                target[onPropertySymbol] = null;\n              }\n            }\n            existingTask.zone.cancelTask(existingTask);\n            if (returnTarget) {\n              return target;\n            }\n            return;\n          }\n        }\n      }\n      // issue 930, didn't find the event name or callback\n      // from zone kept existingTasks, the callback maybe\n      // added outside of zone, we need to call native removeEventListener\n      // to try to remove it.\n      return nativeRemoveEventListener.apply(this, arguments);\n    };\n    proto[LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n      const listeners = [];\n      const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n      for (let i = 0; i < tasks.length; i++) {\n        const task = tasks[i];\n        let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n        listeners.push(delegate);\n      }\n      return listeners;\n    };\n    proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n      if (!eventName) {\n        const keys = Object.keys(target);\n        for (let i = 0; i < keys.length; i++) {\n          const prop = keys[i];\n          const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n          let evtName = match && match[1];\n          // in nodejs EventEmitter, removeListener event is\n          // used for monitoring the removeListener call,\n          // so just keep removeListener eventListener until\n          // all other eventListeners are removed\n          if (evtName && evtName !== 'removeListener') {\n            this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n          }\n        }\n        // remove removeListener listener finally\n        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n      } else {\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n        const symbolEventNames = zoneSymbolEventNames[eventName];\n        if (symbolEventNames) {\n          const symbolEventName = symbolEventNames[FALSE_STR];\n          const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n          const tasks = target[symbolEventName];\n          const captureTasks = target[symbolCaptureEventName];\n          if (tasks) {\n            const removeTasks = tasks.slice();\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n          if (captureTasks) {\n            const removeTasks = captureTasks.slice();\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n        }\n      }\n      if (returnTarget) {\n        return this;\n      }\n    };\n    // for native toString patch\n    attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n    attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n    if (nativeRemoveAllListeners) {\n      attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n    }\n    if (nativeListeners) {\n      attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n    }\n    return true;\n  }\n  let results = [];\n  for (let i = 0; i < apis.length; i++) {\n    results[i] = patchEventTargetMethods(apis[i], patchOptions);\n  }\n  return results;\n}\nfunction findEventTasks(target, eventName) {\n  if (!eventName) {\n    const foundTasks = [];\n    for (let prop in target) {\n      const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n      let evtName = match && match[1];\n      if (evtName && (!eventName || evtName === eventName)) {\n        const tasks = target[prop];\n        if (tasks) {\n          for (let i = 0; i < tasks.length; i++) {\n            foundTasks.push(tasks[i]);\n          }\n        }\n      }\n    }\n    return foundTasks;\n  }\n  let symbolEventName = zoneSymbolEventNames[eventName];\n  if (!symbolEventName) {\n    prepareEventNames(eventName);\n    symbolEventName = zoneSymbolEventNames[eventName];\n  }\n  const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n  const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n  if (!captureFalseTasks) {\n    return captureTrueTasks ? captureTrueTasks.slice() : [];\n  } else {\n    return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) : captureFalseTasks.slice();\n  }\n}\nfunction patchEventPrototype(global, api) {\n  const Event = global['Event'];\n  if (Event && Event.prototype) {\n    api.patchMethod(Event.prototype, 'stopImmediatePropagation', delegate => function (self, args) {\n      self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n      // we need to call the native stopImmediatePropagation\n      // in case in some hybrid application, some part of\n      // application will be controlled by zone, some are not\n      delegate && delegate.apply(self, args);\n    });\n  }\n}\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n  const symbol = Zone.__symbol__(method);\n  if (target[symbol]) {\n    return;\n  }\n  const nativeDelegate = target[symbol] = target[method];\n  target[method] = function (name, opts, options) {\n    if (opts && opts.prototype) {\n      callbacks.forEach(function (callback) {\n        const source = `${targetName}.${method}::` + callback;\n        const prototype = opts.prototype;\n        // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n        // `customElements.define`. We explicitly wrap the patching code into try-catch since\n        // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n        // make those properties non-writable. This means that patching callback will throw an error\n        // `cannot assign to read-only property`. See this code as an example:\n        // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n        // We don't want to stop the application rendering if we couldn't patch some\n        // callback, e.g. `attributeChangedCallback`.\n        try {\n          if (prototype.hasOwnProperty(callback)) {\n            const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n            if (descriptor && descriptor.value) {\n              descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n              api._redefineProperty(opts.prototype, callback, descriptor);\n            } else if (prototype[callback]) {\n              prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n            }\n          } else if (prototype[callback]) {\n            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n          }\n        } catch {\n          // Note: we leave the catch block empty since there's no way to handle the error related\n          // to non-writable property.\n        }\n      });\n    }\n    return nativeDelegate.call(target, name, opts, options);\n  };\n  api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction filterProperties(target, onProperties, ignoreProperties) {\n  if (!ignoreProperties || ignoreProperties.length === 0) {\n    return onProperties;\n  }\n  const tip = ignoreProperties.filter(ip => ip.target === target);\n  if (!tip || tip.length === 0) {\n    return onProperties;\n  }\n  const targetIgnoreProperties = tip[0].ignoreProperties;\n  return onProperties.filter(op => targetIgnoreProperties.indexOf(op) === -1);\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n  // check whether target is available, sometimes target will be undefined\n  // because different browser or some 3rd party plugin.\n  if (!target) {\n    return;\n  }\n  const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n  patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\nfunction getOnEventNames(target) {\n  return Object.getOwnPropertyNames(target).filter(name => name.startsWith('on') && name.length > 2).map(name => name.substring(2));\n}\nfunction propertyDescriptorPatch(api, _global) {\n  if (isNode && !isMix) {\n    return;\n  }\n  if (Zone[api.symbol('patchEvents')]) {\n    // events are already been patched by legacy patch.\n    return;\n  }\n  const ignoreProperties = _global['__Zone_ignore_on_properties'];\n  // for browsers that we can patch the descriptor:  Chrome & Firefox\n  let patchTargets = [];\n  if (isBrowser) {\n    const internalWindow = window;\n    patchTargets = patchTargets.concat(['Document', 'SVGElement', 'Element', 'HTMLElement', 'HTMLBodyElement', 'HTMLMediaElement', 'HTMLFrameSetElement', 'HTMLFrameElement', 'HTMLIFrameElement', 'HTMLMarqueeElement', 'Worker']);\n    const ignoreErrorProperties = isIE() ? [{\n      target: internalWindow,\n      ignoreProperties: ['error']\n    }] : [];\n    // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n    // so we need to pass WindowPrototype to check onProp exist or not\n    patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n  }\n  patchTargets = patchTargets.concat(['XMLHttpRequest', 'XMLHttpRequestEventTarget', 'IDBIndex', 'IDBRequest', 'IDBOpenDBRequest', 'IDBDatabase', 'IDBTransaction', 'IDBCursor', 'WebSocket']);\n  for (let i = 0; i < patchTargets.length; i++) {\n    const target = _global[patchTargets[i]];\n    target && target.prototype && patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n  }\n}\nZone.__load_patch('util', (global, Zone, api) => {\n  // Collect native event names by looking at properties\n  // on the global namespace, e.g. 'onclick'.\n  const eventNames = getOnEventNames(global);\n  api.patchOnProperties = patchOnProperties;\n  api.patchMethod = patchMethod;\n  api.bindArguments = bindArguments;\n  api.patchMacroTask = patchMacroTask;\n  // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS` to\n  // define which events will not be patched by `Zone.js`.\n  // In newer version (>=0.9.0), we change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep\n  // the name consistent with angular repo.\n  // The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be supported for\n  // backwards compatibility.\n  const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n  const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n  if (global[SYMBOL_UNPATCHED_EVENTS]) {\n    global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n  }\n  if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n    Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] = global[SYMBOL_BLACK_LISTED_EVENTS];\n  }\n  api.patchEventPrototype = patchEventPrototype;\n  api.patchEventTarget = patchEventTarget;\n  api.isIEOrEdge = isIEOrEdge;\n  api.ObjectDefineProperty = ObjectDefineProperty;\n  api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n  api.ObjectCreate = ObjectCreate;\n  api.ArraySlice = ArraySlice;\n  api.patchClass = patchClass;\n  api.wrapWithCurrentZone = wrapWithCurrentZone;\n  api.filterProperties = filterProperties;\n  api.attachOriginToPatched = attachOriginToPatched;\n  api._redefineProperty = Object.defineProperty;\n  api.patchCallbacks = patchCallbacks;\n  api.getGlobalObjects = () => ({\n    globalSources,\n    zoneSymbolEventNames,\n    eventNames,\n    isBrowser,\n    isMix,\n    isNode,\n    TRUE_STR,\n    FALSE_STR,\n    ZONE_SYMBOL_PREFIX,\n    ADD_EVENT_LISTENER_STR,\n    REMOVE_EVENT_LISTENER_STR\n  });\n});\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchQueueMicrotask(global, api) {\n  api.patchMethod(global, 'queueMicrotask', delegate => {\n    return function (self, args) {\n      Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n    };\n  });\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n  let setNative = null;\n  let clearNative = null;\n  setName += nameSuffix;\n  cancelName += nameSuffix;\n  const tasksByHandleId = {};\n  function scheduleTask(task) {\n    const data = task.data;\n    data.args[0] = function () {\n      return task.invoke.apply(this, arguments);\n    };\n    data.handleId = setNative.apply(window, data.args);\n    return task;\n  }\n  function clearTask(task) {\n    return clearNative.call(window, task.data.handleId);\n  }\n  setNative = patchMethod(window, setName, delegate => function (self, args) {\n    if (typeof args[0] === 'function') {\n      const options = {\n        isPeriodic: nameSuffix === 'Interval',\n        delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n        args: args\n      };\n      const callback = args[0];\n      args[0] = function timer() {\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          // issue-934, task will be cancelled\n          // even it is a periodic task such as\n          // setInterval\n          // https://github.com/angular/angular/issues/40387\n          // Cleanup tasksByHandleId should be handled before scheduleTask\n          // Since some zoneSpec may intercept and doesn't trigger\n          // scheduleFn(scheduleTask) provided here.\n          if (!options.isPeriodic) {\n            if (typeof options.handleId === 'number') {\n              // in non-nodejs env, we remove timerId\n              // from local cache\n              delete tasksByHandleId[options.handleId];\n            } else if (options.handleId) {\n              // Node returns complex objects as handleIds\n              // we remove task reference from timer object\n              options.handleId[taskSymbol] = null;\n            }\n          }\n        }\n      };\n      const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n      if (!task) {\n        return task;\n      }\n      // Node.js must additionally support the ref and unref functions.\n      const handle = task.data.handleId;\n      if (typeof handle === 'number') {\n        // for non nodejs env, we save handleId: task\n        // mapping in local cache for clearTimeout\n        tasksByHandleId[handle] = task;\n      } else if (handle) {\n        // for nodejs env, we save task\n        // reference in timerId Object for clearTimeout\n        handle[taskSymbol] = task;\n      }\n      // check whether handle is null, because some polyfill or browser\n      // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n      if (handle && handle.ref && handle.unref && typeof handle.ref === 'function' && typeof handle.unref === 'function') {\n        task.ref = handle.ref.bind(handle);\n        task.unref = handle.unref.bind(handle);\n      }\n      if (typeof handle === 'number' || handle) {\n        return handle;\n      }\n      return task;\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(window, args);\n    }\n  });\n  clearNative = patchMethod(window, cancelName, delegate => function (self, args) {\n    const id = args[0];\n    let task;\n    if (typeof id === 'number') {\n      // non nodejs env.\n      task = tasksByHandleId[id];\n    } else {\n      // nodejs env.\n      task = id && id[taskSymbol];\n      // other environments.\n      if (!task) {\n        task = id;\n      }\n    }\n    if (task && typeof task.type === 'string') {\n      if (task.state !== 'notScheduled' && (task.cancelFn && task.data.isPeriodic || task.runCount === 0)) {\n        if (typeof id === 'number') {\n          delete tasksByHandleId[id];\n        } else if (id) {\n          id[taskSymbol] = null;\n        }\n        // Do not cancel already canceled functions\n        task.zone.cancelTask(task);\n      }\n    } else {\n      // cause an error by calling it directly.\n      delegate.apply(window, args);\n    }\n  });\n}\nfunction patchCustomElements(_global, api) {\n  const {\n    isBrowser,\n    isMix\n  } = api.getGlobalObjects();\n  if (!isBrowser && !isMix || !_global['customElements'] || !('customElements' in _global)) {\n    return;\n  }\n  const callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback'];\n  api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\nfunction eventTargetPatch(_global, api) {\n  if (Zone[api.symbol('patchEventTarget')]) {\n    // EventTarget is already patched.\n    return;\n  }\n  const {\n    eventNames,\n    zoneSymbolEventNames,\n    TRUE_STR,\n    FALSE_STR,\n    ZONE_SYMBOL_PREFIX\n  } = api.getGlobalObjects();\n  //  predefine all __zone_symbol__ + eventName + true/false string\n  for (let i = 0; i < eventNames.length; i++) {\n    const eventName = eventNames[i];\n    const falseEventName = eventName + FALSE_STR;\n    const trueEventName = eventName + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n  }\n  const EVENT_TARGET = _global['EventTarget'];\n  if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n    return;\n  }\n  api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n  return true;\n}\nfunction patchEvent(global, api) {\n  api.patchEventPrototype(global, api);\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nZone.__load_patch('legacy', global => {\n  const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n  if (legacyPatch) {\n    legacyPatch();\n  }\n});\nZone.__load_patch('timers', global => {\n  const set = 'set';\n  const clear = 'clear';\n  patchTimer(global, set, clear, 'Timeout');\n  patchTimer(global, set, clear, 'Interval');\n  patchTimer(global, set, clear, 'Immediate');\n});\nZone.__load_patch('requestAnimationFrame', global => {\n  patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n  patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n  patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n});\nZone.__load_patch('blocking', (global, Zone) => {\n  const blockingMethods = ['alert', 'prompt', 'confirm'];\n  for (let i = 0; i < blockingMethods.length; i++) {\n    const name = blockingMethods[i];\n    patchMethod(global, name, (delegate, symbol, name) => {\n      return function (s, args) {\n        return Zone.current.run(delegate, global, args, name);\n      };\n    });\n  }\n});\nZone.__load_patch('EventTarget', (global, Zone, api) => {\n  patchEvent(global, api);\n  eventTargetPatch(global, api);\n  // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n  const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n  if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n    api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n  }\n});\nZone.__load_patch('MutationObserver', (global, Zone, api) => {\n  patchClass('MutationObserver');\n  patchClass('WebKitMutationObserver');\n});\nZone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n  patchClass('IntersectionObserver');\n});\nZone.__load_patch('FileReader', (global, Zone, api) => {\n  patchClass('FileReader');\n});\nZone.__load_patch('on_property', (global, Zone, api) => {\n  propertyDescriptorPatch(api, global);\n});\nZone.__load_patch('customElements', (global, Zone, api) => {\n  patchCustomElements(global, api);\n});\nZone.__load_patch('XHR', (global, Zone) => {\n  // Treat XMLHttpRequest as a macrotask.\n  patchXHR(global);\n  const XHR_TASK = zoneSymbol('xhrTask');\n  const XHR_SYNC = zoneSymbol('xhrSync');\n  const XHR_LISTENER = zoneSymbol('xhrListener');\n  const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n  const XHR_URL = zoneSymbol('xhrURL');\n  const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n  function patchXHR(window) {\n    const XMLHttpRequest = window['XMLHttpRequest'];\n    if (!XMLHttpRequest) {\n      // XMLHttpRequest is not available in service worker\n      return;\n    }\n    const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n    function findPendingTask(target) {\n      return target[XHR_TASK];\n    }\n    let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n    let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n    if (!oriAddListener) {\n      const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n      if (XMLHttpRequestEventTarget) {\n        const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n        oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n        oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n      }\n    }\n    const READY_STATE_CHANGE = 'readystatechange';\n    const SCHEDULED = 'scheduled';\n    function scheduleTask(task) {\n      const data = task.data;\n      const target = data.target;\n      target[XHR_SCHEDULED] = false;\n      target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n      // remove existing event listener\n      const listener = target[XHR_LISTENER];\n      if (!oriAddListener) {\n        oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n        oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n      }\n      if (listener) {\n        oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n      }\n      const newListener = target[XHR_LISTENER] = () => {\n        if (target.readyState === target.DONE) {\n          // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n          // readyState=4 multiple times, so we need to check task state here\n          if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n            // check whether the xhr has registered onload listener\n            // if that is the case, the task should invoke after all\n            // onload listeners finish.\n            // Also if the request failed without response (status = 0), the load event handler\n            // will not be triggered, in that case, we should also invoke the placeholder callback\n            // to close the XMLHttpRequest::send macroTask.\n            // https://github.com/angular/angular/issues/38795\n            const loadTasks = target[Zone.__symbol__('loadfalse')];\n            if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n              const oriInvoke = task.invoke;\n              task.invoke = function () {\n                // need to load the tasks again, because in other\n                // load listener, they may remove themselves\n                const loadTasks = target[Zone.__symbol__('loadfalse')];\n                for (let i = 0; i < loadTasks.length; i++) {\n                  if (loadTasks[i] === task) {\n                    loadTasks.splice(i, 1);\n                  }\n                }\n                if (!data.aborted && task.state === SCHEDULED) {\n                  oriInvoke.call(task);\n                }\n              };\n              loadTasks.push(task);\n            } else {\n              task.invoke();\n            }\n          } else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n            // error occurs when xhr.send()\n            target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n          }\n        }\n      };\n      oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n      const storedTask = target[XHR_TASK];\n      if (!storedTask) {\n        target[XHR_TASK] = task;\n      }\n      sendNative.apply(target, data.args);\n      target[XHR_SCHEDULED] = true;\n      return task;\n    }\n    function placeholderCallback() {}\n    function clearTask(task) {\n      const data = task.data;\n      // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n      // to prevent it from firing. So instead, we store info for the event listener.\n      data.aborted = true;\n      return abortNative.apply(data.target, data.args);\n    }\n    const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n      self[XHR_SYNC] = args[2] == false;\n      self[XHR_URL] = args[1];\n      return openNative.apply(self, args);\n    });\n    const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n    const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n    const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n    const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n      if (Zone.current[fetchTaskScheduling] === true) {\n        // a fetch is scheduling, so we are using xhr to polyfill fetch\n        // and because we already schedule macroTask for fetch, we should\n        // not schedule a macroTask for xhr again\n        return sendNative.apply(self, args);\n      }\n      if (self[XHR_SYNC]) {\n        // if the XHR is sync there is no task to schedule, just execute the code.\n        return sendNative.apply(self, args);\n      } else {\n        const options = {\n          target: self,\n          url: self[XHR_URL],\n          isPeriodic: false,\n          args: args,\n          aborted: false\n        };\n        const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n        if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted && task.state === SCHEDULED) {\n          // xhr request throw error when send\n          // we should invoke task instead of leaving a scheduled\n          // pending macroTask\n          task.invoke();\n        }\n      }\n    });\n    const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n      const task = findPendingTask(self);\n      if (task && typeof task.type == 'string') {\n        // If the XHR has already completed, do nothing.\n        // If the XHR has already been aborted, do nothing.\n        // Fix #569, call abort multiple times before done will cause\n        // macroTask task count be negative number\n        if (task.cancelFn == null || task.data && task.data.aborted) {\n          return;\n        }\n        task.zone.cancelTask(task);\n      } else if (Zone.current[fetchTaskAborting] === true) {\n        // the abort is called from fetch polyfill, we need to call native abort of XHR.\n        return abortNative.apply(self, args);\n      }\n      // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n      // task\n      // to cancel. Do nothing.\n    });\n  }\n});\n\nZone.__load_patch('geolocation', global => {\n  /// GEO_LOCATION\n  if (global['navigator'] && global['navigator'].geolocation) {\n    patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n  }\n});\nZone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n  // handle unhandled promise rejection\n  function findPromiseRejectionHandler(evtName) {\n    return function (e) {\n      const eventTasks = findEventTasks(global, evtName);\n      eventTasks.forEach(eventTask => {\n        // windows has added unhandledrejection event listener\n        // trigger the event listener\n        const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n        if (PromiseRejectionEvent) {\n          const evt = new PromiseRejectionEvent(evtName, {\n            promise: e.promise,\n            reason: e.rejection\n          });\n          eventTask.invoke(evt);\n        }\n      });\n    };\n  }\n  if (global['PromiseRejectionEvent']) {\n    Zone[zoneSymbol('unhandledPromiseRejectionHandler')] = findPromiseRejectionHandler('unhandledrejection');\n    Zone[zoneSymbol('rejectionHandledHandler')] = findPromiseRejectionHandler('rejectionhandled');\n  }\n});\nZone.__load_patch('queueMicrotask', (global, Zone, api) => {\n  patchQueueMicrotask(global, api);\n});", "map": {"version": 3, "names": ["global", "performance", "mark", "name", "performanceMeasure", "label", "symbolPrefix", "__symbol__", "checkDuplicate", "Error", "Zone", "_", "assertZonePatched", "patches", "root", "zone", "current", "parent", "_currentZoneFrame", "currentTask", "_currentTask", "__load_patch", "fn", "ignoreDuplicate", "hasOwnProperty", "perfName", "_api", "_parent", "_name", "constructor", "zoneSpec", "_properties", "properties", "_zoneDelegate", "_ZoneDelegate", "get", "key", "getZoneWith", "fork", "wrap", "callback", "source", "_callback", "intercept", "runGuarded", "arguments", "run", "applyThis", "applyArgs", "invoke", "error", "handleError", "runTask", "task", "NO_ZONE", "state", "notScheduled", "type", "eventTask", "macroTask", "re<PERSON><PERSON><PERSON><PERSON><PERSON>", "running", "_transitionTo", "scheduled", "runCount", "previousTask", "data", "isPeriodic", "cancelFn", "undefined", "invokeTask", "unknown", "_updateTaskCount", "scheduleTask", "newZone", "scheduling", "zoneDelegates", "_zoneDelegates", "_zone", "err", "scheduleMicroTask", "customSchedule", "ZoneTask", "microTask", "scheduleMacroTask", "customCancel", "scheduleEventTask", "cancelTask", "canceling", "count", "i", "length", "DELEGATE_ZS", "onHasTask", "delegate", "target", "hasTaskState", "hasTask", "onScheduleTask", "onInvokeTask", "onCancelTask", "parentDelegate", "_taskCounts", "_parentDelegate", "_forkZS", "onFork", "_forkDlgt", "_forkCurrZone", "_interceptZS", "onIntercept", "_interceptDlgt", "_interceptCurrZone", "_invokeZS", "onInvoke", "_invokeDlgt", "_invokeCurrZone", "_handleErrorZS", "onHandleError", "_handleErrorDlgt", "_handleErrorCurrZone", "_scheduleTaskZS", "_scheduleTaskDlgt", "_scheduleTaskCurrZone", "_invokeTaskZS", "_invokeTaskDlgt", "_invokeTaskCurrZone", "_cancelTaskZS", "_cancelTaskDlgt", "_cancelTaskCurrZone", "_hasTaskZS", "_hasTaskDlgt", "_hasTaskDlgtOwner", "_hasTaskCurrZone", "zoneSpecHasTask", "parentHasTask", "targetZone", "apply", "returnTask", "push", "scheduleFn", "value", "isEmpty", "counts", "prev", "next", "change", "options", "_state", "self", "useG", "call", "args", "_numberOfNestedTaskFrames", "drainMicroTaskQueue", "cancelScheduleRequest", "toState", "fromState1", "fromState2", "toString", "handleId", "Object", "prototype", "toJSON", "symbolSetTimeout", "symbolPromise", "symbolThen", "_microTaskQueue", "_isDrainingMicrotaskQueue", "nativeMicroTaskQueuePromise", "nativeScheduleMicroTask", "func", "resolve", "nativeThen", "queue", "onUnhandledError", "microtaskDrainDone", "symbol", "currentZoneFrame", "noop", "showUncaughtError", "patchEventTarget", "patchOnProperties", "patchMethod", "bindArguments", "patchThen", "patchMacroTask", "patchEventPrototype", "isIEOrEdge", "getGlobalObjects", "ObjectDefineProperty", "ObjectGetOwnPropertyDescriptor", "ObjectCreate", "ArraySlice", "patchClass", "wrapWithCurrentZone", "filterProperties", "attachOriginToPatched", "_redefineProperty", "patchCallbacks", "window", "getOwnPropertyDescriptor", "defineProperty", "ObjectGetPrototypeOf", "getPrototypeOf", "create", "Array", "slice", "ADD_EVENT_LISTENER_STR", "REMOVE_EVENT_LISTENER_STR", "ZONE_SYMBOL_ADD_EVENT_LISTENER", "ZONE_SYMBOL_REMOVE_EVENT_LISTENER", "TRUE_STR", "FALSE_STR", "ZONE_SYMBOL_PREFIX", "scheduleMacroTaskWithCurrentZone", "zoneSymbol", "isWindowExists", "internalWindow", "_global", "REMOVE_ATTRIBUTE", "patchPrototype", "fnNames", "prototypeDesc", "isPropertyWritable", "patched", "propertyDesc", "writable", "set", "isWebWorker", "WorkerGlobalScope", "isNode", "process", "<PERSON><PERSON><PERSON><PERSON>", "isMix", "zoneSymbolEventNames$1", "wrapFn", "event", "eventNameSymbol", "listener", "result", "errorEvent", "message", "filename", "lineno", "colno", "preventDefault", "patchProperty", "obj", "prop", "desc", "enumerable", "configurable", "onPropPatchedSymbol", "originalDescGet", "originalDescSet", "eventName", "newValue", "previousValue", "removeEventListener", "addEventListener", "removeAttribute", "onProperties", "j", "originalInstanceKey", "className", "OriginalClass", "a", "instance", "patchFn", "proto", "<PERSON><PERSON><PERSON>", "patchDelegate", "funcName", "metaCreator", "setNative", "cbIdx", "meta", "original", "isDetectedIEOrEdge", "ieOrEdge", "isIE", "ua", "navigator", "userAgent", "indexOf", "api", "readableObjectToString", "JSON", "stringify", "_uncaughtPromiseErrors", "isDisableWrappingUncaughtPromiseRejection", "creationTrace", "e", "rejection", "console", "stack", "uncaughtPromiseError", "shift", "throwOriginal", "handleUnhandledRejection", "UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL", "handler", "isThenable", "then", "forwardResolution", "forwardRejection", "ZoneAwarePromise", "reject", "symbolState", "symbolValue", "symbolFinally", "symbolParentPromiseValue", "symbolParentPromiseState", "UNRESOLVED", "RESOLVED", "REJECTED", "REJECTED_NO_CATCH", "makeResolver", "promise", "v", "resolvePromise", "once", "wasCalled", "wrapper", "wrappedFunction", "TYPE_ERROR", "CURRENT_TASK_TRACE_SYMBOL", "onceWrapper", "TypeError", "clearRejectedNoCatch", "trace", "scheduleResolveOrReject", "REJECTION_HANDLED_HANDLER", "splice", "chainPromise", "onFulfilled", "onRejected", "promiseState", "parentPromiseValue", "isFinallyPromise", "ZONE_AWARE_PROMISE_TO_STRING", "AggregateError", "any", "values", "Symbol", "iterator", "Promise", "promises", "finished", "errors", "race", "res", "rej", "onResolve", "onReject", "all", "allWithCallback", "allSettled", "P", "then<PERSON>allback", "status", "<PERSON><PERSON><PERSON><PERSON>", "reason", "unresolvedCount", "valueIndex", "resolvedV<PERSON>ues", "curValueIndex", "thenErr", "executor", "toStringTag", "species", "C", "catch", "finally", "onFinally", "NativePromise", "symbolThenPatched", "Ctor", "originalThen", "wrapped", "zoneify", "resultPromise", "ctor", "originalFunctionToString", "Function", "ORIGINAL_DELEGATE_SYMBOL", "PROMISE_SYMBOL", "ERROR_SYMBOL", "newFunctionToString", "originalDelegate", "nativePromise", "nativeError", "originalObjectToString", "PROMISE_OBJECT_TO_STRING", "passiveSupported", "OPTIMIZED_ZONE_EVENT_TASK_DATA", "zoneSymbolEventNames", "globalSources", "EVENT_NAME_SYMBOL_REGX", "RegExp", "IMMEDIATE_PROPAGATION_SYMBOL", "prepareEventNames", "eventNameToString", "falseEventName", "trueEventName", "symbolCapture", "apis", "patchOptions", "ADD_EVENT_LISTENER", "add", "REMOVE_EVENT_LISTENER", "rm", "LISTENERS_EVENT_LISTENER", "listeners", "REMOVE_ALL_LISTENERS_EVENT_LISTENER", "rmAll", "zoneSymbolAddEventListener", "ADD_EVENT_LISTENER_SOURCE", "PREPEND_EVENT_LISTENER", "PREPEND_EVENT_LISTENER_SOURCE", "isRemoved", "handleEvent", "globalCallback", "context", "isCapture", "tasks", "copyTasks", "globalZoneAwareCallback", "globalZoneAwareCaptureCallback", "patchEventTargetMethods", "useGlobalCallback", "validate<PERSON><PERSON><PERSON>", "vh", "chkDup", "<PERSON><PERSON><PERSON><PERSON>", "rt", "taskData", "nativeAddEventListener", "nativeRemoveEventListener", "nativeListeners", "nativeRemoveAllListeners", "nativePrependEventListener", "prepend", "buildEventListenerOptions", "passive", "capture", "customScheduleGlobal", "isExisting", "customCancelGlobal", "symbolEventNames", "symbolEventName", "existingTasks", "existingTask", "allRemoved", "customScheduleNonGlobal", "customSchedulePrepend", "customCancelNonGlobal", "compareTaskCallbackVsDelegate", "typeOfDelegate", "compare", "diff", "unpatchedEvents", "passiveEvents", "makeAddListener", "nativeListener", "addSource", "customScheduleFn", "customCancelFn", "transferEventName", "isHandleEvent", "constructorName", "targetSource", "unshift", "onPropertySymbol", "findEventTasks", "keys", "match", "exec", "evtName", "symbolCaptureEventName", "captureTasks", "removeTasks", "results", "foundTasks", "captureFalseTasks", "captureTrueTasks", "concat", "Event", "targetName", "method", "callbacks", "nativeDelegate", "opts", "for<PERSON>ach", "descriptor", "ignoreProperties", "tip", "filter", "ip", "targetIgnoreProperties", "op", "patchFilteredProperties", "filteredProperties", "getOnEventNames", "getOwnPropertyNames", "startsWith", "map", "substring", "propertyDescriptorPatch", "patchTargets", "ignoreErrorProperties", "eventNames", "SYMBOL_BLACK_LISTED_EVENTS", "SYMBOL_UNPATCHED_EVENTS", "patchQueueMicrotask", "taskSymbol", "patchTimer", "setName", "cancelName", "nameSuffix", "clearNative", "tasksByHandleId", "clearTask", "delay", "timer", "handle", "ref", "unref", "bind", "id", "patchCustomElements", "customElements", "eventTargetPatch", "EVENT_TARGET", "patchEvent", "legacyPatch", "clear", "blockingMethods", "s", "XMLHttpRequestEventTarget", "patchXHR", "XHR_TASK", "XHR_SYNC", "XHR_LISTENER", "XHR_SCHEDULED", "XHR_URL", "XHR_ERROR_BEFORE_SCHEDULED", "XMLHttpRequest", "XMLHttpRequestPrototype", "findPendingTask", "oriAddListener", "oriRemoveListener", "XMLHttpRequestEventTargetPrototype", "READY_STATE_CHANGE", "SCHEDULED", "newListener", "readyState", "DONE", "aborted", "loadTasks", "oriInvoke", "storedTask", "sendNative", "placeholder<PERSON><PERSON><PERSON>", "abortNative", "openNative", "XMLHTTPREQUEST_SOURCE", "fetchTaskAborting", "fetchTaskScheduling", "url", "geolocation", "findPromiseRejectionHandler", "eventTasks", "PromiseRejectionEvent", "evt"], "sources": ["C:/Users/<USER>/PycharmProjects/GenAI/Borys-Slavic-News-Chatbot/news_separate/frontend/node_modules/zone.js/fesm2015/zone.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n((function (global) {\n    const performance = global['performance'];\n    function mark(name) {\n        performance && performance['mark'] && performance['mark'](name);\n    }\n    function performanceMeasure(name, label) {\n        performance && performance['measure'] && performance['measure'](name, label);\n    }\n    mark('Zone');\n    // Initialize before it's accessed below.\n    // __Zone_symbol_prefix global can be used to override the default zone\n    // symbol prefix with a custom one if needed.\n    const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n    function __symbol__(name) {\n        return symbolPrefix + name;\n    }\n    const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n    if (global['Zone']) {\n        // if global['Zone'] already exists (maybe zone.js was already loaded or\n        // some other lib also registered a global object named Zone), we may need\n        // to throw an error, but sometimes user may not want this error.\n        // For example,\n        // we have two web pages, page1 includes zone.js, page2 doesn't.\n        // and the 1st time user load page1 and page2, everything work fine,\n        // but when user load page2 again, error occurs because global['Zone'] already exists.\n        // so we add a flag to let user choose whether to throw this error or not.\n        // By default, if existing Zone is from zone.js, we will not throw the error.\n        if (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function') {\n            throw new Error('Zone already loaded.');\n        }\n        else {\n            return global['Zone'];\n        }\n    }\n    class Zone {\n        // tslint:disable-next-line:require-internal-with-underscore\n        static { this.__symbol__ = __symbol__; }\n        static assertZonePatched() {\n            if (global['Promise'] !== patches['ZoneAwarePromise']) {\n                throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' +\n                    'has been overwritten.\\n' +\n                    'Most likely cause is that a Promise polyfill has been loaded ' +\n                    'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' +\n                    'If you must load one, do so before loading zone.js.)');\n            }\n        }\n        static get root() {\n            let zone = Zone.current;\n            while (zone.parent) {\n                zone = zone.parent;\n            }\n            return zone;\n        }\n        static get current() {\n            return _currentZoneFrame.zone;\n        }\n        static get currentTask() {\n            return _currentTask;\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        static __load_patch(name, fn, ignoreDuplicate = false) {\n            if (patches.hasOwnProperty(name)) {\n                // `checkDuplicate` option is defined from global variable\n                // so it works for all modules.\n                // `ignoreDuplicate` can work for the specified module\n                if (!ignoreDuplicate && checkDuplicate) {\n                    throw Error('Already loaded patch: ' + name);\n                }\n            }\n            else if (!global['__Zone_disable_' + name]) {\n                const perfName = 'Zone:' + name;\n                mark(perfName);\n                patches[name] = fn(global, Zone, _api);\n                performanceMeasure(perfName, perfName);\n            }\n        }\n        get parent() {\n            return this._parent;\n        }\n        get name() {\n            return this._name;\n        }\n        constructor(parent, zoneSpec) {\n            this._parent = parent;\n            this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n            this._properties = zoneSpec && zoneSpec.properties || {};\n            this._zoneDelegate =\n                new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n        }\n        get(key) {\n            const zone = this.getZoneWith(key);\n            if (zone)\n                return zone._properties[key];\n        }\n        getZoneWith(key) {\n            let current = this;\n            while (current) {\n                if (current._properties.hasOwnProperty(key)) {\n                    return current;\n                }\n                current = current._parent;\n            }\n            return null;\n        }\n        fork(zoneSpec) {\n            if (!zoneSpec)\n                throw new Error('ZoneSpec required!');\n            return this._zoneDelegate.fork(this, zoneSpec);\n        }\n        wrap(callback, source) {\n            if (typeof callback !== 'function') {\n                throw new Error('Expecting function got: ' + callback);\n            }\n            const _callback = this._zoneDelegate.intercept(this, callback, source);\n            const zone = this;\n            return function () {\n                return zone.runGuarded(_callback, this, arguments, source);\n            };\n        }\n        run(callback, applyThis, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runGuarded(callback, applyThis = null, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                try {\n                    return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runTask(task, applyThis, applyArgs) {\n            if (task.zone != this) {\n                throw new Error('A task can only be run in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n            }\n            // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n            // will run in notScheduled(canceled) state, we should not try to\n            // run such kind of task but just return\n            if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n                return;\n            }\n            const reEntryGuard = task.state != running;\n            reEntryGuard && task._transitionTo(running, scheduled);\n            task.runCount++;\n            const previousTask = _currentTask;\n            _currentTask = task;\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n                    task.cancelFn = undefined;\n                }\n                try {\n                    return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                // if the task's state is notScheduled or unknown, then it has already been cancelled\n                // we should not reset the state to scheduled\n                if (task.state !== notScheduled && task.state !== unknown) {\n                    if (task.type == eventTask || (task.data && task.data.isPeriodic)) {\n                        reEntryGuard && task._transitionTo(scheduled, running);\n                    }\n                    else {\n                        task.runCount = 0;\n                        this._updateTaskCount(task, -1);\n                        reEntryGuard &&\n                            task._transitionTo(notScheduled, running, notScheduled);\n                    }\n                }\n                _currentZoneFrame = _currentZoneFrame.parent;\n                _currentTask = previousTask;\n            }\n        }\n        scheduleTask(task) {\n            if (task.zone && task.zone !== this) {\n                // check if the task was rescheduled, the newZone\n                // should not be the children of the original zone\n                let newZone = this;\n                while (newZone) {\n                    if (newZone === task.zone) {\n                        throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n                    }\n                    newZone = newZone.parent;\n                }\n            }\n            task._transitionTo(scheduling, notScheduled);\n            const zoneDelegates = [];\n            task._zoneDelegates = zoneDelegates;\n            task._zone = this;\n            try {\n                task = this._zoneDelegate.scheduleTask(this, task);\n            }\n            catch (err) {\n                // should set task's state to unknown when scheduleTask throw error\n                // because the err may from reschedule, so the fromState maybe notScheduled\n                task._transitionTo(unknown, scheduling, notScheduled);\n                // TODO: @JiaLiPassion, should we check the result from handleError?\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            if (task._zoneDelegates === zoneDelegates) {\n                // we have to check because internally the delegate can reschedule the task.\n                this._updateTaskCount(task, 1);\n            }\n            if (task.state == scheduling) {\n                task._transitionTo(scheduled, scheduling);\n            }\n            return task;\n        }\n        scheduleMicroTask(source, callback, data, customSchedule) {\n            return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n        }\n        scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n        }\n        scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n        }\n        cancelTask(task) {\n            if (task.zone != this)\n                throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n            if (task.state !== scheduled && task.state !== running) {\n                return;\n            }\n            task._transitionTo(canceling, scheduled, running);\n            try {\n                this._zoneDelegate.cancelTask(this, task);\n            }\n            catch (err) {\n                // if error occurs when cancelTask, transit the state to unknown\n                task._transitionTo(unknown, canceling);\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            this._updateTaskCount(task, -1);\n            task._transitionTo(notScheduled, canceling);\n            task.runCount = 0;\n            return task;\n        }\n        _updateTaskCount(task, count) {\n            const zoneDelegates = task._zoneDelegates;\n            if (count == -1) {\n                task._zoneDelegates = null;\n            }\n            for (let i = 0; i < zoneDelegates.length; i++) {\n                zoneDelegates[i]._updateTaskCount(task.type, count);\n            }\n        }\n    }\n    const DELEGATE_ZS = {\n        name: '',\n        onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n        onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n        onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n        onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task)\n    };\n    class _ZoneDelegate {\n        constructor(zone, parentDelegate, zoneSpec) {\n            this._taskCounts = { 'microTask': 0, 'macroTask': 0, 'eventTask': 0 };\n            this.zone = zone;\n            this._parentDelegate = parentDelegate;\n            this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n            this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n            this._forkCurrZone =\n                zoneSpec && (zoneSpec.onFork ? this.zone : parentDelegate._forkCurrZone);\n            this._interceptZS =\n                zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n            this._interceptDlgt =\n                zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n            this._interceptCurrZone =\n                zoneSpec && (zoneSpec.onIntercept ? this.zone : parentDelegate._interceptCurrZone);\n            this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n            this._invokeDlgt =\n                zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n            this._invokeCurrZone =\n                zoneSpec && (zoneSpec.onInvoke ? this.zone : parentDelegate._invokeCurrZone);\n            this._handleErrorZS =\n                zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n            this._handleErrorDlgt =\n                zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n            this._handleErrorCurrZone =\n                zoneSpec && (zoneSpec.onHandleError ? this.zone : parentDelegate._handleErrorCurrZone);\n            this._scheduleTaskZS =\n                zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n            this._scheduleTaskDlgt = zoneSpec &&\n                (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n            this._scheduleTaskCurrZone =\n                zoneSpec && (zoneSpec.onScheduleTask ? this.zone : parentDelegate._scheduleTaskCurrZone);\n            this._invokeTaskZS =\n                zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n            this._invokeTaskDlgt =\n                zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n            this._invokeTaskCurrZone =\n                zoneSpec && (zoneSpec.onInvokeTask ? this.zone : parentDelegate._invokeTaskCurrZone);\n            this._cancelTaskZS =\n                zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n            this._cancelTaskDlgt =\n                zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n            this._cancelTaskCurrZone =\n                zoneSpec && (zoneSpec.onCancelTask ? this.zone : parentDelegate._cancelTaskCurrZone);\n            this._hasTaskZS = null;\n            this._hasTaskDlgt = null;\n            this._hasTaskDlgtOwner = null;\n            this._hasTaskCurrZone = null;\n            const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n            const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n            if (zoneSpecHasTask || parentHasTask) {\n                // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n                // a case all task related interceptors must go through this ZD. We can't short circuit it.\n                this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n                this._hasTaskDlgt = parentDelegate;\n                this._hasTaskDlgtOwner = this;\n                this._hasTaskCurrZone = zone;\n                if (!zoneSpec.onScheduleTask) {\n                    this._scheduleTaskZS = DELEGATE_ZS;\n                    this._scheduleTaskDlgt = parentDelegate;\n                    this._scheduleTaskCurrZone = this.zone;\n                }\n                if (!zoneSpec.onInvokeTask) {\n                    this._invokeTaskZS = DELEGATE_ZS;\n                    this._invokeTaskDlgt = parentDelegate;\n                    this._invokeTaskCurrZone = this.zone;\n                }\n                if (!zoneSpec.onCancelTask) {\n                    this._cancelTaskZS = DELEGATE_ZS;\n                    this._cancelTaskDlgt = parentDelegate;\n                    this._cancelTaskCurrZone = this.zone;\n                }\n            }\n        }\n        fork(targetZone, zoneSpec) {\n            return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) :\n                new Zone(targetZone, zoneSpec);\n        }\n        intercept(targetZone, callback, source) {\n            return this._interceptZS ?\n                this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) :\n                callback;\n        }\n        invoke(targetZone, callback, applyThis, applyArgs, source) {\n            return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) :\n                callback.apply(applyThis, applyArgs);\n        }\n        handleError(targetZone, error) {\n            return this._handleErrorZS ?\n                this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) :\n                true;\n        }\n        scheduleTask(targetZone, task) {\n            let returnTask = task;\n            if (this._scheduleTaskZS) {\n                if (this._hasTaskZS) {\n                    returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n                }\n                // clang-format off\n                returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n                // clang-format on\n                if (!returnTask)\n                    returnTask = task;\n            }\n            else {\n                if (task.scheduleFn) {\n                    task.scheduleFn(task);\n                }\n                else if (task.type == microTask) {\n                    scheduleMicroTask(task);\n                }\n                else {\n                    throw new Error('Task is missing scheduleFn.');\n                }\n            }\n            return returnTask;\n        }\n        invokeTask(targetZone, task, applyThis, applyArgs) {\n            return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) :\n                task.callback.apply(applyThis, applyArgs);\n        }\n        cancelTask(targetZone, task) {\n            let value;\n            if (this._cancelTaskZS) {\n                value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n            }\n            else {\n                if (!task.cancelFn) {\n                    throw Error('Task is not cancelable');\n                }\n                value = task.cancelFn(task);\n            }\n            return value;\n        }\n        hasTask(targetZone, isEmpty) {\n            // hasTask should not throw error so other ZoneDelegate\n            // can still trigger hasTask callback\n            try {\n                this._hasTaskZS &&\n                    this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n            }\n            catch (err) {\n                this.handleError(targetZone, err);\n            }\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _updateTaskCount(type, count) {\n            const counts = this._taskCounts;\n            const prev = counts[type];\n            const next = counts[type] = prev + count;\n            if (next < 0) {\n                throw new Error('More tasks executed then were scheduled.');\n            }\n            if (prev == 0 || next == 0) {\n                const isEmpty = {\n                    microTask: counts['microTask'] > 0,\n                    macroTask: counts['macroTask'] > 0,\n                    eventTask: counts['eventTask'] > 0,\n                    change: type\n                };\n                this.hasTask(this.zone, isEmpty);\n            }\n        }\n    }\n    class ZoneTask {\n        constructor(type, source, callback, options, scheduleFn, cancelFn) {\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zone = null;\n            this.runCount = 0;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zoneDelegates = null;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._state = 'notScheduled';\n            this.type = type;\n            this.source = source;\n            this.data = options;\n            this.scheduleFn = scheduleFn;\n            this.cancelFn = cancelFn;\n            if (!callback) {\n                throw new Error('callback is not defined');\n            }\n            this.callback = callback;\n            const self = this;\n            // TODO: @JiaLiPassion options should have interface\n            if (type === eventTask && options && options.useG) {\n                this.invoke = ZoneTask.invokeTask;\n            }\n            else {\n                this.invoke = function () {\n                    return ZoneTask.invokeTask.call(global, self, this, arguments);\n                };\n            }\n        }\n        static invokeTask(task, target, args) {\n            if (!task) {\n                task = this;\n            }\n            _numberOfNestedTaskFrames++;\n            try {\n                task.runCount++;\n                return task.zone.runTask(task, target, args);\n            }\n            finally {\n                if (_numberOfNestedTaskFrames == 1) {\n                    drainMicroTaskQueue();\n                }\n                _numberOfNestedTaskFrames--;\n            }\n        }\n        get zone() {\n            return this._zone;\n        }\n        get state() {\n            return this._state;\n        }\n        cancelScheduleRequest() {\n            this._transitionTo(notScheduled, scheduling);\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _transitionTo(toState, fromState1, fromState2) {\n            if (this._state === fromState1 || this._state === fromState2) {\n                this._state = toState;\n                if (toState == notScheduled) {\n                    this._zoneDelegates = null;\n                }\n            }\n            else {\n                throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? ' or \\'' + fromState2 + '\\'' : ''}, was '${this._state}'.`);\n            }\n        }\n        toString() {\n            if (this.data && typeof this.data.handleId !== 'undefined') {\n                return this.data.handleId.toString();\n            }\n            else {\n                return Object.prototype.toString.call(this);\n            }\n        }\n        // add toJSON method to prevent cyclic error when\n        // call JSON.stringify(zoneTask)\n        toJSON() {\n            return {\n                type: this.type,\n                state: this.state,\n                source: this.source,\n                zone: this.zone.name,\n                runCount: this.runCount\n            };\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  MICROTASK QUEUE\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const symbolSetTimeout = __symbol__('setTimeout');\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    let _microTaskQueue = [];\n    let _isDrainingMicrotaskQueue = false;\n    let nativeMicroTaskQueuePromise;\n    function nativeScheduleMicroTask(func) {\n        if (!nativeMicroTaskQueuePromise) {\n            if (global[symbolPromise]) {\n                nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n            }\n        }\n        if (nativeMicroTaskQueuePromise) {\n            let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n            if (!nativeThen) {\n                // native Promise is not patchable, we need to use `then` directly\n                // issue 1078\n                nativeThen = nativeMicroTaskQueuePromise['then'];\n            }\n            nativeThen.call(nativeMicroTaskQueuePromise, func);\n        }\n        else {\n            global[symbolSetTimeout](func, 0);\n        }\n    }\n    function scheduleMicroTask(task) {\n        // if we are not running in any task, and there has not been anything scheduled\n        // we must bootstrap the initial task creation by manually scheduling the drain\n        if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n            // We are not running in Task, so we need to kickstart the microtask queue.\n            nativeScheduleMicroTask(drainMicroTaskQueue);\n        }\n        task && _microTaskQueue.push(task);\n    }\n    function drainMicroTaskQueue() {\n        if (!_isDrainingMicrotaskQueue) {\n            _isDrainingMicrotaskQueue = true;\n            while (_microTaskQueue.length) {\n                const queue = _microTaskQueue;\n                _microTaskQueue = [];\n                for (let i = 0; i < queue.length; i++) {\n                    const task = queue[i];\n                    try {\n                        task.zone.runTask(task, null, null);\n                    }\n                    catch (error) {\n                        _api.onUnhandledError(error);\n                    }\n                }\n            }\n            _api.microtaskDrainDone();\n            _isDrainingMicrotaskQueue = false;\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  BOOTSTRAP\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const NO_ZONE = { name: 'NO ZONE' };\n    const notScheduled = 'notScheduled', scheduling = 'scheduling', scheduled = 'scheduled', running = 'running', canceling = 'canceling', unknown = 'unknown';\n    const microTask = 'microTask', macroTask = 'macroTask', eventTask = 'eventTask';\n    const patches = {};\n    const _api = {\n        symbol: __symbol__,\n        currentZoneFrame: () => _currentZoneFrame,\n        onUnhandledError: noop,\n        microtaskDrainDone: noop,\n        scheduleMicroTask: scheduleMicroTask,\n        showUncaughtError: () => !Zone[__symbol__('ignoreConsoleErrorUncaughtError')],\n        patchEventTarget: () => [],\n        patchOnProperties: noop,\n        patchMethod: () => noop,\n        bindArguments: () => [],\n        patchThen: () => noop,\n        patchMacroTask: () => noop,\n        patchEventPrototype: () => noop,\n        isIEOrEdge: () => false,\n        getGlobalObjects: () => undefined,\n        ObjectDefineProperty: () => noop,\n        ObjectGetOwnPropertyDescriptor: () => undefined,\n        ObjectCreate: () => undefined,\n        ArraySlice: () => [],\n        patchClass: () => noop,\n        wrapWithCurrentZone: () => noop,\n        filterProperties: () => [],\n        attachOriginToPatched: () => noop,\n        _redefineProperty: () => noop,\n        patchCallbacks: () => noop,\n        nativeScheduleMicroTask: nativeScheduleMicroTask\n    };\n    let _currentZoneFrame = { parent: null, zone: new Zone(null, null) };\n    let _currentTask = null;\n    let _numberOfNestedTaskFrames = 0;\n    function noop() { }\n    performanceMeasure('Zone', 'Zone');\n    return global['Zone'] = Zone;\n}))(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n/// <reference types=\"node\"/>\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = Zone.__symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = Zone.__symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = Zone.__symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n    return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n    return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = Zone.__symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = isWindowExists && internalWindow || typeof self === 'object' && self || global;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (typeof args[i] === 'function') {\n            args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n        }\n    }\n    return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n    const source = prototype.constructor['name'];\n    for (let i = 0; i < fnNames.length; i++) {\n        const name = fnNames[i];\n        const delegate = prototype[name];\n        if (delegate) {\n            const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n            if (!isPropertyWritable(prototypeDesc)) {\n                continue;\n            }\n            prototype[name] = ((delegate) => {\n                const patched = function () {\n                    return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n                };\n                attachOriginToPatched(patched, delegate);\n                return patched;\n            })(delegate);\n        }\n    }\n}\nfunction isPropertyWritable(propertyDesc) {\n    if (!propertyDesc) {\n        return true;\n    }\n    if (propertyDesc.writable === false) {\n        return false;\n    }\n    return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = (typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope);\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = (!('nw' in _global) && typeof _global.process !== 'undefined' &&\n    {}.toString.call(_global.process) === '[object process]');\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' &&\n    {}.toString.call(_global.process) === '[object process]' && !isWebWorker &&\n    !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst wrapFn = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n        return;\n    }\n    let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n    }\n    const target = this || event.target || _global;\n    const listener = target[eventNameSymbol];\n    let result;\n    if (isBrowser && target === internalWindow && event.type === 'error') {\n        // window.onerror have different signature\n        // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n        // and onerror callback will prevent default when callback return true\n        const errorEvent = event;\n        result = listener &&\n            listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n        if (result === true) {\n            event.preventDefault();\n        }\n    }\n    else {\n        result = listener && listener.apply(this, arguments);\n        if (result != undefined && !result) {\n            event.preventDefault();\n        }\n    }\n    return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n    let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n    if (!desc && prototype) {\n        // when patch window object, use prototype to check prop exist or not\n        const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n        if (prototypeDesc) {\n            desc = { enumerable: true, configurable: true };\n        }\n    }\n    // if the descriptor not exists or is not configurable\n    // just return\n    if (!desc || !desc.configurable) {\n        return;\n    }\n    const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n    if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n        return;\n    }\n    // A property descriptor cannot have getter/setter and be writable\n    // deleting the writable and value properties avoids this error:\n    //\n    // TypeError: property descriptors must not specify a value or be writable when a\n    // getter or setter has been specified\n    delete desc.writable;\n    delete desc.value;\n    const originalDescGet = desc.get;\n    const originalDescSet = desc.set;\n    // slice(2) cuz 'onclick' -> 'click', etc\n    const eventName = prop.slice(2);\n    let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n    }\n    desc.set = function (newValue) {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return;\n        }\n        const previousValue = target[eventNameSymbol];\n        if (typeof previousValue === 'function') {\n            target.removeEventListener(eventName, wrapFn);\n        }\n        // issue #978, when onload handler was added before loading zone.js\n        // we should remove it with originalDescSet\n        originalDescSet && originalDescSet.call(target, null);\n        target[eventNameSymbol] = newValue;\n        if (typeof newValue === 'function') {\n            target.addEventListener(eventName, wrapFn, false);\n        }\n    };\n    // The getter would return undefined for unassigned properties but the default value of an\n    // unassigned property is null\n    desc.get = function () {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return null;\n        }\n        const listener = target[eventNameSymbol];\n        if (listener) {\n            return listener;\n        }\n        else if (originalDescGet) {\n            // result will be null when use inline event attribute,\n            // such as <button onclick=\"func();\">OK</button>\n            // because the onclick function is internal raw uncompiled handler\n            // the onclick will be evaluated when first time event was triggered or\n            // the property is accessed, https://github.com/angular/zone.js/issues/525\n            // so we should use original native get to retrieve the handler\n            let value = originalDescGet.call(this);\n            if (value) {\n                desc.set.call(this, value);\n                if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n                    target.removeAttribute(prop);\n                }\n                return value;\n            }\n        }\n        return null;\n    };\n    ObjectDefineProperty(obj, prop, desc);\n    obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n    if (properties) {\n        for (let i = 0; i < properties.length; i++) {\n            patchProperty(obj, 'on' + properties[i], prototype);\n        }\n    }\n    else {\n        const onProperties = [];\n        for (const prop in obj) {\n            if (prop.slice(0, 2) == 'on') {\n                onProperties.push(prop);\n            }\n        }\n        for (let j = 0; j < onProperties.length; j++) {\n            patchProperty(obj, onProperties[j], prototype);\n        }\n    }\n}\nconst originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n    const OriginalClass = _global[className];\n    if (!OriginalClass)\n        return;\n    // keep original class in global\n    _global[zoneSymbol(className)] = OriginalClass;\n    _global[className] = function () {\n        const a = bindArguments(arguments, className);\n        switch (a.length) {\n            case 0:\n                this[originalInstanceKey] = new OriginalClass();\n                break;\n            case 1:\n                this[originalInstanceKey] = new OriginalClass(a[0]);\n                break;\n            case 2:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n                break;\n            case 3:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n                break;\n            case 4:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n                break;\n            default:\n                throw new Error('Arg list too long.');\n        }\n    };\n    // attach original delegate to patched function\n    attachOriginToPatched(_global[className], OriginalClass);\n    const instance = new OriginalClass(function () { });\n    let prop;\n    for (prop in instance) {\n        // https://bugs.webkit.org/show_bug.cgi?id=44721\n        if (className === 'XMLHttpRequest' && prop === 'responseBlob')\n            continue;\n        (function (prop) {\n            if (typeof instance[prop] === 'function') {\n                _global[className].prototype[prop] = function () {\n                    return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n                };\n            }\n            else {\n                ObjectDefineProperty(_global[className].prototype, prop, {\n                    set: function (fn) {\n                        if (typeof fn === 'function') {\n                            this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                            // keep callback in wrapped function so we can\n                            // use it in Function.prototype.toString to return\n                            // the native one.\n                            attachOriginToPatched(this[originalInstanceKey][prop], fn);\n                        }\n                        else {\n                            this[originalInstanceKey][prop] = fn;\n                        }\n                    },\n                    get: function () {\n                        return this[originalInstanceKey][prop];\n                    }\n                });\n            }\n        }(prop));\n    }\n    for (prop in OriginalClass) {\n        if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n            _global[className][prop] = OriginalClass[prop];\n        }\n    }\n}\nfunction patchMethod(target, name, patchFn) {\n    let proto = target;\n    while (proto && !proto.hasOwnProperty(name)) {\n        proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && target[name]) {\n        // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n        proto = target;\n    }\n    const delegateName = zoneSymbol(name);\n    let delegate = null;\n    if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n        delegate = proto[delegateName] = proto[name];\n        // check whether proto[name] is writable\n        // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n        const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n        if (isPropertyWritable(desc)) {\n            const patchDelegate = patchFn(delegate, delegateName, name);\n            proto[name] = function () {\n                return patchDelegate(this, arguments);\n            };\n            attachOriginToPatched(proto[name], delegate);\n        }\n    }\n    return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n    let setNative = null;\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[data.cbIdx] = function () {\n            task.invoke.apply(this, arguments);\n        };\n        setNative.apply(data.target, data.args);\n        return task;\n    }\n    setNative = patchMethod(obj, funcName, (delegate) => function (self, args) {\n        const meta = metaCreator(self, args);\n        if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n            return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(self, args);\n        }\n    });\n}\nfunction attachOriginToPatched(patched, original) {\n    patched[zoneSymbol('OriginalDelegate')] = original;\n}\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\nfunction isIE() {\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n            return true;\n        }\n    }\n    catch (error) {\n    }\n    return false;\n}\nfunction isIEOrEdge() {\n    if (isDetectedIEOrEdge) {\n        return ieOrEdge;\n    }\n    isDetectedIEOrEdge = true;\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n            ieOrEdge = true;\n        }\n    }\n    catch (error) {\n    }\n    return ieOrEdge;\n}\n\nZone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n    const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n    const ObjectDefineProperty = Object.defineProperty;\n    function readableObjectToString(obj) {\n        if (obj && obj.toString === Object.prototype.toString) {\n            const className = obj.constructor && obj.constructor.name;\n            return (className ? className : '') + ': ' + JSON.stringify(obj);\n        }\n        return obj ? obj.toString() : Object.prototype.toString.call(obj);\n    }\n    const __symbol__ = api.symbol;\n    const _uncaughtPromiseErrors = [];\n    const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] === true;\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    const creationTrace = '__creationTrace__';\n    api.onUnhandledError = (e) => {\n        if (api.showUncaughtError()) {\n            const rejection = e && e.rejection;\n            if (rejection) {\n                console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n            }\n            else {\n                console.error(e);\n            }\n        }\n    };\n    api.microtaskDrainDone = () => {\n        while (_uncaughtPromiseErrors.length) {\n            const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n            try {\n                uncaughtPromiseError.zone.runGuarded(() => {\n                    if (uncaughtPromiseError.throwOriginal) {\n                        throw uncaughtPromiseError.rejection;\n                    }\n                    throw uncaughtPromiseError;\n                });\n            }\n            catch (error) {\n                handleUnhandledRejection(error);\n            }\n        }\n    };\n    const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n    function handleUnhandledRejection(e) {\n        api.onUnhandledError(e);\n        try {\n            const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n            if (typeof handler === 'function') {\n                handler.call(this, e);\n            }\n        }\n        catch (err) {\n        }\n    }\n    function isThenable(value) {\n        return value && value.then;\n    }\n    function forwardResolution(value) {\n        return value;\n    }\n    function forwardRejection(rejection) {\n        return ZoneAwarePromise.reject(rejection);\n    }\n    const symbolState = __symbol__('state');\n    const symbolValue = __symbol__('value');\n    const symbolFinally = __symbol__('finally');\n    const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n    const symbolParentPromiseState = __symbol__('parentPromiseState');\n    const source = 'Promise.then';\n    const UNRESOLVED = null;\n    const RESOLVED = true;\n    const REJECTED = false;\n    const REJECTED_NO_CATCH = 0;\n    function makeResolver(promise, state) {\n        return (v) => {\n            try {\n                resolvePromise(promise, state, v);\n            }\n            catch (err) {\n                resolvePromise(promise, false, err);\n            }\n            // Do not return value or you will break the Promise spec.\n        };\n    }\n    const once = function () {\n        let wasCalled = false;\n        return function wrapper(wrappedFunction) {\n            return function () {\n                if (wasCalled) {\n                    return;\n                }\n                wasCalled = true;\n                wrappedFunction.apply(null, arguments);\n            };\n        };\n    };\n    const TYPE_ERROR = 'Promise resolved with itself';\n    const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n    // Promise Resolution\n    function resolvePromise(promise, state, value) {\n        const onceWrapper = once();\n        if (promise === value) {\n            throw new TypeError(TYPE_ERROR);\n        }\n        if (promise[symbolState] === UNRESOLVED) {\n            // should only get value.then once based on promise spec.\n            let then = null;\n            try {\n                if (typeof value === 'object' || typeof value === 'function') {\n                    then = value && value.then;\n                }\n            }\n            catch (err) {\n                onceWrapper(() => {\n                    resolvePromise(promise, false, err);\n                })();\n                return promise;\n            }\n            // if (value instanceof ZoneAwarePromise) {\n            if (state !== REJECTED && value instanceof ZoneAwarePromise &&\n                value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) &&\n                value[symbolState] !== UNRESOLVED) {\n                clearRejectedNoCatch(value);\n                resolvePromise(promise, value[symbolState], value[symbolValue]);\n            }\n            else if (state !== REJECTED && typeof then === 'function') {\n                try {\n                    then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n                }\n                catch (err) {\n                    onceWrapper(() => {\n                        resolvePromise(promise, false, err);\n                    })();\n                }\n            }\n            else {\n                promise[symbolState] = state;\n                const queue = promise[symbolValue];\n                promise[symbolValue] = value;\n                if (promise[symbolFinally] === symbolFinally) {\n                    // the promise is generated by Promise.prototype.finally\n                    if (state === RESOLVED) {\n                        // the state is resolved, should ignore the value\n                        // and use parent promise value\n                        promise[symbolState] = promise[symbolParentPromiseState];\n                        promise[symbolValue] = promise[symbolParentPromiseValue];\n                    }\n                }\n                // record task information in value when error occurs, so we can\n                // do some additional work such as render longStackTrace\n                if (state === REJECTED && value instanceof Error) {\n                    // check if longStackTraceZone is here\n                    const trace = Zone.currentTask && Zone.currentTask.data &&\n                        Zone.currentTask.data[creationTrace];\n                    if (trace) {\n                        // only keep the long stack trace into error when in longStackTraceZone\n                        ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, { configurable: true, enumerable: false, writable: true, value: trace });\n                    }\n                }\n                for (let i = 0; i < queue.length;) {\n                    scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n                }\n                if (queue.length == 0 && state == REJECTED) {\n                    promise[symbolState] = REJECTED_NO_CATCH;\n                    let uncaughtPromiseError = value;\n                    try {\n                        // Here we throws a new Error to print more readable error log\n                        // and if the value is not an error, zone.js builds an `Error`\n                        // Object here to attach the stack information.\n                        throw new Error('Uncaught (in promise): ' + readableObjectToString(value) +\n                            (value && value.stack ? '\\n' + value.stack : ''));\n                    }\n                    catch (err) {\n                        uncaughtPromiseError = err;\n                    }\n                    if (isDisableWrappingUncaughtPromiseRejection) {\n                        // If disable wrapping uncaught promise reject\n                        // use the value instead of wrapping it.\n                        uncaughtPromiseError.throwOriginal = true;\n                    }\n                    uncaughtPromiseError.rejection = value;\n                    uncaughtPromiseError.promise = promise;\n                    uncaughtPromiseError.zone = Zone.current;\n                    uncaughtPromiseError.task = Zone.currentTask;\n                    _uncaughtPromiseErrors.push(uncaughtPromiseError);\n                    api.scheduleMicroTask(); // to make sure that it is running\n                }\n            }\n        }\n        // Resolving an already resolved promise is a noop.\n        return promise;\n    }\n    const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n    function clearRejectedNoCatch(promise) {\n        if (promise[symbolState] === REJECTED_NO_CATCH) {\n            // if the promise is rejected no catch status\n            // and queue.length > 0, means there is a error handler\n            // here to handle the rejected promise, we should trigger\n            // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n            // eventHandler\n            try {\n                const handler = Zone[REJECTION_HANDLED_HANDLER];\n                if (handler && typeof handler === 'function') {\n                    handler.call(this, { rejection: promise[symbolValue], promise: promise });\n                }\n            }\n            catch (err) {\n            }\n            promise[symbolState] = REJECTED;\n            for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n                if (promise === _uncaughtPromiseErrors[i].promise) {\n                    _uncaughtPromiseErrors.splice(i, 1);\n                }\n            }\n        }\n    }\n    function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n        clearRejectedNoCatch(promise);\n        const promiseState = promise[symbolState];\n        const delegate = promiseState ?\n            (typeof onFulfilled === 'function') ? onFulfilled : forwardResolution :\n            (typeof onRejected === 'function') ? onRejected :\n                forwardRejection;\n        zone.scheduleMicroTask(source, () => {\n            try {\n                const parentPromiseValue = promise[symbolValue];\n                const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n                if (isFinallyPromise) {\n                    // if the promise is generated from finally call, keep parent promise's state and value\n                    chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n                    chainPromise[symbolParentPromiseState] = promiseState;\n                }\n                // should not pass value to finally callback\n                const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ?\n                    [] :\n                    [parentPromiseValue]);\n                resolvePromise(chainPromise, true, value);\n            }\n            catch (error) {\n                // if error occurs, should always return this error\n                resolvePromise(chainPromise, false, error);\n            }\n        }, chainPromise);\n    }\n    const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n    const noop = function () { };\n    const AggregateError = global.AggregateError;\n    class ZoneAwarePromise {\n        static toString() {\n            return ZONE_AWARE_PROMISE_TO_STRING;\n        }\n        static resolve(value) {\n            return resolvePromise(new this(null), RESOLVED, value);\n        }\n        static reject(error) {\n            return resolvePromise(new this(null), REJECTED, error);\n        }\n        static any(values) {\n            if (!values || typeof values[Symbol.iterator] !== 'function') {\n                return Promise.reject(new AggregateError([], 'All promises were rejected'));\n            }\n            const promises = [];\n            let count = 0;\n            try {\n                for (let v of values) {\n                    count++;\n                    promises.push(ZoneAwarePromise.resolve(v));\n                }\n            }\n            catch (err) {\n                return Promise.reject(new AggregateError([], 'All promises were rejected'));\n            }\n            if (count === 0) {\n                return Promise.reject(new AggregateError([], 'All promises were rejected'));\n            }\n            let finished = false;\n            const errors = [];\n            return new ZoneAwarePromise((resolve, reject) => {\n                for (let i = 0; i < promises.length; i++) {\n                    promises[i].then(v => {\n                        if (finished) {\n                            return;\n                        }\n                        finished = true;\n                        resolve(v);\n                    }, err => {\n                        errors.push(err);\n                        count--;\n                        if (count === 0) {\n                            finished = true;\n                            reject(new AggregateError(errors, 'All promises were rejected'));\n                        }\n                    });\n                }\n            });\n        }\n        ;\n        static race(values) {\n            let resolve;\n            let reject;\n            let promise = new this((res, rej) => {\n                resolve = res;\n                reject = rej;\n            });\n            function onResolve(value) {\n                resolve(value);\n            }\n            function onReject(error) {\n                reject(error);\n            }\n            for (let value of values) {\n                if (!isThenable(value)) {\n                    value = this.resolve(value);\n                }\n                value.then(onResolve, onReject);\n            }\n            return promise;\n        }\n        static all(values) {\n            return ZoneAwarePromise.allWithCallback(values);\n        }\n        static allSettled(values) {\n            const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n            return P.allWithCallback(values, {\n                thenCallback: (value) => ({ status: 'fulfilled', value }),\n                errorCallback: (err) => ({ status: 'rejected', reason: err })\n            });\n        }\n        static allWithCallback(values, callback) {\n            let resolve;\n            let reject;\n            let promise = new this((res, rej) => {\n                resolve = res;\n                reject = rej;\n            });\n            // Start at 2 to prevent prematurely resolving if .then is called immediately.\n            let unresolvedCount = 2;\n            let valueIndex = 0;\n            const resolvedValues = [];\n            for (let value of values) {\n                if (!isThenable(value)) {\n                    value = this.resolve(value);\n                }\n                const curValueIndex = valueIndex;\n                try {\n                    value.then((value) => {\n                        resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n                        unresolvedCount--;\n                        if (unresolvedCount === 0) {\n                            resolve(resolvedValues);\n                        }\n                    }, (err) => {\n                        if (!callback) {\n                            reject(err);\n                        }\n                        else {\n                            resolvedValues[curValueIndex] = callback.errorCallback(err);\n                            unresolvedCount--;\n                            if (unresolvedCount === 0) {\n                                resolve(resolvedValues);\n                            }\n                        }\n                    });\n                }\n                catch (thenErr) {\n                    reject(thenErr);\n                }\n                unresolvedCount++;\n                valueIndex++;\n            }\n            // Make the unresolvedCount zero-based again.\n            unresolvedCount -= 2;\n            if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n            }\n            return promise;\n        }\n        constructor(executor) {\n            const promise = this;\n            if (!(promise instanceof ZoneAwarePromise)) {\n                throw new Error('Must be an instanceof Promise.');\n            }\n            promise[symbolState] = UNRESOLVED;\n            promise[symbolValue] = []; // queue;\n            try {\n                const onceWrapper = once();\n                executor &&\n                    executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n            }\n            catch (error) {\n                resolvePromise(promise, false, error);\n            }\n        }\n        get [Symbol.toStringTag]() {\n            return 'Promise';\n        }\n        get [Symbol.species]() {\n            return ZoneAwarePromise;\n        }\n        then(onFulfilled, onRejected) {\n            // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n            // may be an object without a prototype (created through `Object.create(null)`); thus\n            // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n            // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n            // object and copies promise properties into that object (within the `getOrCreateLoad`\n            // function). The zone.js then checks if the resolved value has the `then` method and invokes\n            // it with the `value` context. Otherwise, this will throw an error: `TypeError: Cannot read\n            // properties of undefined (reading 'Symbol(Symbol.species)')`.\n            let C = this.constructor?.[Symbol.species];\n            if (!C || typeof C !== 'function') {\n                C = this.constructor || ZoneAwarePromise;\n            }\n            const chainPromise = new C(noop);\n            const zone = Zone.current;\n            if (this[symbolState] == UNRESOLVED) {\n                this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n            }\n            else {\n                scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n            }\n            return chainPromise;\n        }\n        catch(onRejected) {\n            return this.then(null, onRejected);\n        }\n        finally(onFinally) {\n            // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n            let C = this.constructor?.[Symbol.species];\n            if (!C || typeof C !== 'function') {\n                C = ZoneAwarePromise;\n            }\n            const chainPromise = new C(noop);\n            chainPromise[symbolFinally] = symbolFinally;\n            const zone = Zone.current;\n            if (this[symbolState] == UNRESOLVED) {\n                this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n            }\n            else {\n                scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n            }\n            return chainPromise;\n        }\n    }\n    // Protect against aggressive optimizers dropping seemingly unused properties.\n    // E.g. Closure Compiler in advanced mode.\n    ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n    ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n    ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n    ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n    const NativePromise = global[symbolPromise] = global['Promise'];\n    global['Promise'] = ZoneAwarePromise;\n    const symbolThenPatched = __symbol__('thenPatched');\n    function patchThen(Ctor) {\n        const proto = Ctor.prototype;\n        const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n        if (prop && (prop.writable === false || !prop.configurable)) {\n            // check Ctor.prototype.then propertyDescriptor is writable or not\n            // in meteor env, writable is false, we should ignore such case\n            return;\n        }\n        const originalThen = proto.then;\n        // Keep a reference to the original method.\n        proto[symbolThen] = originalThen;\n        Ctor.prototype.then = function (onResolve, onReject) {\n            const wrapped = new ZoneAwarePromise((resolve, reject) => {\n                originalThen.call(this, resolve, reject);\n            });\n            return wrapped.then(onResolve, onReject);\n        };\n        Ctor[symbolThenPatched] = true;\n    }\n    api.patchThen = patchThen;\n    function zoneify(fn) {\n        return function (self, args) {\n            let resultPromise = fn.apply(self, args);\n            if (resultPromise instanceof ZoneAwarePromise) {\n                return resultPromise;\n            }\n            let ctor = resultPromise.constructor;\n            if (!ctor[symbolThenPatched]) {\n                patchThen(ctor);\n            }\n            return resultPromise;\n        };\n    }\n    if (NativePromise) {\n        patchThen(NativePromise);\n        patchMethod(global, 'fetch', delegate => zoneify(delegate));\n    }\n    // This is not part of public API, but it is useful for tests, so we expose it.\n    Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n    return ZoneAwarePromise;\n});\n\n// override Function.prototype.toString to make zone.js patched function\n// look like native function\nZone.__load_patch('toString', (global) => {\n    // patch Func.prototype.toString to let them look like native\n    const originalFunctionToString = Function.prototype.toString;\n    const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n    const PROMISE_SYMBOL = zoneSymbol('Promise');\n    const ERROR_SYMBOL = zoneSymbol('Error');\n    const newFunctionToString = function toString() {\n        if (typeof this === 'function') {\n            const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n            if (originalDelegate) {\n                if (typeof originalDelegate === 'function') {\n                    return originalFunctionToString.call(originalDelegate);\n                }\n                else {\n                    return Object.prototype.toString.call(originalDelegate);\n                }\n            }\n            if (this === Promise) {\n                const nativePromise = global[PROMISE_SYMBOL];\n                if (nativePromise) {\n                    return originalFunctionToString.call(nativePromise);\n                }\n            }\n            if (this === Error) {\n                const nativeError = global[ERROR_SYMBOL];\n                if (nativeError) {\n                    return originalFunctionToString.call(nativeError);\n                }\n            }\n        }\n        return originalFunctionToString.call(this);\n    };\n    newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n    Function.prototype.toString = newFunctionToString;\n    // patch Object.prototype.toString to let them look like native\n    const originalObjectToString = Object.prototype.toString;\n    const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n    Object.prototype.toString = function () {\n        if (typeof Promise === 'function' && this instanceof Promise) {\n            return PROMISE_OBJECT_TO_STRING;\n        }\n        return originalObjectToString.call(this);\n    };\n});\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nlet passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        const options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n            }\n        });\n        // Note: We pass the `options` object as the event handler too. This is not compatible with the\n        // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n        // without an actual handler.\n        window.addEventListener('test', options, options);\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n    useG: true\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n    const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n    const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n    const ADD_EVENT_LISTENER = (patchOptions && patchOptions.add) || ADD_EVENT_LISTENER_STR;\n    const REMOVE_EVENT_LISTENER = (patchOptions && patchOptions.rm) || REMOVE_EVENT_LISTENER_STR;\n    const LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.listeners) || 'eventListeners';\n    const REMOVE_ALL_LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.rmAll) || 'removeAllListeners';\n    const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n    const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n    const PREPEND_EVENT_LISTENER = 'prependListener';\n    const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n    const invokeTask = function (task, target, event) {\n        // for better performance, check isRemoved which is set\n        // by removeEventListener\n        if (task.isRemoved) {\n            return;\n        }\n        const delegate = task.callback;\n        if (typeof delegate === 'object' && delegate.handleEvent) {\n            // create the bind version of handleEvent when invoke\n            task.callback = (event) => delegate.handleEvent(event);\n            task.originalDelegate = delegate;\n        }\n        // invoke static task.invoke\n        // need to try/catch error here, otherwise, the error in one event listener\n        // will break the executions of the other event listeners. Also error will\n        // not remove the event listener when `once` options is true.\n        let error;\n        try {\n            task.invoke(task, target, [event]);\n        }\n        catch (err) {\n            error = err;\n        }\n        const options = task.options;\n        if (options && typeof options === 'object' && options.once) {\n            // if options.once is true, after invoke once remove listener here\n            // only browser need to do this, nodejs eventEmitter will cal removeListener\n            // inside EventEmitter.once\n            const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n            target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n        }\n        return error;\n    };\n    function globalCallback(context, event, isCapture) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        // event.target is needed for Samsung TV and SourceBuffer\n        // || global is needed https://github.com/angular/zone.js/issues/190\n        const target = context || event.target || _global;\n        const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n        if (tasks) {\n            const errors = [];\n            // invoke all tasks which attached to current target with given event.type and capture = false\n            // for performance concern, if task.length === 1, just invoke\n            if (tasks.length === 1) {\n                const err = invokeTask(tasks[0], target, event);\n                err && errors.push(err);\n            }\n            else {\n                // https://github.com/angular/zone.js/issues/836\n                // copy the tasks array before invoke, to avoid\n                // the callback will remove itself or other listener\n                const copyTasks = tasks.slice();\n                for (let i = 0; i < copyTasks.length; i++) {\n                    if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                        break;\n                    }\n                    const err = invokeTask(copyTasks[i], target, event);\n                    err && errors.push(err);\n                }\n            }\n            // Since there is only one error, we don't need to schedule microTask\n            // to throw the error.\n            if (errors.length === 1) {\n                throw errors[0];\n            }\n            else {\n                for (let i = 0; i < errors.length; i++) {\n                    const err = errors[i];\n                    api.nativeScheduleMicroTask(() => {\n                        throw err;\n                    });\n                }\n            }\n        }\n    }\n    // global shared zoneAwareCallback to handle all event callback with capture = false\n    const globalZoneAwareCallback = function (event) {\n        return globalCallback(this, event, false);\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = true\n    const globalZoneAwareCaptureCallback = function (event) {\n        return globalCallback(this, event, true);\n    };\n    function patchEventTargetMethods(obj, patchOptions) {\n        if (!obj) {\n            return false;\n        }\n        let useGlobalCallback = true;\n        if (patchOptions && patchOptions.useG !== undefined) {\n            useGlobalCallback = patchOptions.useG;\n        }\n        const validateHandler = patchOptions && patchOptions.vh;\n        let checkDuplicate = true;\n        if (patchOptions && patchOptions.chkDup !== undefined) {\n            checkDuplicate = patchOptions.chkDup;\n        }\n        let returnTarget = false;\n        if (patchOptions && patchOptions.rt !== undefined) {\n            returnTarget = patchOptions.rt;\n        }\n        let proto = obj;\n        while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n            proto = ObjectGetPrototypeOf(proto);\n        }\n        if (!proto && obj[ADD_EVENT_LISTENER]) {\n            // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n            proto = obj;\n        }\n        if (!proto) {\n            return false;\n        }\n        if (proto[zoneSymbolAddEventListener]) {\n            return false;\n        }\n        const eventNameToString = patchOptions && patchOptions.eventNameToString;\n        // a shared global taskData to pass data for scheduleEventTask\n        // so we do not need to create a new object just for pass some data\n        const taskData = {};\n        const nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n        const nativeRemoveEventListener = proto[zoneSymbol(REMOVE_EVENT_LISTENER)] =\n            proto[REMOVE_EVENT_LISTENER];\n        const nativeListeners = proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] =\n            proto[LISTENERS_EVENT_LISTENER];\n        const nativeRemoveAllListeners = proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] =\n            proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n        let nativePrependEventListener;\n        if (patchOptions && patchOptions.prepend) {\n            nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] =\n                proto[patchOptions.prepend];\n        }\n        /**\n         * This util function will build an option object with passive option\n         * to handle all possible input from the user.\n         */\n        function buildEventListenerOptions(options, passive) {\n            if (!passiveSupported && typeof options === 'object' && options) {\n                // doesn't support passive but user want to pass an object as options.\n                // this will not work on some old browser, so we just pass a boolean\n                // as useCapture parameter\n                return !!options.capture;\n            }\n            if (!passiveSupported || !passive) {\n                return options;\n            }\n            if (typeof options === 'boolean') {\n                return { capture: options, passive: true };\n            }\n            if (!options) {\n                return { passive: true };\n            }\n            if (typeof options === 'object' && options.passive !== false) {\n                return { ...options, passive: true };\n            }\n            return options;\n        }\n        const customScheduleGlobal = function (task) {\n            // if there is already a task for the eventName + capture,\n            // just return, because we use the shared globalZoneAwareCallback here.\n            if (taskData.isExisting) {\n                return;\n            }\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n        };\n        const customCancelGlobal = function (task) {\n            // if task is not marked as isRemoved, this call is directly\n            // from Zone.prototype.cancelTask, we should remove the task\n            // from tasksList of target first\n            if (!task.isRemoved) {\n                const symbolEventNames = zoneSymbolEventNames[task.eventName];\n                let symbolEventName;\n                if (symbolEventNames) {\n                    symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n                }\n                const existingTasks = symbolEventName && task.target[symbolEventName];\n                if (existingTasks) {\n                    for (let i = 0; i < existingTasks.length; i++) {\n                        const existingTask = existingTasks[i];\n                        if (existingTask === task) {\n                            existingTasks.splice(i, 1);\n                            // set isRemoved to data for faster invokeTask check\n                            task.isRemoved = true;\n                            if (existingTasks.length === 0) {\n                                // all tasks for the eventName + capture have gone,\n                                // remove globalZoneAwareCallback and remove the task cache from target\n                                task.allRemoved = true;\n                                task.target[symbolEventName] = null;\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            // if all tasks for the eventName + capture have gone,\n            // we will really remove the global event callback,\n            // if not, return\n            if (!task.allRemoved) {\n                return;\n            }\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n        };\n        const customScheduleNonGlobal = function (task) {\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customSchedulePrepend = function (task) {\n            return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customCancelNonGlobal = function (task) {\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n        };\n        const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n        const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n        const compareTaskCallbackVsDelegate = function (task, delegate) {\n            const typeOfDelegate = typeof delegate;\n            return (typeOfDelegate === 'function' && task.callback === delegate) ||\n                (typeOfDelegate === 'object' && task.originalDelegate === delegate);\n        };\n        const compare = (patchOptions && patchOptions.diff) ? patchOptions.diff : compareTaskCallbackVsDelegate;\n        const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n        const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n        const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n            return function () {\n                const target = this || _global;\n                let eventName = arguments[0];\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                let delegate = arguments[1];\n                if (!delegate) {\n                    return nativeListener.apply(this, arguments);\n                }\n                if (isNode && eventName === 'uncaughtException') {\n                    // don't patch uncaughtException of nodejs to prevent endless loop\n                    return nativeListener.apply(this, arguments);\n                }\n                // don't create the bind delegate function for handleEvent\n                // case here to improve addEventListener performance\n                // we will create the bind delegate when invoke\n                let isHandleEvent = false;\n                if (typeof delegate !== 'function') {\n                    if (!delegate.handleEvent) {\n                        return nativeListener.apply(this, arguments);\n                    }\n                    isHandleEvent = true;\n                }\n                if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n                    return;\n                }\n                const passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n                const options = buildEventListenerOptions(arguments[2], passive);\n                if (unpatchedEvents) {\n                    // check unpatched list\n                    for (let i = 0; i < unpatchedEvents.length; i++) {\n                        if (eventName === unpatchedEvents[i]) {\n                            if (passive) {\n                                return nativeListener.call(target, eventName, delegate, options);\n                            }\n                            else {\n                                return nativeListener.apply(this, arguments);\n                            }\n                        }\n                    }\n                }\n                const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n                const once = options && typeof options === 'object' ? options.once : false;\n                const zone = Zone.current;\n                let symbolEventNames = zoneSymbolEventNames[eventName];\n                if (!symbolEventNames) {\n                    prepareEventNames(eventName, eventNameToString);\n                    symbolEventNames = zoneSymbolEventNames[eventName];\n                }\n                const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                let existingTasks = target[symbolEventName];\n                let isExisting = false;\n                if (existingTasks) {\n                    // already have task registered\n                    isExisting = true;\n                    if (checkDuplicate) {\n                        for (let i = 0; i < existingTasks.length; i++) {\n                            if (compare(existingTasks[i], delegate)) {\n                                // same callback, same capture, same event name, just return\n                                return;\n                            }\n                        }\n                    }\n                }\n                else {\n                    existingTasks = target[symbolEventName] = [];\n                }\n                let source;\n                const constructorName = target.constructor['name'];\n                const targetSource = globalSources[constructorName];\n                if (targetSource) {\n                    source = targetSource[eventName];\n                }\n                if (!source) {\n                    source = constructorName + addSource +\n                        (eventNameToString ? eventNameToString(eventName) : eventName);\n                }\n                // do not create a new object as task.data to pass those things\n                // just use the global shared one\n                taskData.options = options;\n                if (once) {\n                    // if addEventListener with once options, we don't pass it to\n                    // native addEventListener, instead we keep the once setting\n                    // and handle ourselves.\n                    taskData.options.once = false;\n                }\n                taskData.target = target;\n                taskData.capture = capture;\n                taskData.eventName = eventName;\n                taskData.isExisting = isExisting;\n                const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n                // keep taskData into data to allow onScheduleEventTask to access the task information\n                if (data) {\n                    data.taskData = taskData;\n                }\n                const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n                // should clear taskData.target to avoid memory leak\n                // issue, https://github.com/angular/angular/issues/20442\n                taskData.target = null;\n                // need to clear up taskData because it is a global object\n                if (data) {\n                    data.taskData = null;\n                }\n                // have to save those information to task in case\n                // application may call task.zone.cancelTask() directly\n                if (once) {\n                    options.once = true;\n                }\n                if (!(!passiveSupported && typeof task.options === 'boolean')) {\n                    // if not support passive, and we pass an option object\n                    // to addEventListener, we should save the options to task\n                    task.options = options;\n                }\n                task.target = target;\n                task.capture = capture;\n                task.eventName = eventName;\n                if (isHandleEvent) {\n                    // save original delegate for compare to check duplicate\n                    task.originalDelegate = delegate;\n                }\n                if (!prepend) {\n                    existingTasks.push(task);\n                }\n                else {\n                    existingTasks.unshift(task);\n                }\n                if (returnTarget) {\n                    return target;\n                }\n            };\n        };\n        proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n        if (nativePrependEventListener) {\n            proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n        }\n        proto[REMOVE_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const options = arguments[2];\n            const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n            const delegate = arguments[1];\n            if (!delegate) {\n                return nativeRemoveEventListener.apply(this, arguments);\n            }\n            if (validateHandler &&\n                !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n                return;\n            }\n            const symbolEventNames = zoneSymbolEventNames[eventName];\n            let symbolEventName;\n            if (symbolEventNames) {\n                symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n            }\n            const existingTasks = symbolEventName && target[symbolEventName];\n            if (existingTasks) {\n                for (let i = 0; i < existingTasks.length; i++) {\n                    const existingTask = existingTasks[i];\n                    if (compare(existingTask, delegate)) {\n                        existingTasks.splice(i, 1);\n                        // set isRemoved to data for faster invokeTask check\n                        existingTask.isRemoved = true;\n                        if (existingTasks.length === 0) {\n                            // all tasks for the eventName + capture have gone,\n                            // remove globalZoneAwareCallback and remove the task cache from target\n                            existingTask.allRemoved = true;\n                            target[symbolEventName] = null;\n                            // in the target, we have an event listener which is added by on_property\n                            // such as target.onclick = function() {}, so we need to clear this internal\n                            // property too if all delegates all removed\n                            if (typeof eventName === 'string') {\n                                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                                target[onPropertySymbol] = null;\n                            }\n                        }\n                        existingTask.zone.cancelTask(existingTask);\n                        if (returnTarget) {\n                            return target;\n                        }\n                        return;\n                    }\n                }\n            }\n            // issue 930, didn't find the event name or callback\n            // from zone kept existingTasks, the callback maybe\n            // added outside of zone, we need to call native removeEventListener\n            // to try to remove it.\n            return nativeRemoveEventListener.apply(this, arguments);\n        };\n        proto[LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const listeners = [];\n            const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n            for (let i = 0; i < tasks.length; i++) {\n                const task = tasks[i];\n                let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                listeners.push(delegate);\n            }\n            return listeners;\n        };\n        proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (!eventName) {\n                const keys = Object.keys(target);\n                for (let i = 0; i < keys.length; i++) {\n                    const prop = keys[i];\n                    const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                    let evtName = match && match[1];\n                    // in nodejs EventEmitter, removeListener event is\n                    // used for monitoring the removeListener call,\n                    // so just keep removeListener eventListener until\n                    // all other eventListeners are removed\n                    if (evtName && evtName !== 'removeListener') {\n                        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n                    }\n                }\n                // remove removeListener listener finally\n                this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n            }\n            else {\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                const symbolEventNames = zoneSymbolEventNames[eventName];\n                if (symbolEventNames) {\n                    const symbolEventName = symbolEventNames[FALSE_STR];\n                    const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n                    const tasks = target[symbolEventName];\n                    const captureTasks = target[symbolCaptureEventName];\n                    if (tasks) {\n                        const removeTasks = tasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                    if (captureTasks) {\n                        const removeTasks = captureTasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                }\n            }\n            if (returnTarget) {\n                return this;\n            }\n        };\n        // for native toString patch\n        attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n        attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n        if (nativeRemoveAllListeners) {\n            attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n        }\n        if (nativeListeners) {\n            attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n        }\n        return true;\n    }\n    let results = [];\n    for (let i = 0; i < apis.length; i++) {\n        results[i] = patchEventTargetMethods(apis[i], patchOptions);\n    }\n    return results;\n}\nfunction findEventTasks(target, eventName) {\n    if (!eventName) {\n        const foundTasks = [];\n        for (let prop in target) {\n            const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n            let evtName = match && match[1];\n            if (evtName && (!eventName || evtName === eventName)) {\n                const tasks = target[prop];\n                if (tasks) {\n                    for (let i = 0; i < tasks.length; i++) {\n                        foundTasks.push(tasks[i]);\n                    }\n                }\n            }\n        }\n        return foundTasks;\n    }\n    let symbolEventName = zoneSymbolEventNames[eventName];\n    if (!symbolEventName) {\n        prepareEventNames(eventName);\n        symbolEventName = zoneSymbolEventNames[eventName];\n    }\n    const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n    const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n    if (!captureFalseTasks) {\n        return captureTrueTasks ? captureTrueTasks.slice() : [];\n    }\n    else {\n        return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) :\n            captureFalseTasks.slice();\n    }\n}\nfunction patchEventPrototype(global, api) {\n    const Event = global['Event'];\n    if (Event && Event.prototype) {\n        api.patchMethod(Event.prototype, 'stopImmediatePropagation', (delegate) => function (self, args) {\n            self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n            // we need to call the native stopImmediatePropagation\n            // in case in some hybrid application, some part of\n            // application will be controlled by zone, some are not\n            delegate && delegate.apply(self, args);\n        });\n    }\n}\n\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n    const symbol = Zone.__symbol__(method);\n    if (target[symbol]) {\n        return;\n    }\n    const nativeDelegate = target[symbol] = target[method];\n    target[method] = function (name, opts, options) {\n        if (opts && opts.prototype) {\n            callbacks.forEach(function (callback) {\n                const source = `${targetName}.${method}::` + callback;\n                const prototype = opts.prototype;\n                // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n                // `customElements.define`. We explicitly wrap the patching code into try-catch since\n                // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n                // make those properties non-writable. This means that patching callback will throw an error\n                // `cannot assign to read-only property`. See this code as an example:\n                // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n                // We don't want to stop the application rendering if we couldn't patch some\n                // callback, e.g. `attributeChangedCallback`.\n                try {\n                    if (prototype.hasOwnProperty(callback)) {\n                        const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n                        if (descriptor && descriptor.value) {\n                            descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                            api._redefineProperty(opts.prototype, callback, descriptor);\n                        }\n                        else if (prototype[callback]) {\n                            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                        }\n                    }\n                    else if (prototype[callback]) {\n                        prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                    }\n                }\n                catch {\n                    // Note: we leave the catch block empty since there's no way to handle the error related\n                    // to non-writable property.\n                }\n            });\n        }\n        return nativeDelegate.call(target, name, opts, options);\n    };\n    api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction filterProperties(target, onProperties, ignoreProperties) {\n    if (!ignoreProperties || ignoreProperties.length === 0) {\n        return onProperties;\n    }\n    const tip = ignoreProperties.filter(ip => ip.target === target);\n    if (!tip || tip.length === 0) {\n        return onProperties;\n    }\n    const targetIgnoreProperties = tip[0].ignoreProperties;\n    return onProperties.filter(op => targetIgnoreProperties.indexOf(op) === -1);\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n    // check whether target is available, sometimes target will be undefined\n    // because different browser or some 3rd party plugin.\n    if (!target) {\n        return;\n    }\n    const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n    patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\nfunction getOnEventNames(target) {\n    return Object.getOwnPropertyNames(target)\n        .filter(name => name.startsWith('on') && name.length > 2)\n        .map(name => name.substring(2));\n}\nfunction propertyDescriptorPatch(api, _global) {\n    if (isNode && !isMix) {\n        return;\n    }\n    if (Zone[api.symbol('patchEvents')]) {\n        // events are already been patched by legacy patch.\n        return;\n    }\n    const ignoreProperties = _global['__Zone_ignore_on_properties'];\n    // for browsers that we can patch the descriptor:  Chrome & Firefox\n    let patchTargets = [];\n    if (isBrowser) {\n        const internalWindow = window;\n        patchTargets = patchTargets.concat([\n            'Document', 'SVGElement', 'Element', 'HTMLElement', 'HTMLBodyElement', 'HTMLMediaElement',\n            'HTMLFrameSetElement', 'HTMLFrameElement', 'HTMLIFrameElement', 'HTMLMarqueeElement', 'Worker'\n        ]);\n        const ignoreErrorProperties = isIE() ? [{ target: internalWindow, ignoreProperties: ['error'] }] : [];\n        // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n        // so we need to pass WindowPrototype to check onProp exist or not\n        patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n    }\n    patchTargets = patchTargets.concat([\n        'XMLHttpRequest', 'XMLHttpRequestEventTarget', 'IDBIndex', 'IDBRequest', 'IDBOpenDBRequest',\n        'IDBDatabase', 'IDBTransaction', 'IDBCursor', 'WebSocket'\n    ]);\n    for (let i = 0; i < patchTargets.length; i++) {\n        const target = _global[patchTargets[i]];\n        target && target.prototype &&\n            patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n    }\n}\n\nZone.__load_patch('util', (global, Zone, api) => {\n    // Collect native event names by looking at properties\n    // on the global namespace, e.g. 'onclick'.\n    const eventNames = getOnEventNames(global);\n    api.patchOnProperties = patchOnProperties;\n    api.patchMethod = patchMethod;\n    api.bindArguments = bindArguments;\n    api.patchMacroTask = patchMacroTask;\n    // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS` to\n    // define which events will not be patched by `Zone.js`.\n    // In newer version (>=0.9.0), we change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep\n    // the name consistent with angular repo.\n    // The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be supported for\n    // backwards compatibility.\n    const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n    const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n    if (global[SYMBOL_UNPATCHED_EVENTS]) {\n        global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n    }\n    if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n        Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] =\n            global[SYMBOL_BLACK_LISTED_EVENTS];\n    }\n    api.patchEventPrototype = patchEventPrototype;\n    api.patchEventTarget = patchEventTarget;\n    api.isIEOrEdge = isIEOrEdge;\n    api.ObjectDefineProperty = ObjectDefineProperty;\n    api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n    api.ObjectCreate = ObjectCreate;\n    api.ArraySlice = ArraySlice;\n    api.patchClass = patchClass;\n    api.wrapWithCurrentZone = wrapWithCurrentZone;\n    api.filterProperties = filterProperties;\n    api.attachOriginToPatched = attachOriginToPatched;\n    api._redefineProperty = Object.defineProperty;\n    api.patchCallbacks = patchCallbacks;\n    api.getGlobalObjects = () => ({\n        globalSources,\n        zoneSymbolEventNames,\n        eventNames,\n        isBrowser,\n        isMix,\n        isNode,\n        TRUE_STR,\n        FALSE_STR,\n        ZONE_SYMBOL_PREFIX,\n        ADD_EVENT_LISTENER_STR,\n        REMOVE_EVENT_LISTENER_STR\n    });\n});\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchQueueMicrotask(global, api) {\n    api.patchMethod(global, 'queueMicrotask', (delegate) => {\n        return function (self, args) {\n            Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n        };\n    });\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n    let setNative = null;\n    let clearNative = null;\n    setName += nameSuffix;\n    cancelName += nameSuffix;\n    const tasksByHandleId = {};\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[0] = function () {\n            return task.invoke.apply(this, arguments);\n        };\n        data.handleId = setNative.apply(window, data.args);\n        return task;\n    }\n    function clearTask(task) {\n        return clearNative.call(window, task.data.handleId);\n    }\n    setNative =\n        patchMethod(window, setName, (delegate) => function (self, args) {\n            if (typeof args[0] === 'function') {\n                const options = {\n                    isPeriodic: nameSuffix === 'Interval',\n                    delay: (nameSuffix === 'Timeout' || nameSuffix === 'Interval') ? args[1] || 0 :\n                        undefined,\n                    args: args\n                };\n                const callback = args[0];\n                args[0] = function timer() {\n                    try {\n                        return callback.apply(this, arguments);\n                    }\n                    finally {\n                        // issue-934, task will be cancelled\n                        // even it is a periodic task such as\n                        // setInterval\n                        // https://github.com/angular/angular/issues/40387\n                        // Cleanup tasksByHandleId should be handled before scheduleTask\n                        // Since some zoneSpec may intercept and doesn't trigger\n                        // scheduleFn(scheduleTask) provided here.\n                        if (!(options.isPeriodic)) {\n                            if (typeof options.handleId === 'number') {\n                                // in non-nodejs env, we remove timerId\n                                // from local cache\n                                delete tasksByHandleId[options.handleId];\n                            }\n                            else if (options.handleId) {\n                                // Node returns complex objects as handleIds\n                                // we remove task reference from timer object\n                                options.handleId[taskSymbol] = null;\n                            }\n                        }\n                    }\n                };\n                const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n                if (!task) {\n                    return task;\n                }\n                // Node.js must additionally support the ref and unref functions.\n                const handle = task.data.handleId;\n                if (typeof handle === 'number') {\n                    // for non nodejs env, we save handleId: task\n                    // mapping in local cache for clearTimeout\n                    tasksByHandleId[handle] = task;\n                }\n                else if (handle) {\n                    // for nodejs env, we save task\n                    // reference in timerId Object for clearTimeout\n                    handle[taskSymbol] = task;\n                }\n                // check whether handle is null, because some polyfill or browser\n                // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n                if (handle && handle.ref && handle.unref && typeof handle.ref === 'function' &&\n                    typeof handle.unref === 'function') {\n                    task.ref = handle.ref.bind(handle);\n                    task.unref = handle.unref.bind(handle);\n                }\n                if (typeof handle === 'number' || handle) {\n                    return handle;\n                }\n                return task;\n            }\n            else {\n                // cause an error by calling it directly.\n                return delegate.apply(window, args);\n            }\n        });\n    clearNative =\n        patchMethod(window, cancelName, (delegate) => function (self, args) {\n            const id = args[0];\n            let task;\n            if (typeof id === 'number') {\n                // non nodejs env.\n                task = tasksByHandleId[id];\n            }\n            else {\n                // nodejs env.\n                task = id && id[taskSymbol];\n                // other environments.\n                if (!task) {\n                    task = id;\n                }\n            }\n            if (task && typeof task.type === 'string') {\n                if (task.state !== 'notScheduled' &&\n                    (task.cancelFn && task.data.isPeriodic || task.runCount === 0)) {\n                    if (typeof id === 'number') {\n                        delete tasksByHandleId[id];\n                    }\n                    else if (id) {\n                        id[taskSymbol] = null;\n                    }\n                    // Do not cancel already canceled functions\n                    task.zone.cancelTask(task);\n                }\n            }\n            else {\n                // cause an error by calling it directly.\n                delegate.apply(window, args);\n            }\n        });\n}\n\nfunction patchCustomElements(_global, api) {\n    const { isBrowser, isMix } = api.getGlobalObjects();\n    if ((!isBrowser && !isMix) || !_global['customElements'] || !('customElements' in _global)) {\n        return;\n    }\n    const callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback'];\n    api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n\nfunction eventTargetPatch(_global, api) {\n    if (Zone[api.symbol('patchEventTarget')]) {\n        // EventTarget is already patched.\n        return;\n    }\n    const { eventNames, zoneSymbolEventNames, TRUE_STR, FALSE_STR, ZONE_SYMBOL_PREFIX } = api.getGlobalObjects();\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (let i = 0; i < eventNames.length; i++) {\n        const eventName = eventNames[i];\n        const falseEventName = eventName + FALSE_STR;\n        const trueEventName = eventName + TRUE_STR;\n        const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    const EVENT_TARGET = _global['EventTarget'];\n    if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n        return;\n    }\n    api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n    return true;\n}\nfunction patchEvent(global, api) {\n    api.patchEventPrototype(global, api);\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nZone.__load_patch('legacy', (global) => {\n    const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n    if (legacyPatch) {\n        legacyPatch();\n    }\n});\nZone.__load_patch('timers', (global) => {\n    const set = 'set';\n    const clear = 'clear';\n    patchTimer(global, set, clear, 'Timeout');\n    patchTimer(global, set, clear, 'Interval');\n    patchTimer(global, set, clear, 'Immediate');\n});\nZone.__load_patch('requestAnimationFrame', (global) => {\n    patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n    patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n    patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n});\nZone.__load_patch('blocking', (global, Zone) => {\n    const blockingMethods = ['alert', 'prompt', 'confirm'];\n    for (let i = 0; i < blockingMethods.length; i++) {\n        const name = blockingMethods[i];\n        patchMethod(global, name, (delegate, symbol, name) => {\n            return function (s, args) {\n                return Zone.current.run(delegate, global, args, name);\n            };\n        });\n    }\n});\nZone.__load_patch('EventTarget', (global, Zone, api) => {\n    patchEvent(global, api);\n    eventTargetPatch(global, api);\n    // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n    const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n    if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n        api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n    }\n});\nZone.__load_patch('MutationObserver', (global, Zone, api) => {\n    patchClass('MutationObserver');\n    patchClass('WebKitMutationObserver');\n});\nZone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n    patchClass('IntersectionObserver');\n});\nZone.__load_patch('FileReader', (global, Zone, api) => {\n    patchClass('FileReader');\n});\nZone.__load_patch('on_property', (global, Zone, api) => {\n    propertyDescriptorPatch(api, global);\n});\nZone.__load_patch('customElements', (global, Zone, api) => {\n    patchCustomElements(global, api);\n});\nZone.__load_patch('XHR', (global, Zone) => {\n    // Treat XMLHttpRequest as a macrotask.\n    patchXHR(global);\n    const XHR_TASK = zoneSymbol('xhrTask');\n    const XHR_SYNC = zoneSymbol('xhrSync');\n    const XHR_LISTENER = zoneSymbol('xhrListener');\n    const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n    const XHR_URL = zoneSymbol('xhrURL');\n    const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n    function patchXHR(window) {\n        const XMLHttpRequest = window['XMLHttpRequest'];\n        if (!XMLHttpRequest) {\n            // XMLHttpRequest is not available in service worker\n            return;\n        }\n        const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n        function findPendingTask(target) {\n            return target[XHR_TASK];\n        }\n        let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n        let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n        if (!oriAddListener) {\n            const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n            if (XMLHttpRequestEventTarget) {\n                const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n                oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            }\n        }\n        const READY_STATE_CHANGE = 'readystatechange';\n        const SCHEDULED = 'scheduled';\n        function scheduleTask(task) {\n            const data = task.data;\n            const target = data.target;\n            target[XHR_SCHEDULED] = false;\n            target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n            // remove existing event listener\n            const listener = target[XHR_LISTENER];\n            if (!oriAddListener) {\n                oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            }\n            if (listener) {\n                oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n            }\n            const newListener = target[XHR_LISTENER] = () => {\n                if (target.readyState === target.DONE) {\n                    // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n                    // readyState=4 multiple times, so we need to check task state here\n                    if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n                        // check whether the xhr has registered onload listener\n                        // if that is the case, the task should invoke after all\n                        // onload listeners finish.\n                        // Also if the request failed without response (status = 0), the load event handler\n                        // will not be triggered, in that case, we should also invoke the placeholder callback\n                        // to close the XMLHttpRequest::send macroTask.\n                        // https://github.com/angular/angular/issues/38795\n                        const loadTasks = target[Zone.__symbol__('loadfalse')];\n                        if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                            const oriInvoke = task.invoke;\n                            task.invoke = function () {\n                                // need to load the tasks again, because in other\n                                // load listener, they may remove themselves\n                                const loadTasks = target[Zone.__symbol__('loadfalse')];\n                                for (let i = 0; i < loadTasks.length; i++) {\n                                    if (loadTasks[i] === task) {\n                                        loadTasks.splice(i, 1);\n                                    }\n                                }\n                                if (!data.aborted && task.state === SCHEDULED) {\n                                    oriInvoke.call(task);\n                                }\n                            };\n                            loadTasks.push(task);\n                        }\n                        else {\n                            task.invoke();\n                        }\n                    }\n                    else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n                        // error occurs when xhr.send()\n                        target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n                    }\n                }\n            };\n            oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n            const storedTask = target[XHR_TASK];\n            if (!storedTask) {\n                target[XHR_TASK] = task;\n            }\n            sendNative.apply(target, data.args);\n            target[XHR_SCHEDULED] = true;\n            return task;\n        }\n        function placeholderCallback() { }\n        function clearTask(task) {\n            const data = task.data;\n            // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n            // to prevent it from firing. So instead, we store info for the event listener.\n            data.aborted = true;\n            return abortNative.apply(data.target, data.args);\n        }\n        const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n            self[XHR_SYNC] = args[2] == false;\n            self[XHR_URL] = args[1];\n            return openNative.apply(self, args);\n        });\n        const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n        const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n        const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n        const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n            if (Zone.current[fetchTaskScheduling] === true) {\n                // a fetch is scheduling, so we are using xhr to polyfill fetch\n                // and because we already schedule macroTask for fetch, we should\n                // not schedule a macroTask for xhr again\n                return sendNative.apply(self, args);\n            }\n            if (self[XHR_SYNC]) {\n                // if the XHR is sync there is no task to schedule, just execute the code.\n                return sendNative.apply(self, args);\n            }\n            else {\n                const options = { target: self, url: self[XHR_URL], isPeriodic: false, args: args, aborted: false };\n                const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n                if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted &&\n                    task.state === SCHEDULED) {\n                    // xhr request throw error when send\n                    // we should invoke task instead of leaving a scheduled\n                    // pending macroTask\n                    task.invoke();\n                }\n            }\n        });\n        const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n            const task = findPendingTask(self);\n            if (task && typeof task.type == 'string') {\n                // If the XHR has already completed, do nothing.\n                // If the XHR has already been aborted, do nothing.\n                // Fix #569, call abort multiple times before done will cause\n                // macroTask task count be negative number\n                if (task.cancelFn == null || (task.data && task.data.aborted)) {\n                    return;\n                }\n                task.zone.cancelTask(task);\n            }\n            else if (Zone.current[fetchTaskAborting] === true) {\n                // the abort is called from fetch polyfill, we need to call native abort of XHR.\n                return abortNative.apply(self, args);\n            }\n            // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n            // task\n            // to cancel. Do nothing.\n        });\n    }\n});\nZone.__load_patch('geolocation', (global) => {\n    /// GEO_LOCATION\n    if (global['navigator'] && global['navigator'].geolocation) {\n        patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n    }\n});\nZone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n    // handle unhandled promise rejection\n    function findPromiseRejectionHandler(evtName) {\n        return function (e) {\n            const eventTasks = findEventTasks(global, evtName);\n            eventTasks.forEach(eventTask => {\n                // windows has added unhandledrejection event listener\n                // trigger the event listener\n                const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n                if (PromiseRejectionEvent) {\n                    const evt = new PromiseRejectionEvent(evtName, { promise: e.promise, reason: e.rejection });\n                    eventTask.invoke(evt);\n                }\n            });\n        };\n    }\n    if (global['PromiseRejectionEvent']) {\n        Zone[zoneSymbol('unhandledPromiseRejectionHandler')] =\n            findPromiseRejectionHandler('unhandledrejection');\n        Zone[zoneSymbol('rejectionHandledHandler')] =\n            findPromiseRejectionHandler('rejectionhandled');\n    }\n});\nZone.__load_patch('queueMicrotask', (global, Zone, api) => {\n    patchQueueMicrotask(global, api);\n});\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;AACA,CAAE,UAAUA,MAAM,EAAE;EAChB,MAAMC,WAAW,GAAGD,MAAM,CAAC,aAAa,CAAC;EACzC,SAASE,IAAIA,CAACC,IAAI,EAAE;IAChBF,WAAW,IAAIA,WAAW,CAAC,MAAM,CAAC,IAAIA,WAAW,CAAC,MAAM,CAAC,CAACE,IAAI,CAAC;EACnE;EACA,SAASC,kBAAkBA,CAACD,IAAI,EAAEE,KAAK,EAAE;IACrCJ,WAAW,IAAIA,WAAW,CAAC,SAAS,CAAC,IAAIA,WAAW,CAAC,SAAS,CAAC,CAACE,IAAI,EAAEE,KAAK,CAAC;EAChF;EACAH,IAAI,CAAC,MAAM,CAAC;EACZ;EACA;EACA;EACA,MAAMI,YAAY,GAAGN,MAAM,CAAC,sBAAsB,CAAC,IAAI,iBAAiB;EACxE,SAASO,UAAUA,CAACJ,IAAI,EAAE;IACtB,OAAOG,YAAY,GAAGH,IAAI;EAC9B;EACA,MAAMK,cAAc,GAAGR,MAAM,CAACO,UAAU,CAAC,yBAAyB,CAAC,CAAC,KAAK,IAAI;EAC7E,IAAIP,MAAM,CAAC,MAAM,CAAC,EAAE;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIQ,cAAc,IAAI,OAAOR,MAAM,CAAC,MAAM,CAAC,CAACO,UAAU,KAAK,UAAU,EAAE;MACnE,MAAM,IAAIE,KAAK,CAAC,sBAAsB,CAAC;IAC3C,CAAC,MACI;MACD,OAAOT,MAAM,CAAC,MAAM,CAAC;IACzB;EACJ;EACA,MAAMU,IAAI,CAAC;IACP;IAAA,QAAAC,CAAA,GACS,IAAI,CAACJ,UAAU,GAAGA,UAAU;IACrC,OAAOK,iBAAiBA,CAAA,EAAG;MACvB,IAAIZ,MAAM,CAAC,SAAS,CAAC,KAAKa,OAAO,CAAC,kBAAkB,CAAC,EAAE;QACnD,MAAM,IAAIJ,KAAK,CAAC,uEAAuE,GACnF,yBAAyB,GACzB,+DAA+D,GAC/D,kFAAkF,GAClF,sDAAsD,CAAC;MAC/D;IACJ;IACA,WAAWK,IAAIA,CAAA,EAAG;MACd,IAAIC,IAAI,GAAGL,IAAI,CAACM,OAAO;MACvB,OAAOD,IAAI,CAACE,MAAM,EAAE;QAChBF,IAAI,GAAGA,IAAI,CAACE,MAAM;MACtB;MACA,OAAOF,IAAI;IACf;IACA,WAAWC,OAAOA,CAAA,EAAG;MACjB,OAAOE,iBAAiB,CAACH,IAAI;IACjC;IACA,WAAWI,WAAWA,CAAA,EAAG;MACrB,OAAOC,YAAY;IACvB;IACA;IACA,OAAOC,YAAYA,CAAClB,IAAI,EAAEmB,EAAE,EAAEC,eAAe,GAAG,KAAK,EAAE;MACnD,IAAIV,OAAO,CAACW,cAAc,CAACrB,IAAI,CAAC,EAAE;QAC9B;QACA;QACA;QACA,IAAI,CAACoB,eAAe,IAAIf,cAAc,EAAE;UACpC,MAAMC,KAAK,CAAC,wBAAwB,GAAGN,IAAI,CAAC;QAChD;MACJ,CAAC,MACI,IAAI,CAACH,MAAM,CAAC,iBAAiB,GAAGG,IAAI,CAAC,EAAE;QACxC,MAAMsB,QAAQ,GAAG,OAAO,GAAGtB,IAAI;QAC/BD,IAAI,CAACuB,QAAQ,CAAC;QACdZ,OAAO,CAACV,IAAI,CAAC,GAAGmB,EAAE,CAACtB,MAAM,EAAEU,IAAI,EAAEgB,IAAI,CAAC;QACtCtB,kBAAkB,CAACqB,QAAQ,EAAEA,QAAQ,CAAC;MAC1C;IACJ;IACA,IAAIR,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACU,OAAO;IACvB;IACA,IAAIxB,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACyB,KAAK;IACrB;IACAC,WAAWA,CAACZ,MAAM,EAAEa,QAAQ,EAAE;MAC1B,IAAI,CAACH,OAAO,GAAGV,MAAM;MACrB,IAAI,CAACW,KAAK,GAAGE,QAAQ,GAAGA,QAAQ,CAAC3B,IAAI,IAAI,SAAS,GAAG,QAAQ;MAC7D,IAAI,CAAC4B,WAAW,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,UAAU,IAAI,CAAC,CAAC;MACxD,IAAI,CAACC,aAAa,GACd,IAAIC,aAAa,CAAC,IAAI,EAAE,IAAI,CAACP,OAAO,IAAI,IAAI,CAACA,OAAO,CAACM,aAAa,EAAEH,QAAQ,CAAC;IACrF;IACAK,GAAGA,CAACC,GAAG,EAAE;MACL,MAAMrB,IAAI,GAAG,IAAI,CAACsB,WAAW,CAACD,GAAG,CAAC;MAClC,IAAIrB,IAAI,EACJ,OAAOA,IAAI,CAACgB,WAAW,CAACK,GAAG,CAAC;IACpC;IACAC,WAAWA,CAACD,GAAG,EAAE;MACb,IAAIpB,OAAO,GAAG,IAAI;MAClB,OAAOA,OAAO,EAAE;QACZ,IAAIA,OAAO,CAACe,WAAW,CAACP,cAAc,CAACY,GAAG,CAAC,EAAE;UACzC,OAAOpB,OAAO;QAClB;QACAA,OAAO,GAAGA,OAAO,CAACW,OAAO;MAC7B;MACA,OAAO,IAAI;IACf;IACAW,IAAIA,CAACR,QAAQ,EAAE;MACX,IAAI,CAACA,QAAQ,EACT,MAAM,IAAIrB,KAAK,CAAC,oBAAoB,CAAC;MACzC,OAAO,IAAI,CAACwB,aAAa,CAACK,IAAI,CAAC,IAAI,EAAER,QAAQ,CAAC;IAClD;IACAS,IAAIA,CAACC,QAAQ,EAAEC,MAAM,EAAE;MACnB,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;QAChC,MAAM,IAAI/B,KAAK,CAAC,0BAA0B,GAAG+B,QAAQ,CAAC;MAC1D;MACA,MAAME,SAAS,GAAG,IAAI,CAACT,aAAa,CAACU,SAAS,CAAC,IAAI,EAAEH,QAAQ,EAAEC,MAAM,CAAC;MACtE,MAAM1B,IAAI,GAAG,IAAI;MACjB,OAAO,YAAY;QACf,OAAOA,IAAI,CAAC6B,UAAU,CAACF,SAAS,EAAE,IAAI,EAAEG,SAAS,EAAEJ,MAAM,CAAC;MAC9D,CAAC;IACL;IACAK,GAAGA,CAACN,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACxCvB,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,OAAO,IAAI,CAACkB,aAAa,CAACgB,MAAM,CAAC,IAAI,EAAET,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC;MAClF,CAAC,SACO;QACJvB,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;MAChD;IACJ;IACA2B,UAAUA,CAACJ,QAAQ,EAAEO,SAAS,GAAG,IAAI,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACtDvB,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,IAAI;UACA,OAAO,IAAI,CAACkB,aAAa,CAACgB,MAAM,CAAC,IAAI,EAAET,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC;QAClF,CAAC,CACD,OAAOS,KAAK,EAAE;UACV,IAAI,IAAI,CAACjB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAED,KAAK,CAAC,EAAE;YAC7C,MAAMA,KAAK;UACf;QACJ;MACJ,CAAC,SACO;QACJhC,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;MAChD;IACJ;IACAmC,OAAOA,CAACC,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;MAChC,IAAIK,IAAI,CAACtC,IAAI,IAAI,IAAI,EAAE;QACnB,MAAM,IAAIN,KAAK,CAAC,6DAA6D,GACzE,CAAC4C,IAAI,CAACtC,IAAI,IAAIuC,OAAO,EAAEnD,IAAI,GAAG,eAAe,GAAG,IAAI,CAACA,IAAI,GAAG,GAAG,CAAC;MACxE;MACA;MACA;MACA;MACA,IAAIkD,IAAI,CAACE,KAAK,KAAKC,YAAY,KAAKH,IAAI,CAACI,IAAI,KAAKC,SAAS,IAAIL,IAAI,CAACI,IAAI,KAAKE,SAAS,CAAC,EAAE;QACrF;MACJ;MACA,MAAMC,YAAY,GAAGP,IAAI,CAACE,KAAK,IAAIM,OAAO;MAC1CD,YAAY,IAAIP,IAAI,CAACS,aAAa,CAACD,OAAO,EAAEE,SAAS,CAAC;MACtDV,IAAI,CAACW,QAAQ,EAAE;MACf,MAAMC,YAAY,GAAG7C,YAAY;MACjCA,YAAY,GAAGiC,IAAI;MACnBnC,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,IAAIsC,IAAI,CAACI,IAAI,IAAIE,SAAS,IAAIN,IAAI,CAACa,IAAI,IAAI,CAACb,IAAI,CAACa,IAAI,CAACC,UAAU,EAAE;UAC9Dd,IAAI,CAACe,QAAQ,GAAGC,SAAS;QAC7B;QACA,IAAI;UACA,OAAO,IAAI,CAACpC,aAAa,CAACqC,UAAU,CAAC,IAAI,EAAEjB,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC;QAC1E,CAAC,CACD,OAAOE,KAAK,EAAE;UACV,IAAI,IAAI,CAACjB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAED,KAAK,CAAC,EAAE;YAC7C,MAAMA,KAAK;UACf;QACJ;MACJ,CAAC,SACO;QACJ;QACA;QACA,IAAIG,IAAI,CAACE,KAAK,KAAKC,YAAY,IAAIH,IAAI,CAACE,KAAK,KAAKgB,OAAO,EAAE;UACvD,IAAIlB,IAAI,CAACI,IAAI,IAAIC,SAAS,IAAKL,IAAI,CAACa,IAAI,IAAIb,IAAI,CAACa,IAAI,CAACC,UAAW,EAAE;YAC/DP,YAAY,IAAIP,IAAI,CAACS,aAAa,CAACC,SAAS,EAAEF,OAAO,CAAC;UAC1D,CAAC,MACI;YACDR,IAAI,CAACW,QAAQ,GAAG,CAAC;YACjB,IAAI,CAACQ,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC,CAAC;YAC/BO,YAAY,IACRP,IAAI,CAACS,aAAa,CAACN,YAAY,EAAEK,OAAO,EAAEL,YAAY,CAAC;UAC/D;QACJ;QACAtC,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;QAC5CG,YAAY,GAAG6C,YAAY;MAC/B;IACJ;IACAQ,YAAYA,CAACpB,IAAI,EAAE;MACf,IAAIA,IAAI,CAACtC,IAAI,IAAIsC,IAAI,CAACtC,IAAI,KAAK,IAAI,EAAE;QACjC;QACA;QACA,IAAI2D,OAAO,GAAG,IAAI;QAClB,OAAOA,OAAO,EAAE;UACZ,IAAIA,OAAO,KAAKrB,IAAI,CAACtC,IAAI,EAAE;YACvB,MAAMN,KAAK,CAAE,8BAA6B,IAAI,CAACN,IAAK,8CAA6CkD,IAAI,CAACtC,IAAI,CAACZ,IAAK,EAAC,CAAC;UACtH;UACAuE,OAAO,GAAGA,OAAO,CAACzD,MAAM;QAC5B;MACJ;MACAoC,IAAI,CAACS,aAAa,CAACa,UAAU,EAAEnB,YAAY,CAAC;MAC5C,MAAMoB,aAAa,GAAG,EAAE;MACxBvB,IAAI,CAACwB,cAAc,GAAGD,aAAa;MACnCvB,IAAI,CAACyB,KAAK,GAAG,IAAI;MACjB,IAAI;QACAzB,IAAI,GAAG,IAAI,CAACpB,aAAa,CAACwC,YAAY,CAAC,IAAI,EAAEpB,IAAI,CAAC;MACtD,CAAC,CACD,OAAO0B,GAAG,EAAE;QACR;QACA;QACA1B,IAAI,CAACS,aAAa,CAACS,OAAO,EAAEI,UAAU,EAAEnB,YAAY,CAAC;QACrD;QACA,IAAI,CAACvB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAE4B,GAAG,CAAC;QACzC,MAAMA,GAAG;MACb;MACA,IAAI1B,IAAI,CAACwB,cAAc,KAAKD,aAAa,EAAE;QACvC;QACA,IAAI,CAACJ,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC;MAClC;MACA,IAAIA,IAAI,CAACE,KAAK,IAAIoB,UAAU,EAAE;QAC1BtB,IAAI,CAACS,aAAa,CAACC,SAAS,EAAEY,UAAU,CAAC;MAC7C;MACA,OAAOtB,IAAI;IACf;IACA2B,iBAAiBA,CAACvC,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAE;MACtD,OAAO,IAAI,CAACR,YAAY,CAAC,IAAIS,QAAQ,CAACC,SAAS,EAAE1C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEZ,SAAS,CAAC,CAAC;IACxG;IACAe,iBAAiBA,CAAC3C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;MACpE,OAAO,IAAI,CAACZ,YAAY,CAAC,IAAIS,QAAQ,CAACvB,SAAS,EAAElB,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC,CAAC;IAC3G;IACAC,iBAAiBA,CAAC7C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;MACpE,OAAO,IAAI,CAACZ,YAAY,CAAC,IAAIS,QAAQ,CAACxB,SAAS,EAAEjB,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC,CAAC;IAC3G;IACAE,UAAUA,CAAClC,IAAI,EAAE;MACb,IAAIA,IAAI,CAACtC,IAAI,IAAI,IAAI,EACjB,MAAM,IAAIN,KAAK,CAAC,mEAAmE,GAC/E,CAAC4C,IAAI,CAACtC,IAAI,IAAIuC,OAAO,EAAEnD,IAAI,GAAG,eAAe,GAAG,IAAI,CAACA,IAAI,GAAG,GAAG,CAAC;MACxE,IAAIkD,IAAI,CAACE,KAAK,KAAKQ,SAAS,IAAIV,IAAI,CAACE,KAAK,KAAKM,OAAO,EAAE;QACpD;MACJ;MACAR,IAAI,CAACS,aAAa,CAAC0B,SAAS,EAAEzB,SAAS,EAAEF,OAAO,CAAC;MACjD,IAAI;QACA,IAAI,CAAC5B,aAAa,CAACsD,UAAU,CAAC,IAAI,EAAElC,IAAI,CAAC;MAC7C,CAAC,CACD,OAAO0B,GAAG,EAAE;QACR;QACA1B,IAAI,CAACS,aAAa,CAACS,OAAO,EAAEiB,SAAS,CAAC;QACtC,IAAI,CAACvD,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAE4B,GAAG,CAAC;QACzC,MAAMA,GAAG;MACb;MACA,IAAI,CAACP,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC,CAAC;MAC/BA,IAAI,CAACS,aAAa,CAACN,YAAY,EAAEgC,SAAS,CAAC;MAC3CnC,IAAI,CAACW,QAAQ,GAAG,CAAC;MACjB,OAAOX,IAAI;IACf;IACAmB,gBAAgBA,CAACnB,IAAI,EAAEoC,KAAK,EAAE;MAC1B,MAAMb,aAAa,GAAGvB,IAAI,CAACwB,cAAc;MACzC,IAAIY,KAAK,IAAI,CAAC,CAAC,EAAE;QACbpC,IAAI,CAACwB,cAAc,GAAG,IAAI;MAC9B;MACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,aAAa,CAACe,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3Cd,aAAa,CAACc,CAAC,CAAC,CAAClB,gBAAgB,CAACnB,IAAI,CAACI,IAAI,EAAEgC,KAAK,CAAC;MACvD;IACJ;EACJ;EACA,MAAMG,WAAW,GAAG;IAChBzF,IAAI,EAAE,EAAE;IACR0F,SAAS,EAAEA,CAACC,QAAQ,EAAEnF,CAAC,EAAEoF,MAAM,EAAEC,YAAY,KAAKF,QAAQ,CAACG,OAAO,CAACF,MAAM,EAAEC,YAAY,CAAC;IACxFE,cAAc,EAAEA,CAACJ,QAAQ,EAAEnF,CAAC,EAAEoF,MAAM,EAAE1C,IAAI,KAAKyC,QAAQ,CAACrB,YAAY,CAACsB,MAAM,EAAE1C,IAAI,CAAC;IAClF8C,YAAY,EAAEA,CAACL,QAAQ,EAAEnF,CAAC,EAAEoF,MAAM,EAAE1C,IAAI,EAAEN,SAAS,EAAEC,SAAS,KAAK8C,QAAQ,CAACxB,UAAU,CAACyB,MAAM,EAAE1C,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC;IAC1HoD,YAAY,EAAEA,CAACN,QAAQ,EAAEnF,CAAC,EAAEoF,MAAM,EAAE1C,IAAI,KAAKyC,QAAQ,CAACP,UAAU,CAACQ,MAAM,EAAE1C,IAAI;EACjF,CAAC;EACD,MAAMnB,aAAa,CAAC;IAChBL,WAAWA,CAACd,IAAI,EAAEsF,cAAc,EAAEvE,QAAQ,EAAE;MACxC,IAAI,CAACwE,WAAW,GAAG;QAAE,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE;MAAE,CAAC;MACrE,IAAI,CAACvF,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACwF,eAAe,GAAGF,cAAc;MACrC,IAAI,CAACG,OAAO,GAAG1E,QAAQ,KAAKA,QAAQ,IAAIA,QAAQ,CAAC2E,MAAM,GAAG3E,QAAQ,GAAGuE,cAAc,CAACG,OAAO,CAAC;MAC5F,IAAI,CAACE,SAAS,GAAG5E,QAAQ,KAAKA,QAAQ,CAAC2E,MAAM,GAAGJ,cAAc,GAAGA,cAAc,CAACK,SAAS,CAAC;MAC1F,IAAI,CAACC,aAAa,GACd7E,QAAQ,KAAKA,QAAQ,CAAC2E,MAAM,GAAG,IAAI,CAAC1F,IAAI,GAAGsF,cAAc,CAACM,aAAa,CAAC;MAC5E,IAAI,CAACC,YAAY,GACb9E,QAAQ,KAAKA,QAAQ,CAAC+E,WAAW,GAAG/E,QAAQ,GAAGuE,cAAc,CAACO,YAAY,CAAC;MAC/E,IAAI,CAACE,cAAc,GACfhF,QAAQ,KAAKA,QAAQ,CAAC+E,WAAW,GAAGR,cAAc,GAAGA,cAAc,CAACS,cAAc,CAAC;MACvF,IAAI,CAACC,kBAAkB,GACnBjF,QAAQ,KAAKA,QAAQ,CAAC+E,WAAW,GAAG,IAAI,CAAC9F,IAAI,GAAGsF,cAAc,CAACU,kBAAkB,CAAC;MACtF,IAAI,CAACC,SAAS,GAAGlF,QAAQ,KAAKA,QAAQ,CAACmF,QAAQ,GAAGnF,QAAQ,GAAGuE,cAAc,CAACW,SAAS,CAAC;MACtF,IAAI,CAACE,WAAW,GACZpF,QAAQ,KAAKA,QAAQ,CAACmF,QAAQ,GAAGZ,cAAc,GAAGA,cAAc,CAACa,WAAW,CAAC;MACjF,IAAI,CAACC,eAAe,GAChBrF,QAAQ,KAAKA,QAAQ,CAACmF,QAAQ,GAAG,IAAI,CAAClG,IAAI,GAAGsF,cAAc,CAACc,eAAe,CAAC;MAChF,IAAI,CAACC,cAAc,GACftF,QAAQ,KAAKA,QAAQ,CAACuF,aAAa,GAAGvF,QAAQ,GAAGuE,cAAc,CAACe,cAAc,CAAC;MACnF,IAAI,CAACE,gBAAgB,GACjBxF,QAAQ,KAAKA,QAAQ,CAACuF,aAAa,GAAGhB,cAAc,GAAGA,cAAc,CAACiB,gBAAgB,CAAC;MAC3F,IAAI,CAACC,oBAAoB,GACrBzF,QAAQ,KAAKA,QAAQ,CAACuF,aAAa,GAAG,IAAI,CAACtG,IAAI,GAAGsF,cAAc,CAACkB,oBAAoB,CAAC;MAC1F,IAAI,CAACC,eAAe,GAChB1F,QAAQ,KAAKA,QAAQ,CAACoE,cAAc,GAAGpE,QAAQ,GAAGuE,cAAc,CAACmB,eAAe,CAAC;MACrF,IAAI,CAACC,iBAAiB,GAAG3F,QAAQ,KAC5BA,QAAQ,CAACoE,cAAc,GAAGG,cAAc,GAAGA,cAAc,CAACoB,iBAAiB,CAAC;MACjF,IAAI,CAACC,qBAAqB,GACtB5F,QAAQ,KAAKA,QAAQ,CAACoE,cAAc,GAAG,IAAI,CAACnF,IAAI,GAAGsF,cAAc,CAACqB,qBAAqB,CAAC;MAC5F,IAAI,CAACC,aAAa,GACd7F,QAAQ,KAAKA,QAAQ,CAACqE,YAAY,GAAGrE,QAAQ,GAAGuE,cAAc,CAACsB,aAAa,CAAC;MACjF,IAAI,CAACC,eAAe,GAChB9F,QAAQ,KAAKA,QAAQ,CAACqE,YAAY,GAAGE,cAAc,GAAGA,cAAc,CAACuB,eAAe,CAAC;MACzF,IAAI,CAACC,mBAAmB,GACpB/F,QAAQ,KAAKA,QAAQ,CAACqE,YAAY,GAAG,IAAI,CAACpF,IAAI,GAAGsF,cAAc,CAACwB,mBAAmB,CAAC;MACxF,IAAI,CAACC,aAAa,GACdhG,QAAQ,KAAKA,QAAQ,CAACsE,YAAY,GAAGtE,QAAQ,GAAGuE,cAAc,CAACyB,aAAa,CAAC;MACjF,IAAI,CAACC,eAAe,GAChBjG,QAAQ,KAAKA,QAAQ,CAACsE,YAAY,GAAGC,cAAc,GAAGA,cAAc,CAAC0B,eAAe,CAAC;MACzF,IAAI,CAACC,mBAAmB,GACpBlG,QAAQ,KAAKA,QAAQ,CAACsE,YAAY,GAAG,IAAI,CAACrF,IAAI,GAAGsF,cAAc,CAAC2B,mBAAmB,CAAC;MACxF,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,MAAMC,eAAe,GAAGvG,QAAQ,IAAIA,QAAQ,CAAC+D,SAAS;MACtD,MAAMyC,aAAa,GAAGjC,cAAc,IAAIA,cAAc,CAAC4B,UAAU;MACjE,IAAII,eAAe,IAAIC,aAAa,EAAE;QAClC;QACA;QACA,IAAI,CAACL,UAAU,GAAGI,eAAe,GAAGvG,QAAQ,GAAG8D,WAAW;QAC1D,IAAI,CAACsC,YAAY,GAAG7B,cAAc;QAClC,IAAI,CAAC8B,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,gBAAgB,GAAGrH,IAAI;QAC5B,IAAI,CAACe,QAAQ,CAACoE,cAAc,EAAE;UAC1B,IAAI,CAACsB,eAAe,GAAG5B,WAAW;UAClC,IAAI,CAAC6B,iBAAiB,GAAGpB,cAAc;UACvC,IAAI,CAACqB,qBAAqB,GAAG,IAAI,CAAC3G,IAAI;QAC1C;QACA,IAAI,CAACe,QAAQ,CAACqE,YAAY,EAAE;UACxB,IAAI,CAACwB,aAAa,GAAG/B,WAAW;UAChC,IAAI,CAACgC,eAAe,GAAGvB,cAAc;UACrC,IAAI,CAACwB,mBAAmB,GAAG,IAAI,CAAC9G,IAAI;QACxC;QACA,IAAI,CAACe,QAAQ,CAACsE,YAAY,EAAE;UACxB,IAAI,CAAC0B,aAAa,GAAGlC,WAAW;UAChC,IAAI,CAACmC,eAAe,GAAG1B,cAAc;UACrC,IAAI,CAAC2B,mBAAmB,GAAG,IAAI,CAACjH,IAAI;QACxC;MACJ;IACJ;IACAuB,IAAIA,CAACiG,UAAU,EAAEzG,QAAQ,EAAE;MACvB,OAAO,IAAI,CAAC0E,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACC,SAAS,EAAE,IAAI,CAAC3F,IAAI,EAAEwH,UAAU,EAAEzG,QAAQ,CAAC,GACtF,IAAIpB,IAAI,CAAC6H,UAAU,EAAEzG,QAAQ,CAAC;IACtC;IACAa,SAASA,CAAC4F,UAAU,EAAE/F,QAAQ,EAAEC,MAAM,EAAE;MACpC,OAAO,IAAI,CAACmE,YAAY,GACpB,IAAI,CAACA,YAAY,CAACC,WAAW,CAAC,IAAI,CAACC,cAAc,EAAE,IAAI,CAACC,kBAAkB,EAAEwB,UAAU,EAAE/F,QAAQ,EAAEC,MAAM,CAAC,GACzGD,QAAQ;IAChB;IACAS,MAAMA,CAACsF,UAAU,EAAE/F,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACvD,OAAO,IAAI,CAACuE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,eAAe,EAAEoB,UAAU,EAAE/F,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC,GACvID,QAAQ,CAACgG,KAAK,CAACzF,SAAS,EAAEC,SAAS,CAAC;IAC5C;IACAG,WAAWA,CAACoF,UAAU,EAAErF,KAAK,EAAE;MAC3B,OAAO,IAAI,CAACkE,cAAc,GACtB,IAAI,CAACA,cAAc,CAACC,aAAa,CAAC,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,oBAAoB,EAAEgB,UAAU,EAAErF,KAAK,CAAC,GACtG,IAAI;IACZ;IACAuB,YAAYA,CAAC8D,UAAU,EAAElF,IAAI,EAAE;MAC3B,IAAIoF,UAAU,GAAGpF,IAAI;MACrB,IAAI,IAAI,CAACmE,eAAe,EAAE;QACtB,IAAI,IAAI,CAACS,UAAU,EAAE;UACjBQ,UAAU,CAAC5D,cAAc,CAAC6D,IAAI,CAAC,IAAI,CAACP,iBAAiB,CAAC;QAC1D;QACA;QACAM,UAAU,GAAG,IAAI,CAACjB,eAAe,CAACtB,cAAc,CAAC,IAAI,CAACuB,iBAAiB,EAAE,IAAI,CAACC,qBAAqB,EAAEa,UAAU,EAAElF,IAAI,CAAC;QACtH;QACA,IAAI,CAACoF,UAAU,EACXA,UAAU,GAAGpF,IAAI;MACzB,CAAC,MACI;QACD,IAAIA,IAAI,CAACsF,UAAU,EAAE;UACjBtF,IAAI,CAACsF,UAAU,CAACtF,IAAI,CAAC;QACzB,CAAC,MACI,IAAIA,IAAI,CAACI,IAAI,IAAI0B,SAAS,EAAE;UAC7BH,iBAAiB,CAAC3B,IAAI,CAAC;QAC3B,CAAC,MACI;UACD,MAAM,IAAI5C,KAAK,CAAC,6BAA6B,CAAC;QAClD;MACJ;MACA,OAAOgI,UAAU;IACrB;IACAnE,UAAUA,CAACiE,UAAU,EAAElF,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;MAC/C,OAAO,IAAI,CAAC2E,aAAa,GAAG,IAAI,CAACA,aAAa,CAACxB,YAAY,CAAC,IAAI,CAACyB,eAAe,EAAE,IAAI,CAACC,mBAAmB,EAAEU,UAAU,EAAElF,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC,GAC/IK,IAAI,CAACb,QAAQ,CAACgG,KAAK,CAACzF,SAAS,EAAEC,SAAS,CAAC;IACjD;IACAuC,UAAUA,CAACgD,UAAU,EAAElF,IAAI,EAAE;MACzB,IAAIuF,KAAK;MACT,IAAI,IAAI,CAACd,aAAa,EAAE;QACpBc,KAAK,GAAG,IAAI,CAACd,aAAa,CAAC1B,YAAY,CAAC,IAAI,CAAC2B,eAAe,EAAE,IAAI,CAACC,mBAAmB,EAAEO,UAAU,EAAElF,IAAI,CAAC;MAC7G,CAAC,MACI;QACD,IAAI,CAACA,IAAI,CAACe,QAAQ,EAAE;UAChB,MAAM3D,KAAK,CAAC,wBAAwB,CAAC;QACzC;QACAmI,KAAK,GAAGvF,IAAI,CAACe,QAAQ,CAACf,IAAI,CAAC;MAC/B;MACA,OAAOuF,KAAK;IAChB;IACA3C,OAAOA,CAACsC,UAAU,EAAEM,OAAO,EAAE;MACzB;MACA;MACA,IAAI;QACA,IAAI,CAACZ,UAAU,IACX,IAAI,CAACA,UAAU,CAACpC,SAAS,CAAC,IAAI,CAACqC,YAAY,EAAE,IAAI,CAACE,gBAAgB,EAAEG,UAAU,EAAEM,OAAO,CAAC;MAChG,CAAC,CACD,OAAO9D,GAAG,EAAE;QACR,IAAI,CAAC5B,WAAW,CAACoF,UAAU,EAAExD,GAAG,CAAC;MACrC;IACJ;IACA;IACAP,gBAAgBA,CAACf,IAAI,EAAEgC,KAAK,EAAE;MAC1B,MAAMqD,MAAM,GAAG,IAAI,CAACxC,WAAW;MAC/B,MAAMyC,IAAI,GAAGD,MAAM,CAACrF,IAAI,CAAC;MACzB,MAAMuF,IAAI,GAAGF,MAAM,CAACrF,IAAI,CAAC,GAAGsF,IAAI,GAAGtD,KAAK;MACxC,IAAIuD,IAAI,GAAG,CAAC,EAAE;QACV,MAAM,IAAIvI,KAAK,CAAC,0CAA0C,CAAC;MAC/D;MACA,IAAIsI,IAAI,IAAI,CAAC,IAAIC,IAAI,IAAI,CAAC,EAAE;QACxB,MAAMH,OAAO,GAAG;UACZ1D,SAAS,EAAE2D,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCnF,SAAS,EAAEmF,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCpF,SAAS,EAAEoF,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCG,MAAM,EAAExF;QACZ,CAAC;QACD,IAAI,CAACwC,OAAO,CAAC,IAAI,CAAClF,IAAI,EAAE8H,OAAO,CAAC;MACpC;IACJ;EACJ;EACA,MAAM3D,QAAQ,CAAC;IACXrD,WAAWA,CAAC4B,IAAI,EAAEhB,MAAM,EAAED,QAAQ,EAAE0G,OAAO,EAAEP,UAAU,EAAEvE,QAAQ,EAAE;MAC/D;MACA,IAAI,CAACU,KAAK,GAAG,IAAI;MACjB,IAAI,CAACd,QAAQ,GAAG,CAAC;MACjB;MACA,IAAI,CAACa,cAAc,GAAG,IAAI;MAC1B;MACA,IAAI,CAACsE,MAAM,GAAG,cAAc;MAC5B,IAAI,CAAC1F,IAAI,GAAGA,IAAI;MAChB,IAAI,CAAChB,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACyB,IAAI,GAAGgF,OAAO;MACnB,IAAI,CAACP,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACvE,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAAC5B,QAAQ,EAAE;QACX,MAAM,IAAI/B,KAAK,CAAC,yBAAyB,CAAC;MAC9C;MACA,IAAI,CAAC+B,QAAQ,GAAGA,QAAQ;MACxB,MAAM4G,IAAI,GAAG,IAAI;MACjB;MACA,IAAI3F,IAAI,KAAKC,SAAS,IAAIwF,OAAO,IAAIA,OAAO,CAACG,IAAI,EAAE;QAC/C,IAAI,CAACpG,MAAM,GAAGiC,QAAQ,CAACZ,UAAU;MACrC,CAAC,MACI;QACD,IAAI,CAACrB,MAAM,GAAG,YAAY;UACtB,OAAOiC,QAAQ,CAACZ,UAAU,CAACgF,IAAI,CAACtJ,MAAM,EAAEoJ,IAAI,EAAE,IAAI,EAAEvG,SAAS,CAAC;QAClE,CAAC;MACL;IACJ;IACA,OAAOyB,UAAUA,CAACjB,IAAI,EAAE0C,MAAM,EAAEwD,IAAI,EAAE;MAClC,IAAI,CAAClG,IAAI,EAAE;QACPA,IAAI,GAAG,IAAI;MACf;MACAmG,yBAAyB,EAAE;MAC3B,IAAI;QACAnG,IAAI,CAACW,QAAQ,EAAE;QACf,OAAOX,IAAI,CAACtC,IAAI,CAACqC,OAAO,CAACC,IAAI,EAAE0C,MAAM,EAAEwD,IAAI,CAAC;MAChD,CAAC,SACO;QACJ,IAAIC,yBAAyB,IAAI,CAAC,EAAE;UAChCC,mBAAmB,CAAC,CAAC;QACzB;QACAD,yBAAyB,EAAE;MAC/B;IACJ;IACA,IAAIzI,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAAC+D,KAAK;IACrB;IACA,IAAIvB,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAAC4F,MAAM;IACtB;IACAO,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAAC5F,aAAa,CAACN,YAAY,EAAEmB,UAAU,CAAC;IAChD;IACA;IACAb,aAAaA,CAAC6F,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAE;MAC3C,IAAI,IAAI,CAACV,MAAM,KAAKS,UAAU,IAAI,IAAI,CAACT,MAAM,KAAKU,UAAU,EAAE;QAC1D,IAAI,CAACV,MAAM,GAAGQ,OAAO;QACrB,IAAIA,OAAO,IAAInG,YAAY,EAAE;UACzB,IAAI,CAACqB,cAAc,GAAG,IAAI;QAC9B;MACJ,CAAC,MACI;QACD,MAAM,IAAIpE,KAAK,CAAE,GAAE,IAAI,CAACgD,IAAK,KAAI,IAAI,CAAChB,MAAO,6BAA4BkH,OAAQ,uBAAsBC,UAAW,IAAGC,UAAU,GAAG,QAAQ,GAAGA,UAAU,GAAG,IAAI,GAAG,EAAG,UAAS,IAAI,CAACV,MAAO,IAAG,CAAC;MACjM;IACJ;IACAW,QAAQA,CAAA,EAAG;MACP,IAAI,IAAI,CAAC5F,IAAI,IAAI,OAAO,IAAI,CAACA,IAAI,CAAC6F,QAAQ,KAAK,WAAW,EAAE;QACxD,OAAO,IAAI,CAAC7F,IAAI,CAAC6F,QAAQ,CAACD,QAAQ,CAAC,CAAC;MACxC,CAAC,MACI;QACD,OAAOE,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;MAC/C;IACJ;IACA;IACA;IACAY,MAAMA,CAAA,EAAG;MACL,OAAO;QACHzG,IAAI,EAAE,IAAI,CAACA,IAAI;QACfF,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBd,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB1B,IAAI,EAAE,IAAI,CAACA,IAAI,CAACZ,IAAI;QACpB6D,QAAQ,EAAE,IAAI,CAACA;MACnB,CAAC;IACL;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAAMmG,gBAAgB,GAAG5J,UAAU,CAAC,YAAY,CAAC;EACjD,MAAM6J,aAAa,GAAG7J,UAAU,CAAC,SAAS,CAAC;EAC3C,MAAM8J,UAAU,GAAG9J,UAAU,CAAC,MAAM,CAAC;EACrC,IAAI+J,eAAe,GAAG,EAAE;EACxB,IAAIC,yBAAyB,GAAG,KAAK;EACrC,IAAIC,2BAA2B;EAC/B,SAASC,uBAAuBA,CAACC,IAAI,EAAE;IACnC,IAAI,CAACF,2BAA2B,EAAE;MAC9B,IAAIxK,MAAM,CAACoK,aAAa,CAAC,EAAE;QACvBI,2BAA2B,GAAGxK,MAAM,CAACoK,aAAa,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC;MAClE;IACJ;IACA,IAAIH,2BAA2B,EAAE;MAC7B,IAAII,UAAU,GAAGJ,2BAA2B,CAACH,UAAU,CAAC;MACxD,IAAI,CAACO,UAAU,EAAE;QACb;QACA;QACAA,UAAU,GAAGJ,2BAA2B,CAAC,MAAM,CAAC;MACpD;MACAI,UAAU,CAACtB,IAAI,CAACkB,2BAA2B,EAAEE,IAAI,CAAC;IACtD,CAAC,MACI;MACD1K,MAAM,CAACmK,gBAAgB,CAAC,CAACO,IAAI,EAAE,CAAC,CAAC;IACrC;EACJ;EACA,SAAS1F,iBAAiBA,CAAC3B,IAAI,EAAE;IAC7B;IACA;IACA,IAAImG,yBAAyB,KAAK,CAAC,IAAIc,eAAe,CAAC3E,MAAM,KAAK,CAAC,EAAE;MACjE;MACA8E,uBAAuB,CAAChB,mBAAmB,CAAC;IAChD;IACApG,IAAI,IAAIiH,eAAe,CAAC5B,IAAI,CAACrF,IAAI,CAAC;EACtC;EACA,SAASoG,mBAAmBA,CAAA,EAAG;IAC3B,IAAI,CAACc,yBAAyB,EAAE;MAC5BA,yBAAyB,GAAG,IAAI;MAChC,OAAOD,eAAe,CAAC3E,MAAM,EAAE;QAC3B,MAAMkF,KAAK,GAAGP,eAAe;QAC7BA,eAAe,GAAG,EAAE;QACpB,KAAK,IAAI5E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,KAAK,CAAClF,MAAM,EAAED,CAAC,EAAE,EAAE;UACnC,MAAMrC,IAAI,GAAGwH,KAAK,CAACnF,CAAC,CAAC;UACrB,IAAI;YACArC,IAAI,CAACtC,IAAI,CAACqC,OAAO,CAACC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACvC,CAAC,CACD,OAAOH,KAAK,EAAE;YACVxB,IAAI,CAACoJ,gBAAgB,CAAC5H,KAAK,CAAC;UAChC;QACJ;MACJ;MACAxB,IAAI,CAACqJ,kBAAkB,CAAC,CAAC;MACzBR,yBAAyB,GAAG,KAAK;IACrC;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAAMjH,OAAO,GAAG;IAAEnD,IAAI,EAAE;EAAU,CAAC;EACnC,MAAMqD,YAAY,GAAG,cAAc;IAAEmB,UAAU,GAAG,YAAY;IAAEZ,SAAS,GAAG,WAAW;IAAEF,OAAO,GAAG,SAAS;IAAE2B,SAAS,GAAG,WAAW;IAAEjB,OAAO,GAAG,SAAS;EAC1J,MAAMY,SAAS,GAAG,WAAW;IAAExB,SAAS,GAAG,WAAW;IAAED,SAAS,GAAG,WAAW;EAC/E,MAAM7C,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMa,IAAI,GAAG;IACTsJ,MAAM,EAAEzK,UAAU;IAClB0K,gBAAgB,EAAEA,CAAA,KAAM/J,iBAAiB;IACzC4J,gBAAgB,EAAEI,IAAI;IACtBH,kBAAkB,EAAEG,IAAI;IACxBlG,iBAAiB,EAAEA,iBAAiB;IACpCmG,iBAAiB,EAAEA,CAAA,KAAM,CAACzK,IAAI,CAACH,UAAU,CAAC,iCAAiC,CAAC,CAAC;IAC7E6K,gBAAgB,EAAEA,CAAA,KAAM,EAAE;IAC1BC,iBAAiB,EAAEH,IAAI;IACvBI,WAAW,EAAEA,CAAA,KAAMJ,IAAI;IACvBK,aAAa,EAAEA,CAAA,KAAM,EAAE;IACvBC,SAAS,EAAEA,CAAA,KAAMN,IAAI;IACrBO,cAAc,EAAEA,CAAA,KAAMP,IAAI;IAC1BQ,mBAAmB,EAAEA,CAAA,KAAMR,IAAI;IAC/BS,UAAU,EAAEA,CAAA,KAAM,KAAK;IACvBC,gBAAgB,EAAEA,CAAA,KAAMvH,SAAS;IACjCwH,oBAAoB,EAAEA,CAAA,KAAMX,IAAI;IAChCY,8BAA8B,EAAEA,CAAA,KAAMzH,SAAS;IAC/C0H,YAAY,EAAEA,CAAA,KAAM1H,SAAS;IAC7B2H,UAAU,EAAEA,CAAA,KAAM,EAAE;IACpBC,UAAU,EAAEA,CAAA,KAAMf,IAAI;IACtBgB,mBAAmB,EAAEA,CAAA,KAAMhB,IAAI;IAC/BiB,gBAAgB,EAAEA,CAAA,KAAM,EAAE;IAC1BC,qBAAqB,EAAEA,CAAA,KAAMlB,IAAI;IACjCmB,iBAAiB,EAAEA,CAAA,KAAMnB,IAAI;IAC7BoB,cAAc,EAAEA,CAAA,KAAMpB,IAAI;IAC1BT,uBAAuB,EAAEA;EAC7B,CAAC;EACD,IAAIvJ,iBAAiB,GAAG;IAAED,MAAM,EAAE,IAAI;IAAEF,IAAI,EAAE,IAAIL,IAAI,CAAC,IAAI,EAAE,IAAI;EAAE,CAAC;EACpE,IAAIU,YAAY,GAAG,IAAI;EACvB,IAAIoI,yBAAyB,GAAG,CAAC;EACjC,SAAS0B,IAAIA,CAAA,EAAG,CAAE;EAClB9K,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC;EAClC,OAAOJ,MAAM,CAAC,MAAM,CAAC,GAAGU,IAAI;AAChC,CAAC,EAAG,OAAO6L,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAI,OAAOnD,IAAI,KAAK,WAAW,IAAIA,IAAI,IAAIpJ,MAAM,CAAC;;AAE7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8L,8BAA8B,GAAG9B,MAAM,CAACwC,wBAAwB;AACtE;AACA,MAAMX,oBAAoB,GAAG7B,MAAM,CAACyC,cAAc;AAClD;AACA,MAAMC,oBAAoB,GAAG1C,MAAM,CAAC2C,cAAc;AAClD;AACA,MAAMZ,YAAY,GAAG/B,MAAM,CAAC4C,MAAM;AAClC;AACA,MAAMZ,UAAU,GAAGa,KAAK,CAAC5C,SAAS,CAAC6C,KAAK;AACxC;AACA,MAAMC,sBAAsB,GAAG,kBAAkB;AACjD;AACA,MAAMC,yBAAyB,GAAG,qBAAqB;AACvD;AACA,MAAMC,8BAA8B,GAAGvM,IAAI,CAACH,UAAU,CAACwM,sBAAsB,CAAC;AAC9E;AACA,MAAMG,iCAAiC,GAAGxM,IAAI,CAACH,UAAU,CAACyM,yBAAyB,CAAC;AACpF;AACA,MAAMG,QAAQ,GAAG,MAAM;AACvB;AACA,MAAMC,SAAS,GAAG,OAAO;AACzB;AACA,MAAMC,kBAAkB,GAAG3M,IAAI,CAACH,UAAU,CAAC,EAAE,CAAC;AAC9C,SAAS2L,mBAAmBA,CAAC1J,QAAQ,EAAEC,MAAM,EAAE;EAC3C,OAAO/B,IAAI,CAACM,OAAO,CAACuB,IAAI,CAACC,QAAQ,EAAEC,MAAM,CAAC;AAC9C;AACA,SAAS6K,gCAAgCA,CAAC7K,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;EAC5F,OAAO3E,IAAI,CAACM,OAAO,CAACoE,iBAAiB,CAAC3C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC;AAC/F;AACA,MAAMkI,UAAU,GAAG7M,IAAI,CAACH,UAAU;AAClC,MAAMiN,cAAc,GAAG,OAAOjB,MAAM,KAAK,WAAW;AACpD,MAAMkB,cAAc,GAAGD,cAAc,GAAGjB,MAAM,GAAGlI,SAAS;AAC1D,MAAMqJ,OAAO,GAAGF,cAAc,IAAIC,cAAc,IAAI,OAAOrE,IAAI,KAAK,QAAQ,IAAIA,IAAI,IAAIpJ,MAAM;AAC9F,MAAM2N,gBAAgB,GAAG,iBAAiB;AAC1C,SAASpC,aAAaA,CAAChC,IAAI,EAAE9G,MAAM,EAAE;EACjC,KAAK,IAAIiD,CAAC,GAAG6D,IAAI,CAAC5D,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,IAAI,OAAO6D,IAAI,CAAC7D,CAAC,CAAC,KAAK,UAAU,EAAE;MAC/B6D,IAAI,CAAC7D,CAAC,CAAC,GAAGwG,mBAAmB,CAAC3C,IAAI,CAAC7D,CAAC,CAAC,EAAEjD,MAAM,GAAG,GAAG,GAAGiD,CAAC,CAAC;IAC5D;EACJ;EACA,OAAO6D,IAAI;AACf;AACA,SAASqE,cAAcA,CAAC3D,SAAS,EAAE4D,OAAO,EAAE;EACxC,MAAMpL,MAAM,GAAGwH,SAAS,CAACpI,WAAW,CAAC,MAAM,CAAC;EAC5C,KAAK,IAAI6D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmI,OAAO,CAAClI,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMvF,IAAI,GAAG0N,OAAO,CAACnI,CAAC,CAAC;IACvB,MAAMI,QAAQ,GAAGmE,SAAS,CAAC9J,IAAI,CAAC;IAChC,IAAI2F,QAAQ,EAAE;MACV,MAAMgI,aAAa,GAAGhC,8BAA8B,CAAC7B,SAAS,EAAE9J,IAAI,CAAC;MACrE,IAAI,CAAC4N,kBAAkB,CAACD,aAAa,CAAC,EAAE;QACpC;MACJ;MACA7D,SAAS,CAAC9J,IAAI,CAAC,GAAG,CAAE2F,QAAQ,IAAK;QAC7B,MAAMkI,OAAO,GAAG,SAAAA,CAAA,EAAY;UACxB,OAAOlI,QAAQ,CAAC0C,KAAK,CAAC,IAAI,EAAE+C,aAAa,CAAC1I,SAAS,EAAEJ,MAAM,GAAG,GAAG,GAAGtC,IAAI,CAAC,CAAC;QAC9E,CAAC;QACDiM,qBAAqB,CAAC4B,OAAO,EAAElI,QAAQ,CAAC;QACxC,OAAOkI,OAAO;MAClB,CAAC,EAAElI,QAAQ,CAAC;IAChB;EACJ;AACJ;AACA,SAASiI,kBAAkBA,CAACE,YAAY,EAAE;EACtC,IAAI,CAACA,YAAY,EAAE;IACf,OAAO,IAAI;EACf;EACA,IAAIA,YAAY,CAACC,QAAQ,KAAK,KAAK,EAAE;IACjC,OAAO,KAAK;EAChB;EACA,OAAO,EAAE,OAAOD,YAAY,CAAC9L,GAAG,KAAK,UAAU,IAAI,OAAO8L,YAAY,CAACE,GAAG,KAAK,WAAW,CAAC;AAC/F;AACA,MAAMC,WAAW,GAAI,OAAOC,iBAAiB,KAAK,WAAW,IAAIjF,IAAI,YAAYiF,iBAAkB;AACnG;AACA;AACA,MAAMC,MAAM,GAAI,EAAE,IAAI,IAAIZ,OAAO,CAAC,IAAI,OAAOA,OAAO,CAACa,OAAO,KAAK,WAAW,IACxE,CAAC,CAAC,CAACzE,QAAQ,CAACR,IAAI,CAACoE,OAAO,CAACa,OAAO,CAAC,KAAK,kBAAmB;AAC7D,MAAMC,SAAS,GAAG,CAACF,MAAM,IAAI,CAACF,WAAW,IAAI,CAAC,EAAEZ,cAAc,IAAIC,cAAc,CAAC,aAAa,CAAC,CAAC;AAChG;AACA;AACA;AACA,MAAMgB,KAAK,GAAG,OAAOf,OAAO,CAACa,OAAO,KAAK,WAAW,IAChD,CAAC,CAAC,CAACzE,QAAQ,CAACR,IAAI,CAACoE,OAAO,CAACa,OAAO,CAAC,KAAK,kBAAkB,IAAI,CAACH,WAAW,IACxE,CAAC,EAAEZ,cAAc,IAAIC,cAAc,CAAC,aAAa,CAAC,CAAC;AACvD,MAAMiB,sBAAsB,GAAG,CAAC,CAAC;AACjC,MAAMC,MAAM,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAC5B;EACA;EACAA,KAAK,GAAGA,KAAK,IAAIlB,OAAO,CAACkB,KAAK;EAC9B,IAAI,CAACA,KAAK,EAAE;IACR;EACJ;EACA,IAAIC,eAAe,GAAGH,sBAAsB,CAACE,KAAK,CAACnL,IAAI,CAAC;EACxD,IAAI,CAACoL,eAAe,EAAE;IAClBA,eAAe,GAAGH,sBAAsB,CAACE,KAAK,CAACnL,IAAI,CAAC,GAAG8J,UAAU,CAAC,aAAa,GAAGqB,KAAK,CAACnL,IAAI,CAAC;EACjG;EACA,MAAMsC,MAAM,GAAG,IAAI,IAAI6I,KAAK,CAAC7I,MAAM,IAAI2H,OAAO;EAC9C,MAAMoB,QAAQ,GAAG/I,MAAM,CAAC8I,eAAe,CAAC;EACxC,IAAIE,MAAM;EACV,IAAIP,SAAS,IAAIzI,MAAM,KAAK0H,cAAc,IAAImB,KAAK,CAACnL,IAAI,KAAK,OAAO,EAAE;IAClE;IACA;IACA;IACA,MAAMuL,UAAU,GAAGJ,KAAK;IACxBG,MAAM,GAAGD,QAAQ,IACbA,QAAQ,CAACxF,IAAI,CAAC,IAAI,EAAE0F,UAAU,CAACC,OAAO,EAAED,UAAU,CAACE,QAAQ,EAAEF,UAAU,CAACG,MAAM,EAAEH,UAAU,CAACI,KAAK,EAAEJ,UAAU,CAAC9L,KAAK,CAAC;IACvH,IAAI6L,MAAM,KAAK,IAAI,EAAE;MACjBH,KAAK,CAACS,cAAc,CAAC,CAAC;IAC1B;EACJ,CAAC,MACI;IACDN,MAAM,GAAGD,QAAQ,IAAIA,QAAQ,CAACtG,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;IACpD,IAAIkM,MAAM,IAAI1K,SAAS,IAAI,CAAC0K,MAAM,EAAE;MAChCH,KAAK,CAACS,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA,OAAON,MAAM;AACjB,CAAC;AACD,SAASO,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAEvF,SAAS,EAAE;EACzC,IAAIwF,IAAI,GAAG3D,8BAA8B,CAACyD,GAAG,EAAEC,IAAI,CAAC;EACpD,IAAI,CAACC,IAAI,IAAIxF,SAAS,EAAE;IACpB;IACA,MAAM6D,aAAa,GAAGhC,8BAA8B,CAAC7B,SAAS,EAAEuF,IAAI,CAAC;IACrE,IAAI1B,aAAa,EAAE;MACf2B,IAAI,GAAG;QAAEC,UAAU,EAAE,IAAI;QAAEC,YAAY,EAAE;MAAK,CAAC;IACnD;EACJ;EACA;EACA;EACA,IAAI,CAACF,IAAI,IAAI,CAACA,IAAI,CAACE,YAAY,EAAE;IAC7B;EACJ;EACA,MAAMC,mBAAmB,GAAGrC,UAAU,CAAC,IAAI,GAAGiC,IAAI,GAAG,SAAS,CAAC;EAC/D,IAAID,GAAG,CAAC/N,cAAc,CAACoO,mBAAmB,CAAC,IAAIL,GAAG,CAACK,mBAAmB,CAAC,EAAE;IACrE;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,OAAOH,IAAI,CAACvB,QAAQ;EACpB,OAAOuB,IAAI,CAAC7G,KAAK;EACjB,MAAMiH,eAAe,GAAGJ,IAAI,CAACtN,GAAG;EAChC,MAAM2N,eAAe,GAAGL,IAAI,CAACtB,GAAG;EAChC;EACA,MAAM4B,SAAS,GAAGP,IAAI,CAAC1C,KAAK,CAAC,CAAC,CAAC;EAC/B,IAAI+B,eAAe,GAAGH,sBAAsB,CAACqB,SAAS,CAAC;EACvD,IAAI,CAAClB,eAAe,EAAE;IAClBA,eAAe,GAAGH,sBAAsB,CAACqB,SAAS,CAAC,GAAGxC,UAAU,CAAC,aAAa,GAAGwC,SAAS,CAAC;EAC/F;EACAN,IAAI,CAACtB,GAAG,GAAG,UAAU6B,QAAQ,EAAE;IAC3B;IACA;IACA,IAAIjK,MAAM,GAAG,IAAI;IACjB,IAAI,CAACA,MAAM,IAAIwJ,GAAG,KAAK7B,OAAO,EAAE;MAC5B3H,MAAM,GAAG2H,OAAO;IACpB;IACA,IAAI,CAAC3H,MAAM,EAAE;MACT;IACJ;IACA,MAAMkK,aAAa,GAAGlK,MAAM,CAAC8I,eAAe,CAAC;IAC7C,IAAI,OAAOoB,aAAa,KAAK,UAAU,EAAE;MACrClK,MAAM,CAACmK,mBAAmB,CAACH,SAAS,EAAEpB,MAAM,CAAC;IACjD;IACA;IACA;IACAmB,eAAe,IAAIA,eAAe,CAACxG,IAAI,CAACvD,MAAM,EAAE,IAAI,CAAC;IACrDA,MAAM,CAAC8I,eAAe,CAAC,GAAGmB,QAAQ;IAClC,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAChCjK,MAAM,CAACoK,gBAAgB,CAACJ,SAAS,EAAEpB,MAAM,EAAE,KAAK,CAAC;IACrD;EACJ,CAAC;EACD;EACA;EACAc,IAAI,CAACtN,GAAG,GAAG,YAAY;IACnB;IACA;IACA,IAAI4D,MAAM,GAAG,IAAI;IACjB,IAAI,CAACA,MAAM,IAAIwJ,GAAG,KAAK7B,OAAO,EAAE;MAC5B3H,MAAM,GAAG2H,OAAO;IACpB;IACA,IAAI,CAAC3H,MAAM,EAAE;MACT,OAAO,IAAI;IACf;IACA,MAAM+I,QAAQ,GAAG/I,MAAM,CAAC8I,eAAe,CAAC;IACxC,IAAIC,QAAQ,EAAE;MACV,OAAOA,QAAQ;IACnB,CAAC,MACI,IAAIe,eAAe,EAAE;MACtB;MACA;MACA;MACA;MACA;MACA;MACA,IAAIjH,KAAK,GAAGiH,eAAe,CAACvG,IAAI,CAAC,IAAI,CAAC;MACtC,IAAIV,KAAK,EAAE;QACP6G,IAAI,CAACtB,GAAG,CAAC7E,IAAI,CAAC,IAAI,EAAEV,KAAK,CAAC;QAC1B,IAAI,OAAO7C,MAAM,CAAC4H,gBAAgB,CAAC,KAAK,UAAU,EAAE;UAChD5H,MAAM,CAACqK,eAAe,CAACZ,IAAI,CAAC;QAChC;QACA,OAAO5G,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDiD,oBAAoB,CAAC0D,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;EACrCF,GAAG,CAACK,mBAAmB,CAAC,GAAG,IAAI;AACnC;AACA,SAASvE,iBAAiBA,CAACkE,GAAG,EAAEvN,UAAU,EAAEiI,SAAS,EAAE;EACnD,IAAIjI,UAAU,EAAE;IACZ,KAAK,IAAI0D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1D,UAAU,CAAC2D,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC4J,aAAa,CAACC,GAAG,EAAE,IAAI,GAAGvN,UAAU,CAAC0D,CAAC,CAAC,EAAEuE,SAAS,CAAC;IACvD;EACJ,CAAC,MACI;IACD,MAAMoG,YAAY,GAAG,EAAE;IACvB,KAAK,MAAMb,IAAI,IAAID,GAAG,EAAE;MACpB,IAAIC,IAAI,CAAC1C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE;QAC1BuD,YAAY,CAAC3H,IAAI,CAAC8G,IAAI,CAAC;MAC3B;IACJ;IACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,CAAC1K,MAAM,EAAE2K,CAAC,EAAE,EAAE;MAC1ChB,aAAa,CAACC,GAAG,EAAEc,YAAY,CAACC,CAAC,CAAC,EAAErG,SAAS,CAAC;IAClD;EACJ;AACJ;AACA,MAAMsG,mBAAmB,GAAGhD,UAAU,CAAC,kBAAkB,CAAC;AAC1D;AACA,SAAStB,UAAUA,CAACuE,SAAS,EAAE;EAC3B,MAAMC,aAAa,GAAG/C,OAAO,CAAC8C,SAAS,CAAC;EACxC,IAAI,CAACC,aAAa,EACd;EACJ;EACA/C,OAAO,CAACH,UAAU,CAACiD,SAAS,CAAC,CAAC,GAAGC,aAAa;EAC9C/C,OAAO,CAAC8C,SAAS,CAAC,GAAG,YAAY;IAC7B,MAAME,CAAC,GAAGnF,aAAa,CAAC1I,SAAS,EAAE2N,SAAS,CAAC;IAC7C,QAAQE,CAAC,CAAC/K,MAAM;MACZ,KAAK,CAAC;QACF,IAAI,CAAC4K,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAAC,CAAC;QAC/C;MACJ,KAAK,CAAC;QACF,IAAI,CAACF,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE;MACJ;QACI,MAAM,IAAIjQ,KAAK,CAAC,oBAAoB,CAAC;IAC7C;EACJ,CAAC;EACD;EACA2L,qBAAqB,CAACsB,OAAO,CAAC8C,SAAS,CAAC,EAAEC,aAAa,CAAC;EACxD,MAAME,QAAQ,GAAG,IAAIF,aAAa,CAAC,YAAY,CAAE,CAAC,CAAC;EACnD,IAAIjB,IAAI;EACR,KAAKA,IAAI,IAAImB,QAAQ,EAAE;IACnB;IACA,IAAIH,SAAS,KAAK,gBAAgB,IAAIhB,IAAI,KAAK,cAAc,EACzD;IACH,WAAUA,IAAI,EAAE;MACb,IAAI,OAAOmB,QAAQ,CAACnB,IAAI,CAAC,KAAK,UAAU,EAAE;QACtC9B,OAAO,CAAC8C,SAAS,CAAC,CAACvG,SAAS,CAACuF,IAAI,CAAC,GAAG,YAAY;UAC7C,OAAO,IAAI,CAACe,mBAAmB,CAAC,CAACf,IAAI,CAAC,CAAChH,KAAK,CAAC,IAAI,CAAC+H,mBAAmB,CAAC,EAAE1N,SAAS,CAAC;QACtF,CAAC;MACL,CAAC,MACI;QACDgJ,oBAAoB,CAAC6B,OAAO,CAAC8C,SAAS,CAAC,CAACvG,SAAS,EAAEuF,IAAI,EAAE;UACrDrB,GAAG,EAAE,SAAAA,CAAU7M,EAAE,EAAE;YACf,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;cAC1B,IAAI,CAACiP,mBAAmB,CAAC,CAACf,IAAI,CAAC,GAAGtD,mBAAmB,CAAC5K,EAAE,EAAEkP,SAAS,GAAG,GAAG,GAAGhB,IAAI,CAAC;cACjF;cACA;cACA;cACApD,qBAAqB,CAAC,IAAI,CAACmE,mBAAmB,CAAC,CAACf,IAAI,CAAC,EAAElO,EAAE,CAAC;YAC9D,CAAC,MACI;cACD,IAAI,CAACiP,mBAAmB,CAAC,CAACf,IAAI,CAAC,GAAGlO,EAAE;YACxC;UACJ,CAAC;UACDa,GAAG,EAAE,SAAAA,CAAA,EAAY;YACb,OAAO,IAAI,CAACoO,mBAAmB,CAAC,CAACf,IAAI,CAAC;UAC1C;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,EAACA,IAAI,CAAC;EACX;EACA,KAAKA,IAAI,IAAIiB,aAAa,EAAE;IACxB,IAAIjB,IAAI,KAAK,WAAW,IAAIiB,aAAa,CAACjP,cAAc,CAACgO,IAAI,CAAC,EAAE;MAC5D9B,OAAO,CAAC8C,SAAS,CAAC,CAAChB,IAAI,CAAC,GAAGiB,aAAa,CAACjB,IAAI,CAAC;IAClD;EACJ;AACJ;AACA,SAASlE,WAAWA,CAACvF,MAAM,EAAE5F,IAAI,EAAEyQ,OAAO,EAAE;EACxC,IAAIC,KAAK,GAAG9K,MAAM;EAClB,OAAO8K,KAAK,IAAI,CAACA,KAAK,CAACrP,cAAc,CAACrB,IAAI,CAAC,EAAE;IACzC0Q,KAAK,GAAGnE,oBAAoB,CAACmE,KAAK,CAAC;EACvC;EACA,IAAI,CAACA,KAAK,IAAI9K,MAAM,CAAC5F,IAAI,CAAC,EAAE;IACxB;IACA0Q,KAAK,GAAG9K,MAAM;EAClB;EACA,MAAM+K,YAAY,GAAGvD,UAAU,CAACpN,IAAI,CAAC;EACrC,IAAI2F,QAAQ,GAAG,IAAI;EACnB,IAAI+K,KAAK,KAAK,EAAE/K,QAAQ,GAAG+K,KAAK,CAACC,YAAY,CAAC,CAAC,IAAI,CAACD,KAAK,CAACrP,cAAc,CAACsP,YAAY,CAAC,CAAC,EAAE;IACrFhL,QAAQ,GAAG+K,KAAK,CAACC,YAAY,CAAC,GAAGD,KAAK,CAAC1Q,IAAI,CAAC;IAC5C;IACA;IACA,MAAMsP,IAAI,GAAGoB,KAAK,IAAI/E,8BAA8B,CAAC+E,KAAK,EAAE1Q,IAAI,CAAC;IACjE,IAAI4N,kBAAkB,CAAC0B,IAAI,CAAC,EAAE;MAC1B,MAAMsB,aAAa,GAAGH,OAAO,CAAC9K,QAAQ,EAAEgL,YAAY,EAAE3Q,IAAI,CAAC;MAC3D0Q,KAAK,CAAC1Q,IAAI,CAAC,GAAG,YAAY;QACtB,OAAO4Q,aAAa,CAAC,IAAI,EAAElO,SAAS,CAAC;MACzC,CAAC;MACDuJ,qBAAqB,CAACyE,KAAK,CAAC1Q,IAAI,CAAC,EAAE2F,QAAQ,CAAC;IAChD;EACJ;EACA,OAAOA,QAAQ;AACnB;AACA;AACA,SAAS2F,cAAcA,CAAC8D,GAAG,EAAEyB,QAAQ,EAAEC,WAAW,EAAE;EAChD,IAAIC,SAAS,GAAG,IAAI;EACpB,SAASzM,YAAYA,CAACpB,IAAI,EAAE;IACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;IACtBA,IAAI,CAACqF,IAAI,CAACrF,IAAI,CAACiN,KAAK,CAAC,GAAG,YAAY;MAChC9N,IAAI,CAACJ,MAAM,CAACuF,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;IACtC,CAAC;IACDqO,SAAS,CAAC1I,KAAK,CAACtE,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,CAACqF,IAAI,CAAC;IACvC,OAAOlG,IAAI;EACf;EACA6N,SAAS,GAAG5F,WAAW,CAACiE,GAAG,EAAEyB,QAAQ,EAAGlL,QAAQ,IAAK,UAAUsD,IAAI,EAAEG,IAAI,EAAE;IACvE,MAAM6H,IAAI,GAAGH,WAAW,CAAC7H,IAAI,EAAEG,IAAI,CAAC;IACpC,IAAI6H,IAAI,CAACD,KAAK,IAAI,CAAC,IAAI,OAAO5H,IAAI,CAAC6H,IAAI,CAACD,KAAK,CAAC,KAAK,UAAU,EAAE;MAC3D,OAAO7D,gCAAgC,CAAC8D,IAAI,CAACjR,IAAI,EAAEoJ,IAAI,CAAC6H,IAAI,CAACD,KAAK,CAAC,EAAEC,IAAI,EAAE3M,YAAY,CAAC;IAC5F,CAAC,MACI;MACD;MACA,OAAOqB,QAAQ,CAAC0C,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;IACrC;EACJ,CAAC,CAAC;AACN;AACA,SAAS6C,qBAAqBA,CAAC4B,OAAO,EAAEqD,QAAQ,EAAE;EAC9CrD,OAAO,CAACT,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAG8D,QAAQ;AACtD;AACA,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,QAAQ,GAAG,KAAK;AACpB,SAASC,IAAIA,CAAA,EAAG;EACZ,IAAI;IACA,MAAMC,EAAE,GAAGhE,cAAc,CAACiE,SAAS,CAACC,SAAS;IAC7C,IAAIF,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7D,OAAO,IAAI;IACf;EACJ,CAAC,CACD,OAAO1O,KAAK,EAAE,CACd;EACA,OAAO,KAAK;AAChB;AACA,SAASyI,UAAUA,CAAA,EAAG;EAClB,IAAI2F,kBAAkB,EAAE;IACpB,OAAOC,QAAQ;EACnB;EACAD,kBAAkB,GAAG,IAAI;EACzB,IAAI;IACA,MAAMG,EAAE,GAAGhE,cAAc,CAACiE,SAAS,CAACC,SAAS;IAC7C,IAAIF,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3FL,QAAQ,GAAG,IAAI;IACnB;EACJ,CAAC,CACD,OAAOrO,KAAK,EAAE,CACd;EACA,OAAOqO,QAAQ;AACnB;AAEA7Q,IAAI,CAACW,YAAY,CAAC,kBAAkB,EAAE,CAACrB,MAAM,EAAEU,IAAI,EAAEmR,GAAG,KAAK;EACzD,MAAM/F,8BAA8B,GAAG9B,MAAM,CAACwC,wBAAwB;EACtE,MAAMX,oBAAoB,GAAG7B,MAAM,CAACyC,cAAc;EAClD,SAASqF,sBAAsBA,CAACvC,GAAG,EAAE;IACjC,IAAIA,GAAG,IAAIA,GAAG,CAACzF,QAAQ,KAAKE,MAAM,CAACC,SAAS,CAACH,QAAQ,EAAE;MACnD,MAAM0G,SAAS,GAAGjB,GAAG,CAAC1N,WAAW,IAAI0N,GAAG,CAAC1N,WAAW,CAAC1B,IAAI;MACzD,OAAO,CAACqQ,SAAS,GAAGA,SAAS,GAAG,EAAE,IAAI,IAAI,GAAGuB,IAAI,CAACC,SAAS,CAACzC,GAAG,CAAC;IACpE;IACA,OAAOA,GAAG,GAAGA,GAAG,CAACzF,QAAQ,CAAC,CAAC,GAAGE,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAACiG,GAAG,CAAC;EACrE;EACA,MAAMhP,UAAU,GAAGsR,GAAG,CAAC7G,MAAM;EAC7B,MAAMiH,sBAAsB,GAAG,EAAE;EACjC,MAAMC,yCAAyC,GAAGlS,MAAM,CAACO,UAAU,CAAC,6CAA6C,CAAC,CAAC,KAAK,IAAI;EAC5H,MAAM6J,aAAa,GAAG7J,UAAU,CAAC,SAAS,CAAC;EAC3C,MAAM8J,UAAU,GAAG9J,UAAU,CAAC,MAAM,CAAC;EACrC,MAAM4R,aAAa,GAAG,mBAAmB;EACzCN,GAAG,CAAC/G,gBAAgB,GAAIsH,CAAC,IAAK;IAC1B,IAAIP,GAAG,CAAC1G,iBAAiB,CAAC,CAAC,EAAE;MACzB,MAAMkH,SAAS,GAAGD,CAAC,IAAIA,CAAC,CAACC,SAAS;MAClC,IAAIA,SAAS,EAAE;QACXC,OAAO,CAACpP,KAAK,CAAC,8BAA8B,EAAEmP,SAAS,YAAY5R,KAAK,GAAG4R,SAAS,CAACpD,OAAO,GAAGoD,SAAS,EAAE,SAAS,EAAED,CAAC,CAACrR,IAAI,CAACZ,IAAI,EAAE,SAAS,EAAEiS,CAAC,CAAC/O,IAAI,IAAI+O,CAAC,CAAC/O,IAAI,CAACZ,MAAM,EAAE,UAAU,EAAE4P,SAAS,EAAEA,SAAS,YAAY5R,KAAK,GAAG4R,SAAS,CAACE,KAAK,GAAGlO,SAAS,CAAC;MAC1P,CAAC,MACI;QACDiO,OAAO,CAACpP,KAAK,CAACkP,CAAC,CAAC;MACpB;IACJ;EACJ,CAAC;EACDP,GAAG,CAAC9G,kBAAkB,GAAG,MAAM;IAC3B,OAAOkH,sBAAsB,CAACtM,MAAM,EAAE;MAClC,MAAM6M,oBAAoB,GAAGP,sBAAsB,CAACQ,KAAK,CAAC,CAAC;MAC3D,IAAI;QACAD,oBAAoB,CAACzR,IAAI,CAAC6B,UAAU,CAAC,MAAM;UACvC,IAAI4P,oBAAoB,CAACE,aAAa,EAAE;YACpC,MAAMF,oBAAoB,CAACH,SAAS;UACxC;UACA,MAAMG,oBAAoB;QAC9B,CAAC,CAAC;MACN,CAAC,CACD,OAAOtP,KAAK,EAAE;QACVyP,wBAAwB,CAACzP,KAAK,CAAC;MACnC;IACJ;EACJ,CAAC;EACD,MAAM0P,0CAA0C,GAAGrS,UAAU,CAAC,kCAAkC,CAAC;EACjG,SAASoS,wBAAwBA,CAACP,CAAC,EAAE;IACjCP,GAAG,CAAC/G,gBAAgB,CAACsH,CAAC,CAAC;IACvB,IAAI;MACA,MAAMS,OAAO,GAAGnS,IAAI,CAACkS,0CAA0C,CAAC;MAChE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;QAC/BA,OAAO,CAACvJ,IAAI,CAAC,IAAI,EAAE8I,CAAC,CAAC;MACzB;IACJ,CAAC,CACD,OAAOrN,GAAG,EAAE,CACZ;EACJ;EACA,SAAS+N,UAAUA,CAAClK,KAAK,EAAE;IACvB,OAAOA,KAAK,IAAIA,KAAK,CAACmK,IAAI;EAC9B;EACA,SAASC,iBAAiBA,CAACpK,KAAK,EAAE;IAC9B,OAAOA,KAAK;EAChB;EACA,SAASqK,gBAAgBA,CAACZ,SAAS,EAAE;IACjC,OAAOa,gBAAgB,CAACC,MAAM,CAACd,SAAS,CAAC;EAC7C;EACA,MAAMe,WAAW,GAAG7S,UAAU,CAAC,OAAO,CAAC;EACvC,MAAM8S,WAAW,GAAG9S,UAAU,CAAC,OAAO,CAAC;EACvC,MAAM+S,aAAa,GAAG/S,UAAU,CAAC,SAAS,CAAC;EAC3C,MAAMgT,wBAAwB,GAAGhT,UAAU,CAAC,oBAAoB,CAAC;EACjE,MAAMiT,wBAAwB,GAAGjT,UAAU,CAAC,oBAAoB,CAAC;EACjE,MAAMkC,MAAM,GAAG,cAAc;EAC7B,MAAMgR,UAAU,GAAG,IAAI;EACvB,MAAMC,QAAQ,GAAG,IAAI;EACrB,MAAMC,QAAQ,GAAG,KAAK;EACtB,MAAMC,iBAAiB,GAAG,CAAC;EAC3B,SAASC,YAAYA,CAACC,OAAO,EAAEvQ,KAAK,EAAE;IAClC,OAAQwQ,CAAC,IAAK;MACV,IAAI;QACAC,cAAc,CAACF,OAAO,EAAEvQ,KAAK,EAAEwQ,CAAC,CAAC;MACrC,CAAC,CACD,OAAOhP,GAAG,EAAE;QACRiP,cAAc,CAACF,OAAO,EAAE,KAAK,EAAE/O,GAAG,CAAC;MACvC;MACA;IACJ,CAAC;EACL;;EACA,MAAMkP,IAAI,GAAG,SAAAA,CAAA,EAAY;IACrB,IAAIC,SAAS,GAAG,KAAK;IACrB,OAAO,SAASC,OAAOA,CAACC,eAAe,EAAE;MACrC,OAAO,YAAY;QACf,IAAIF,SAAS,EAAE;UACX;QACJ;QACAA,SAAS,GAAG,IAAI;QAChBE,eAAe,CAAC5L,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;MAC1C,CAAC;IACL,CAAC;EACL,CAAC;EACD,MAAMwR,UAAU,GAAG,8BAA8B;EACjD,MAAMC,yBAAyB,GAAG/T,UAAU,CAAC,kBAAkB,CAAC;EAChE;EACA,SAASyT,cAAcA,CAACF,OAAO,EAAEvQ,KAAK,EAAEqF,KAAK,EAAE;IAC3C,MAAM2L,WAAW,GAAGN,IAAI,CAAC,CAAC;IAC1B,IAAIH,OAAO,KAAKlL,KAAK,EAAE;MACnB,MAAM,IAAI4L,SAAS,CAACH,UAAU,CAAC;IACnC;IACA,IAAIP,OAAO,CAACV,WAAW,CAAC,KAAKK,UAAU,EAAE;MACrC;MACA,IAAIV,IAAI,GAAG,IAAI;MACf,IAAI;QACA,IAAI,OAAOnK,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;UAC1DmK,IAAI,GAAGnK,KAAK,IAAIA,KAAK,CAACmK,IAAI;QAC9B;MACJ,CAAC,CACD,OAAOhO,GAAG,EAAE;QACRwP,WAAW,CAAC,MAAM;UACdP,cAAc,CAACF,OAAO,EAAE,KAAK,EAAE/O,GAAG,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC;QACJ,OAAO+O,OAAO;MAClB;MACA;MACA,IAAIvQ,KAAK,KAAKoQ,QAAQ,IAAI/K,KAAK,YAAYsK,gBAAgB,IACvDtK,KAAK,CAACpH,cAAc,CAAC4R,WAAW,CAAC,IAAIxK,KAAK,CAACpH,cAAc,CAAC6R,WAAW,CAAC,IACtEzK,KAAK,CAACwK,WAAW,CAAC,KAAKK,UAAU,EAAE;QACnCgB,oBAAoB,CAAC7L,KAAK,CAAC;QAC3BoL,cAAc,CAACF,OAAO,EAAElL,KAAK,CAACwK,WAAW,CAAC,EAAExK,KAAK,CAACyK,WAAW,CAAC,CAAC;MACnE,CAAC,MACI,IAAI9P,KAAK,KAAKoQ,QAAQ,IAAI,OAAOZ,IAAI,KAAK,UAAU,EAAE;QACvD,IAAI;UACAA,IAAI,CAACzJ,IAAI,CAACV,KAAK,EAAE2L,WAAW,CAACV,YAAY,CAACC,OAAO,EAAEvQ,KAAK,CAAC,CAAC,EAAEgR,WAAW,CAACV,YAAY,CAACC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAC1G,CAAC,CACD,OAAO/O,GAAG,EAAE;UACRwP,WAAW,CAAC,MAAM;YACdP,cAAc,CAACF,OAAO,EAAE,KAAK,EAAE/O,GAAG,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC;QACR;MACJ,CAAC,MACI;QACD+O,OAAO,CAACV,WAAW,CAAC,GAAG7P,KAAK;QAC5B,MAAMsH,KAAK,GAAGiJ,OAAO,CAACT,WAAW,CAAC;QAClCS,OAAO,CAACT,WAAW,CAAC,GAAGzK,KAAK;QAC5B,IAAIkL,OAAO,CAACR,aAAa,CAAC,KAAKA,aAAa,EAAE;UAC1C;UACA,IAAI/P,KAAK,KAAKmQ,QAAQ,EAAE;YACpB;YACA;YACAI,OAAO,CAACV,WAAW,CAAC,GAAGU,OAAO,CAACN,wBAAwB,CAAC;YACxDM,OAAO,CAACT,WAAW,CAAC,GAAGS,OAAO,CAACP,wBAAwB,CAAC;UAC5D;QACJ;QACA;QACA;QACA,IAAIhQ,KAAK,KAAKoQ,QAAQ,IAAI/K,KAAK,YAAYnI,KAAK,EAAE;UAC9C;UACA,MAAMiU,KAAK,GAAGhU,IAAI,CAACS,WAAW,IAAIT,IAAI,CAACS,WAAW,CAAC+C,IAAI,IACnDxD,IAAI,CAACS,WAAW,CAAC+C,IAAI,CAACiO,aAAa,CAAC;UACxC,IAAIuC,KAAK,EAAE;YACP;YACA7I,oBAAoB,CAACjD,KAAK,EAAE0L,yBAAyB,EAAE;cAAE3E,YAAY,EAAE,IAAI;cAAED,UAAU,EAAE,KAAK;cAAExB,QAAQ,EAAE,IAAI;cAAEtF,KAAK,EAAE8L;YAAM,CAAC,CAAC;UACnI;QACJ;QACA,KAAK,IAAIhP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,KAAK,CAAClF,MAAM,GAAG;UAC/BgP,uBAAuB,CAACb,OAAO,EAAEjJ,KAAK,CAACnF,CAAC,EAAE,CAAC,EAAEmF,KAAK,CAACnF,CAAC,EAAE,CAAC,EAAEmF,KAAK,CAACnF,CAAC,EAAE,CAAC,EAAEmF,KAAK,CAACnF,CAAC,EAAE,CAAC,CAAC;QACpF;QACA,IAAImF,KAAK,CAAClF,MAAM,IAAI,CAAC,IAAIpC,KAAK,IAAIoQ,QAAQ,EAAE;UACxCG,OAAO,CAACV,WAAW,CAAC,GAAGQ,iBAAiB;UACxC,IAAIpB,oBAAoB,GAAG5J,KAAK;UAChC,IAAI;YACA;YACA;YACA;YACA,MAAM,IAAInI,KAAK,CAAC,yBAAyB,GAAGqR,sBAAsB,CAAClJ,KAAK,CAAC,IACpEA,KAAK,IAAIA,KAAK,CAAC2J,KAAK,GAAG,IAAI,GAAG3J,KAAK,CAAC2J,KAAK,GAAG,EAAE,CAAC,CAAC;UACzD,CAAC,CACD,OAAOxN,GAAG,EAAE;YACRyN,oBAAoB,GAAGzN,GAAG;UAC9B;UACA,IAAImN,yCAAyC,EAAE;YAC3C;YACA;YACAM,oBAAoB,CAACE,aAAa,GAAG,IAAI;UAC7C;UACAF,oBAAoB,CAACH,SAAS,GAAGzJ,KAAK;UACtC4J,oBAAoB,CAACsB,OAAO,GAAGA,OAAO;UACtCtB,oBAAoB,CAACzR,IAAI,GAAGL,IAAI,CAACM,OAAO;UACxCwR,oBAAoB,CAACnP,IAAI,GAAG3C,IAAI,CAACS,WAAW;UAC5C8Q,sBAAsB,CAACvJ,IAAI,CAAC8J,oBAAoB,CAAC;UACjDX,GAAG,CAAC7M,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC7B;MACJ;IACJ;IACA;IACA,OAAO8O,OAAO;EAClB;EACA,MAAMc,yBAAyB,GAAGrU,UAAU,CAAC,yBAAyB,CAAC;EACvE,SAASkU,oBAAoBA,CAACX,OAAO,EAAE;IACnC,IAAIA,OAAO,CAACV,WAAW,CAAC,KAAKQ,iBAAiB,EAAE;MAC5C;MACA;MACA;MACA;MACA;MACA,IAAI;QACA,MAAMf,OAAO,GAAGnS,IAAI,CAACkU,yBAAyB,CAAC;QAC/C,IAAI/B,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;UAC1CA,OAAO,CAACvJ,IAAI,CAAC,IAAI,EAAE;YAAE+I,SAAS,EAAEyB,OAAO,CAACT,WAAW,CAAC;YAAES,OAAO,EAAEA;UAAQ,CAAC,CAAC;QAC7E;MACJ,CAAC,CACD,OAAO/O,GAAG,EAAE,CACZ;MACA+O,OAAO,CAACV,WAAW,CAAC,GAAGO,QAAQ;MAC/B,KAAK,IAAIjO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuM,sBAAsB,CAACtM,MAAM,EAAED,CAAC,EAAE,EAAE;QACpD,IAAIoO,OAAO,KAAK7B,sBAAsB,CAACvM,CAAC,CAAC,CAACoO,OAAO,EAAE;UAC/C7B,sBAAsB,CAAC4C,MAAM,CAACnP,CAAC,EAAE,CAAC,CAAC;QACvC;MACJ;IACJ;EACJ;EACA,SAASiP,uBAAuBA,CAACb,OAAO,EAAE/S,IAAI,EAAE+T,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAE;IACnFP,oBAAoB,CAACX,OAAO,CAAC;IAC7B,MAAMmB,YAAY,GAAGnB,OAAO,CAACV,WAAW,CAAC;IACzC,MAAMtN,QAAQ,GAAGmP,YAAY,GACxB,OAAOF,WAAW,KAAK,UAAU,GAAIA,WAAW,GAAG/B,iBAAiB,GACpE,OAAOgC,UAAU,KAAK,UAAU,GAAIA,UAAU,GAC3C/B,gBAAgB;IACxBlS,IAAI,CAACiE,iBAAiB,CAACvC,MAAM,EAAE,MAAM;MACjC,IAAI;QACA,MAAMyS,kBAAkB,GAAGpB,OAAO,CAACT,WAAW,CAAC;QAC/C,MAAM8B,gBAAgB,GAAG,CAAC,CAACL,YAAY,IAAIxB,aAAa,KAAKwB,YAAY,CAACxB,aAAa,CAAC;QACxF,IAAI6B,gBAAgB,EAAE;UAClB;UACAL,YAAY,CAACvB,wBAAwB,CAAC,GAAG2B,kBAAkB;UAC3DJ,YAAY,CAACtB,wBAAwB,CAAC,GAAGyB,YAAY;QACzD;QACA;QACA,MAAMrM,KAAK,GAAG7H,IAAI,CAAC+B,GAAG,CAACgD,QAAQ,EAAEzB,SAAS,EAAE8Q,gBAAgB,IAAIrP,QAAQ,KAAKmN,gBAAgB,IAAInN,QAAQ,KAAKkN,iBAAiB,GAC3H,EAAE,GACF,CAACkC,kBAAkB,CAAC,CAAC;QACzBlB,cAAc,CAACc,YAAY,EAAE,IAAI,EAAElM,KAAK,CAAC;MAC7C,CAAC,CACD,OAAO1F,KAAK,EAAE;QACV;QACA8Q,cAAc,CAACc,YAAY,EAAE,KAAK,EAAE5R,KAAK,CAAC;MAC9C;IACJ,CAAC,EAAE4R,YAAY,CAAC;EACpB;EACA,MAAMM,4BAA4B,GAAG,+CAA+C;EACpF,MAAMlK,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;EAC5B,MAAMmK,cAAc,GAAGrV,MAAM,CAACqV,cAAc;EAC5C,MAAMnC,gBAAgB,CAAC;IACnB,OAAOpJ,QAAQA,CAAA,EAAG;MACd,OAAOsL,4BAA4B;IACvC;IACA,OAAOzK,OAAOA,CAAC/B,KAAK,EAAE;MAClB,OAAOoL,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAEN,QAAQ,EAAE9K,KAAK,CAAC;IAC1D;IACA,OAAOuK,MAAMA,CAACjQ,KAAK,EAAE;MACjB,OAAO8Q,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAEL,QAAQ,EAAEzQ,KAAK,CAAC;IAC1D;IACA,OAAOoS,GAAGA,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE;QAC1D,OAAOC,OAAO,CAACvC,MAAM,CAAC,IAAIkC,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;MAC/E;MACA,MAAMM,QAAQ,GAAG,EAAE;MACnB,IAAIlQ,KAAK,GAAG,CAAC;MACb,IAAI;QACA,KAAK,IAAIsO,CAAC,IAAIwB,MAAM,EAAE;UAClB9P,KAAK,EAAE;UACPkQ,QAAQ,CAACjN,IAAI,CAACwK,gBAAgB,CAACvI,OAAO,CAACoJ,CAAC,CAAC,CAAC;QAC9C;MACJ,CAAC,CACD,OAAOhP,GAAG,EAAE;QACR,OAAO2Q,OAAO,CAACvC,MAAM,CAAC,IAAIkC,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;MAC/E;MACA,IAAI5P,KAAK,KAAK,CAAC,EAAE;QACb,OAAOiQ,OAAO,CAACvC,MAAM,CAAC,IAAIkC,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;MAC/E;MACA,IAAIO,QAAQ,GAAG,KAAK;MACpB,MAAMC,MAAM,GAAG,EAAE;MACjB,OAAO,IAAI3C,gBAAgB,CAAC,CAACvI,OAAO,EAAEwI,MAAM,KAAK;QAC7C,KAAK,IAAIzN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiQ,QAAQ,CAAChQ,MAAM,EAAED,CAAC,EAAE,EAAE;UACtCiQ,QAAQ,CAACjQ,CAAC,CAAC,CAACqN,IAAI,CAACgB,CAAC,IAAI;YAClB,IAAI6B,QAAQ,EAAE;cACV;YACJ;YACAA,QAAQ,GAAG,IAAI;YACfjL,OAAO,CAACoJ,CAAC,CAAC;UACd,CAAC,EAAEhP,GAAG,IAAI;YACN8Q,MAAM,CAACnN,IAAI,CAAC3D,GAAG,CAAC;YAChBU,KAAK,EAAE;YACP,IAAIA,KAAK,KAAK,CAAC,EAAE;cACbmQ,QAAQ,GAAG,IAAI;cACfzC,MAAM,CAAC,IAAIkC,cAAc,CAACQ,MAAM,EAAE,4BAA4B,CAAC,CAAC;YACpE;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IAEA,OAAOC,IAAIA,CAACP,MAAM,EAAE;MAChB,IAAI5K,OAAO;MACX,IAAIwI,MAAM;MACV,IAAIW,OAAO,GAAG,IAAI,IAAI,CAAC,CAACiC,GAAG,EAAEC,GAAG,KAAK;QACjCrL,OAAO,GAAGoL,GAAG;QACb5C,MAAM,GAAG6C,GAAG;MAChB,CAAC,CAAC;MACF,SAASC,SAASA,CAACrN,KAAK,EAAE;QACtB+B,OAAO,CAAC/B,KAAK,CAAC;MAClB;MACA,SAASsN,QAAQA,CAAChT,KAAK,EAAE;QACrBiQ,MAAM,CAACjQ,KAAK,CAAC;MACjB;MACA,KAAK,IAAI0F,KAAK,IAAI2M,MAAM,EAAE;QACtB,IAAI,CAACzC,UAAU,CAAClK,KAAK,CAAC,EAAE;UACpBA,KAAK,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,KAAK,CAAC;QAC/B;QACAA,KAAK,CAACmK,IAAI,CAACkD,SAAS,EAAEC,QAAQ,CAAC;MACnC;MACA,OAAOpC,OAAO;IAClB;IACA,OAAOqC,GAAGA,CAACZ,MAAM,EAAE;MACf,OAAOrC,gBAAgB,CAACkD,eAAe,CAACb,MAAM,CAAC;IACnD;IACA,OAAOc,UAAUA,CAACd,MAAM,EAAE;MACtB,MAAMe,CAAC,GAAG,IAAI,IAAI,IAAI,CAACrM,SAAS,YAAYiJ,gBAAgB,GAAG,IAAI,GAAGA,gBAAgB;MACtF,OAAOoD,CAAC,CAACF,eAAe,CAACb,MAAM,EAAE;QAC7BgB,YAAY,EAAG3N,KAAK,KAAM;UAAE4N,MAAM,EAAE,WAAW;UAAE5N;QAAM,CAAC,CAAC;QACzD6N,aAAa,EAAG1R,GAAG,KAAM;UAAEyR,MAAM,EAAE,UAAU;UAAEE,MAAM,EAAE3R;QAAI,CAAC;MAChE,CAAC,CAAC;IACN;IACA,OAAOqR,eAAeA,CAACb,MAAM,EAAE/S,QAAQ,EAAE;MACrC,IAAImI,OAAO;MACX,IAAIwI,MAAM;MACV,IAAIW,OAAO,GAAG,IAAI,IAAI,CAAC,CAACiC,GAAG,EAAEC,GAAG,KAAK;QACjCrL,OAAO,GAAGoL,GAAG;QACb5C,MAAM,GAAG6C,GAAG;MAChB,CAAC,CAAC;MACF;MACA,IAAIW,eAAe,GAAG,CAAC;MACvB,IAAIC,UAAU,GAAG,CAAC;MAClB,MAAMC,cAAc,GAAG,EAAE;MACzB,KAAK,IAAIjO,KAAK,IAAI2M,MAAM,EAAE;QACtB,IAAI,CAACzC,UAAU,CAAClK,KAAK,CAAC,EAAE;UACpBA,KAAK,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,KAAK,CAAC;QAC/B;QACA,MAAMkO,aAAa,GAAGF,UAAU;QAChC,IAAI;UACAhO,KAAK,CAACmK,IAAI,CAAEnK,KAAK,IAAK;YAClBiO,cAAc,CAACC,aAAa,CAAC,GAAGtU,QAAQ,GAAGA,QAAQ,CAAC+T,YAAY,CAAC3N,KAAK,CAAC,GAAGA,KAAK;YAC/E+N,eAAe,EAAE;YACjB,IAAIA,eAAe,KAAK,CAAC,EAAE;cACvBhM,OAAO,CAACkM,cAAc,CAAC;YAC3B;UACJ,CAAC,EAAG9R,GAAG,IAAK;YACR,IAAI,CAACvC,QAAQ,EAAE;cACX2Q,MAAM,CAACpO,GAAG,CAAC;YACf,CAAC,MACI;cACD8R,cAAc,CAACC,aAAa,CAAC,GAAGtU,QAAQ,CAACiU,aAAa,CAAC1R,GAAG,CAAC;cAC3D4R,eAAe,EAAE;cACjB,IAAIA,eAAe,KAAK,CAAC,EAAE;gBACvBhM,OAAO,CAACkM,cAAc,CAAC;cAC3B;YACJ;UACJ,CAAC,CAAC;QACN,CAAC,CACD,OAAOE,OAAO,EAAE;UACZ5D,MAAM,CAAC4D,OAAO,CAAC;QACnB;QACAJ,eAAe,EAAE;QACjBC,UAAU,EAAE;MAChB;MACA;MACAD,eAAe,IAAI,CAAC;MACpB,IAAIA,eAAe,KAAK,CAAC,EAAE;QACvBhM,OAAO,CAACkM,cAAc,CAAC;MAC3B;MACA,OAAO/C,OAAO;IAClB;IACAjS,WAAWA,CAACmV,QAAQ,EAAE;MAClB,MAAMlD,OAAO,GAAG,IAAI;MACpB,IAAI,EAAEA,OAAO,YAAYZ,gBAAgB,CAAC,EAAE;QACxC,MAAM,IAAIzS,KAAK,CAAC,gCAAgC,CAAC;MACrD;MACAqT,OAAO,CAACV,WAAW,CAAC,GAAGK,UAAU;MACjCK,OAAO,CAACT,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;MAC3B,IAAI;QACA,MAAMkB,WAAW,GAAGN,IAAI,CAAC,CAAC;QAC1B+C,QAAQ,IACJA,QAAQ,CAACzC,WAAW,CAACV,YAAY,CAACC,OAAO,EAAEJ,QAAQ,CAAC,CAAC,EAAEa,WAAW,CAACV,YAAY,CAACC,OAAO,EAAEH,QAAQ,CAAC,CAAC,CAAC;MAC5G,CAAC,CACD,OAAOzQ,KAAK,EAAE;QACV8Q,cAAc,CAACF,OAAO,EAAE,KAAK,EAAE5Q,KAAK,CAAC;MACzC;IACJ;IACA,KAAKsS,MAAM,CAACyB,WAAW,IAAI;MACvB,OAAO,SAAS;IACpB;IACA,KAAKzB,MAAM,CAAC0B,OAAO,IAAI;MACnB,OAAOhE,gBAAgB;IAC3B;IACAH,IAAIA,CAACgC,WAAW,EAAEC,UAAU,EAAE;MAC1B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAImC,CAAC,GAAG,IAAI,CAACtV,WAAW,GAAG2T,MAAM,CAAC0B,OAAO,CAAC;MAC1C,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;QAC/BA,CAAC,GAAG,IAAI,CAACtV,WAAW,IAAIqR,gBAAgB;MAC5C;MACA,MAAM4B,YAAY,GAAG,IAAIqC,CAAC,CAACjM,IAAI,CAAC;MAChC,MAAMnK,IAAI,GAAGL,IAAI,CAACM,OAAO;MACzB,IAAI,IAAI,CAACoS,WAAW,CAAC,IAAIK,UAAU,EAAE;QACjC,IAAI,CAACJ,WAAW,CAAC,CAAC3K,IAAI,CAAC3H,IAAI,EAAE+T,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC;MACvE,CAAC,MACI;QACDL,uBAAuB,CAAC,IAAI,EAAE5T,IAAI,EAAE+T,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC;MAC9E;MACA,OAAOF,YAAY;IACvB;IACAsC,KAAKA,CAACpC,UAAU,EAAE;MACd,OAAO,IAAI,CAACjC,IAAI,CAAC,IAAI,EAAEiC,UAAU,CAAC;IACtC;IACAqC,OAAOA,CAACC,SAAS,EAAE;MACf;MACA,IAAIH,CAAC,GAAG,IAAI,CAACtV,WAAW,GAAG2T,MAAM,CAAC0B,OAAO,CAAC;MAC1C,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;QAC/BA,CAAC,GAAGjE,gBAAgB;MACxB;MACA,MAAM4B,YAAY,GAAG,IAAIqC,CAAC,CAACjM,IAAI,CAAC;MAChC4J,YAAY,CAACxB,aAAa,CAAC,GAAGA,aAAa;MAC3C,MAAMvS,IAAI,GAAGL,IAAI,CAACM,OAAO;MACzB,IAAI,IAAI,CAACoS,WAAW,CAAC,IAAIK,UAAU,EAAE;QACjC,IAAI,CAACJ,WAAW,CAAC,CAAC3K,IAAI,CAAC3H,IAAI,EAAE+T,YAAY,EAAEwC,SAAS,EAAEA,SAAS,CAAC;MACpE,CAAC,MACI;QACD3C,uBAAuB,CAAC,IAAI,EAAE5T,IAAI,EAAE+T,YAAY,EAAEwC,SAAS,EAAEA,SAAS,CAAC;MAC3E;MACA,OAAOxC,YAAY;IACvB;EACJ;EACA;EACA;EACA5B,gBAAgB,CAAC,SAAS,CAAC,GAAGA,gBAAgB,CAACvI,OAAO;EACtDuI,gBAAgB,CAAC,QAAQ,CAAC,GAAGA,gBAAgB,CAACC,MAAM;EACpDD,gBAAgB,CAAC,MAAM,CAAC,GAAGA,gBAAgB,CAAC4C,IAAI;EAChD5C,gBAAgB,CAAC,KAAK,CAAC,GAAGA,gBAAgB,CAACiD,GAAG;EAC9C,MAAMoB,aAAa,GAAGvX,MAAM,CAACoK,aAAa,CAAC,GAAGpK,MAAM,CAAC,SAAS,CAAC;EAC/DA,MAAM,CAAC,SAAS,CAAC,GAAGkT,gBAAgB;EACpC,MAAMsE,iBAAiB,GAAGjX,UAAU,CAAC,aAAa,CAAC;EACnD,SAASiL,SAASA,CAACiM,IAAI,EAAE;IACrB,MAAM5G,KAAK,GAAG4G,IAAI,CAACxN,SAAS;IAC5B,MAAMuF,IAAI,GAAG1D,8BAA8B,CAAC+E,KAAK,EAAE,MAAM,CAAC;IAC1D,IAAIrB,IAAI,KAAKA,IAAI,CAACtB,QAAQ,KAAK,KAAK,IAAI,CAACsB,IAAI,CAACG,YAAY,CAAC,EAAE;MACzD;MACA;MACA;IACJ;IACA,MAAM+H,YAAY,GAAG7G,KAAK,CAACkC,IAAI;IAC/B;IACAlC,KAAK,CAACxG,UAAU,CAAC,GAAGqN,YAAY;IAChCD,IAAI,CAACxN,SAAS,CAAC8I,IAAI,GAAG,UAAUkD,SAAS,EAAEC,QAAQ,EAAE;MACjD,MAAMyB,OAAO,GAAG,IAAIzE,gBAAgB,CAAC,CAACvI,OAAO,EAAEwI,MAAM,KAAK;QACtDuE,YAAY,CAACpO,IAAI,CAAC,IAAI,EAAEqB,OAAO,EAAEwI,MAAM,CAAC;MAC5C,CAAC,CAAC;MACF,OAAOwE,OAAO,CAAC5E,IAAI,CAACkD,SAAS,EAAEC,QAAQ,CAAC;IAC5C,CAAC;IACDuB,IAAI,CAACD,iBAAiB,CAAC,GAAG,IAAI;EAClC;EACA3F,GAAG,CAACrG,SAAS,GAAGA,SAAS;EACzB,SAASoM,OAAOA,CAACtW,EAAE,EAAE;IACjB,OAAO,UAAU8H,IAAI,EAAEG,IAAI,EAAE;MACzB,IAAIsO,aAAa,GAAGvW,EAAE,CAACkH,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;MACxC,IAAIsO,aAAa,YAAY3E,gBAAgB,EAAE;QAC3C,OAAO2E,aAAa;MACxB;MACA,IAAIC,IAAI,GAAGD,aAAa,CAAChW,WAAW;MACpC,IAAI,CAACiW,IAAI,CAACN,iBAAiB,CAAC,EAAE;QAC1BhM,SAAS,CAACsM,IAAI,CAAC;MACnB;MACA,OAAOD,aAAa;IACxB,CAAC;EACL;EACA,IAAIN,aAAa,EAAE;IACf/L,SAAS,CAAC+L,aAAa,CAAC;IACxBjM,WAAW,CAACtL,MAAM,EAAE,OAAO,EAAE8F,QAAQ,IAAI8R,OAAO,CAAC9R,QAAQ,CAAC,CAAC;EAC/D;EACA;EACA4P,OAAO,CAAChV,IAAI,CAACH,UAAU,CAAC,uBAAuB,CAAC,CAAC,GAAG0R,sBAAsB;EAC1E,OAAOiB,gBAAgB;AAC3B,CAAC,CAAC;;AAEF;AACA;AACAxS,IAAI,CAACW,YAAY,CAAC,UAAU,EAAGrB,MAAM,IAAK;EACtC;EACA,MAAM+X,wBAAwB,GAAGC,QAAQ,CAAC/N,SAAS,CAACH,QAAQ;EAC5D,MAAMmO,wBAAwB,GAAG1K,UAAU,CAAC,kBAAkB,CAAC;EAC/D,MAAM2K,cAAc,GAAG3K,UAAU,CAAC,SAAS,CAAC;EAC5C,MAAM4K,YAAY,GAAG5K,UAAU,CAAC,OAAO,CAAC;EACxC,MAAM6K,mBAAmB,GAAG,SAAStO,QAAQA,CAAA,EAAG;IAC5C,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;MAC5B,MAAMuO,gBAAgB,GAAG,IAAI,CAACJ,wBAAwB,CAAC;MACvD,IAAII,gBAAgB,EAAE;QAClB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;UACxC,OAAON,wBAAwB,CAACzO,IAAI,CAAC+O,gBAAgB,CAAC;QAC1D,CAAC,MACI;UACD,OAAOrO,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAAC+O,gBAAgB,CAAC;QAC3D;MACJ;MACA,IAAI,IAAI,KAAK3C,OAAO,EAAE;QAClB,MAAM4C,aAAa,GAAGtY,MAAM,CAACkY,cAAc,CAAC;QAC5C,IAAII,aAAa,EAAE;UACf,OAAOP,wBAAwB,CAACzO,IAAI,CAACgP,aAAa,CAAC;QACvD;MACJ;MACA,IAAI,IAAI,KAAK7X,KAAK,EAAE;QAChB,MAAM8X,WAAW,GAAGvY,MAAM,CAACmY,YAAY,CAAC;QACxC,IAAII,WAAW,EAAE;UACb,OAAOR,wBAAwB,CAACzO,IAAI,CAACiP,WAAW,CAAC;QACrD;MACJ;IACJ;IACA,OAAOR,wBAAwB,CAACzO,IAAI,CAAC,IAAI,CAAC;EAC9C,CAAC;EACD8O,mBAAmB,CAACH,wBAAwB,CAAC,GAAGF,wBAAwB;EACxEC,QAAQ,CAAC/N,SAAS,CAACH,QAAQ,GAAGsO,mBAAmB;EACjD;EACA,MAAMI,sBAAsB,GAAGxO,MAAM,CAACC,SAAS,CAACH,QAAQ;EACxD,MAAM2O,wBAAwB,GAAG,kBAAkB;EACnDzO,MAAM,CAACC,SAAS,CAACH,QAAQ,GAAG,YAAY;IACpC,IAAI,OAAO4L,OAAO,KAAK,UAAU,IAAI,IAAI,YAAYA,OAAO,EAAE;MAC1D,OAAO+C,wBAAwB;IACnC;IACA,OAAOD,sBAAsB,CAAClP,IAAI,CAAC,IAAI,CAAC;EAC5C,CAAC;AACL,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,IAAIoP,gBAAgB,GAAG,KAAK;AAC5B,IAAI,OAAOnM,MAAM,KAAK,WAAW,EAAE;EAC/B,IAAI;IACA,MAAMrD,OAAO,GAAGc,MAAM,CAACyC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;MACjDtK,GAAG,EAAE,SAAAA,CAAA,EAAY;QACbuW,gBAAgB,GAAG,IAAI;MAC3B;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACAnM,MAAM,CAAC4D,gBAAgB,CAAC,MAAM,EAAEjH,OAAO,EAAEA,OAAO,CAAC;IACjDqD,MAAM,CAAC2D,mBAAmB,CAAC,MAAM,EAAEhH,OAAO,EAAEA,OAAO,CAAC;EACxD,CAAC,CACD,OAAOnE,GAAG,EAAE;IACR2T,gBAAgB,GAAG,KAAK;EAC5B;AACJ;AACA;AACA,MAAMC,8BAA8B,GAAG;EACnCtP,IAAI,EAAE;AACV,CAAC;AACD,MAAMuP,oBAAoB,GAAG,CAAC,CAAC;AAC/B,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,MAAMC,sBAAsB,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAG1L,kBAAkB,GAAG,qBAAqB,CAAC;AAC3F,MAAM2L,4BAA4B,GAAGzL,UAAU,CAAC,oBAAoB,CAAC;AACrE,SAAS0L,iBAAiBA,CAAClJ,SAAS,EAAEmJ,iBAAiB,EAAE;EACrD,MAAMC,cAAc,GAAG,CAACD,iBAAiB,GAAGA,iBAAiB,CAACnJ,SAAS,CAAC,GAAGA,SAAS,IAAI3C,SAAS;EACjG,MAAMgM,aAAa,GAAG,CAACF,iBAAiB,GAAGA,iBAAiB,CAACnJ,SAAS,CAAC,GAAGA,SAAS,IAAI5C,QAAQ;EAC/F,MAAMnC,MAAM,GAAGqC,kBAAkB,GAAG8L,cAAc;EAClD,MAAME,aAAa,GAAGhM,kBAAkB,GAAG+L,aAAa;EACxDR,oBAAoB,CAAC7I,SAAS,CAAC,GAAG,CAAC,CAAC;EACpC6I,oBAAoB,CAAC7I,SAAS,CAAC,CAAC3C,SAAS,CAAC,GAAGpC,MAAM;EACnD4N,oBAAoB,CAAC7I,SAAS,CAAC,CAAC5C,QAAQ,CAAC,GAAGkM,aAAa;AAC7D;AACA,SAASjO,gBAAgBA,CAACsC,OAAO,EAAEmE,GAAG,EAAEyH,IAAI,EAAEC,YAAY,EAAE;EACxD,MAAMC,kBAAkB,GAAID,YAAY,IAAIA,YAAY,CAACE,GAAG,IAAK1M,sBAAsB;EACvF,MAAM2M,qBAAqB,GAAIH,YAAY,IAAIA,YAAY,CAACI,EAAE,IAAK3M,yBAAyB;EAC5F,MAAM4M,wBAAwB,GAAIL,YAAY,IAAIA,YAAY,CAACM,SAAS,IAAK,gBAAgB;EAC7F,MAAMC,mCAAmC,GAAIP,YAAY,IAAIA,YAAY,CAACQ,KAAK,IAAK,oBAAoB;EACxG,MAAMC,0BAA0B,GAAGzM,UAAU,CAACiM,kBAAkB,CAAC;EACjE,MAAMS,yBAAyB,GAAG,GAAG,GAAGT,kBAAkB,GAAG,GAAG;EAChE,MAAMU,sBAAsB,GAAG,iBAAiB;EAChD,MAAMC,6BAA6B,GAAG,GAAG,GAAGD,sBAAsB,GAAG,GAAG;EACxE,MAAM5V,UAAU,GAAG,SAAAA,CAAUjB,IAAI,EAAE0C,MAAM,EAAE6I,KAAK,EAAE;IAC9C;IACA;IACA,IAAIvL,IAAI,CAAC+W,SAAS,EAAE;MAChB;IACJ;IACA,MAAMtU,QAAQ,GAAGzC,IAAI,CAACb,QAAQ;IAC9B,IAAI,OAAOsD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACuU,WAAW,EAAE;MACtD;MACAhX,IAAI,CAACb,QAAQ,GAAIoM,KAAK,IAAK9I,QAAQ,CAACuU,WAAW,CAACzL,KAAK,CAAC;MACtDvL,IAAI,CAACgV,gBAAgB,GAAGvS,QAAQ;IACpC;IACA;IACA;IACA;IACA;IACA,IAAI5C,KAAK;IACT,IAAI;MACAG,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE0C,MAAM,EAAE,CAAC6I,KAAK,CAAC,CAAC;IACtC,CAAC,CACD,OAAO7J,GAAG,EAAE;MACR7B,KAAK,GAAG6B,GAAG;IACf;IACA,MAAMmE,OAAO,GAAG7F,IAAI,CAAC6F,OAAO;IAC5B,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAAC+K,IAAI,EAAE;MACxD;MACA;MACA;MACA,MAAMnO,QAAQ,GAAGzC,IAAI,CAACgV,gBAAgB,GAAGhV,IAAI,CAACgV,gBAAgB,GAAGhV,IAAI,CAACb,QAAQ;MAC9EuD,MAAM,CAAC2T,qBAAqB,CAAC,CAACpQ,IAAI,CAACvD,MAAM,EAAE6I,KAAK,CAACnL,IAAI,EAAEqC,QAAQ,EAAEoD,OAAO,CAAC;IAC7E;IACA,OAAOhG,KAAK;EAChB,CAAC;EACD,SAASoX,cAAcA,CAACC,OAAO,EAAE3L,KAAK,EAAE4L,SAAS,EAAE;IAC/C;IACA;IACA5L,KAAK,GAAGA,KAAK,IAAIlB,OAAO,CAACkB,KAAK;IAC9B,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA;IACA;IACA,MAAM7I,MAAM,GAAGwU,OAAO,IAAI3L,KAAK,CAAC7I,MAAM,IAAI2H,OAAO;IACjD,MAAM+M,KAAK,GAAG1U,MAAM,CAAC6S,oBAAoB,CAAChK,KAAK,CAACnL,IAAI,CAAC,CAAC+W,SAAS,GAAGrN,QAAQ,GAAGC,SAAS,CAAC,CAAC;IACxF,IAAIqN,KAAK,EAAE;MACP,MAAM5E,MAAM,GAAG,EAAE;MACjB;MACA;MACA,IAAI4E,KAAK,CAAC9U,MAAM,KAAK,CAAC,EAAE;QACpB,MAAMZ,GAAG,GAAGT,UAAU,CAACmW,KAAK,CAAC,CAAC,CAAC,EAAE1U,MAAM,EAAE6I,KAAK,CAAC;QAC/C7J,GAAG,IAAI8Q,MAAM,CAACnN,IAAI,CAAC3D,GAAG,CAAC;MAC3B,CAAC,MACI;QACD;QACA;QACA;QACA,MAAM2V,SAAS,GAAGD,KAAK,CAAC3N,KAAK,CAAC,CAAC;QAC/B,KAAK,IAAIpH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgV,SAAS,CAAC/U,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC,IAAIkJ,KAAK,IAAIA,KAAK,CAACoK,4BAA4B,CAAC,KAAK,IAAI,EAAE;YACvD;UACJ;UACA,MAAMjU,GAAG,GAAGT,UAAU,CAACoW,SAAS,CAAChV,CAAC,CAAC,EAAEK,MAAM,EAAE6I,KAAK,CAAC;UACnD7J,GAAG,IAAI8Q,MAAM,CAACnN,IAAI,CAAC3D,GAAG,CAAC;QAC3B;MACJ;MACA;MACA;MACA,IAAI8Q,MAAM,CAAClQ,MAAM,KAAK,CAAC,EAAE;QACrB,MAAMkQ,MAAM,CAAC,CAAC,CAAC;MACnB,CAAC,MACI;QACD,KAAK,IAAInQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmQ,MAAM,CAAClQ,MAAM,EAAED,CAAC,EAAE,EAAE;UACpC,MAAMX,GAAG,GAAG8Q,MAAM,CAACnQ,CAAC,CAAC;UACrBmM,GAAG,CAACpH,uBAAuB,CAAC,MAAM;YAC9B,MAAM1F,GAAG;UACb,CAAC,CAAC;QACN;MACJ;IACJ;EACJ;EACA;EACA,MAAM4V,uBAAuB,GAAG,SAAAA,CAAU/L,KAAK,EAAE;IAC7C,OAAO0L,cAAc,CAAC,IAAI,EAAE1L,KAAK,EAAE,KAAK,CAAC;EAC7C,CAAC;EACD;EACA,MAAMgM,8BAA8B,GAAG,SAAAA,CAAUhM,KAAK,EAAE;IACpD,OAAO0L,cAAc,CAAC,IAAI,EAAE1L,KAAK,EAAE,IAAI,CAAC;EAC5C,CAAC;EACD,SAASiM,uBAAuBA,CAACtL,GAAG,EAAEgK,YAAY,EAAE;IAChD,IAAI,CAAChK,GAAG,EAAE;MACN,OAAO,KAAK;IAChB;IACA,IAAIuL,iBAAiB,GAAG,IAAI;IAC5B,IAAIvB,YAAY,IAAIA,YAAY,CAAClQ,IAAI,KAAKhF,SAAS,EAAE;MACjDyW,iBAAiB,GAAGvB,YAAY,CAAClQ,IAAI;IACzC;IACA,MAAM0R,eAAe,GAAGxB,YAAY,IAAIA,YAAY,CAACyB,EAAE;IACvD,IAAIxa,cAAc,GAAG,IAAI;IACzB,IAAI+Y,YAAY,IAAIA,YAAY,CAAC0B,MAAM,KAAK5W,SAAS,EAAE;MACnD7D,cAAc,GAAG+Y,YAAY,CAAC0B,MAAM;IACxC;IACA,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAI3B,YAAY,IAAIA,YAAY,CAAC4B,EAAE,KAAK9W,SAAS,EAAE;MAC/C6W,YAAY,GAAG3B,YAAY,CAAC4B,EAAE;IAClC;IACA,IAAItK,KAAK,GAAGtB,GAAG;IACf,OAAOsB,KAAK,IAAI,CAACA,KAAK,CAACrP,cAAc,CAACgY,kBAAkB,CAAC,EAAE;MACvD3I,KAAK,GAAGnE,oBAAoB,CAACmE,KAAK,CAAC;IACvC;IACA,IAAI,CAACA,KAAK,IAAItB,GAAG,CAACiK,kBAAkB,CAAC,EAAE;MACnC;MACA3I,KAAK,GAAGtB,GAAG;IACf;IACA,IAAI,CAACsB,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;IACA,IAAIA,KAAK,CAACmJ,0BAA0B,CAAC,EAAE;MACnC,OAAO,KAAK;IAChB;IACA,MAAMd,iBAAiB,GAAGK,YAAY,IAAIA,YAAY,CAACL,iBAAiB;IACxE;IACA;IACA,MAAMkC,QAAQ,GAAG,CAAC,CAAC;IACnB,MAAMC,sBAAsB,GAAGxK,KAAK,CAACmJ,0BAA0B,CAAC,GAAGnJ,KAAK,CAAC2I,kBAAkB,CAAC;IAC5F,MAAM8B,yBAAyB,GAAGzK,KAAK,CAACtD,UAAU,CAACmM,qBAAqB,CAAC,CAAC,GACtE7I,KAAK,CAAC6I,qBAAqB,CAAC;IAChC,MAAM6B,eAAe,GAAG1K,KAAK,CAACtD,UAAU,CAACqM,wBAAwB,CAAC,CAAC,GAC/D/I,KAAK,CAAC+I,wBAAwB,CAAC;IACnC,MAAM4B,wBAAwB,GAAG3K,KAAK,CAACtD,UAAU,CAACuM,mCAAmC,CAAC,CAAC,GACnFjJ,KAAK,CAACiJ,mCAAmC,CAAC;IAC9C,IAAI2B,0BAA0B;IAC9B,IAAIlC,YAAY,IAAIA,YAAY,CAACmC,OAAO,EAAE;MACtCD,0BAA0B,GAAG5K,KAAK,CAACtD,UAAU,CAACgM,YAAY,CAACmC,OAAO,CAAC,CAAC,GAChE7K,KAAK,CAAC0I,YAAY,CAACmC,OAAO,CAAC;IACnC;IACA;AACR;AACA;AACA;IACQ,SAASC,yBAAyBA,CAACzS,OAAO,EAAE0S,OAAO,EAAE;MACjD,IAAI,CAAClD,gBAAgB,IAAI,OAAOxP,OAAO,KAAK,QAAQ,IAAIA,OAAO,EAAE;QAC7D;QACA;QACA;QACA,OAAO,CAAC,CAACA,OAAO,CAAC2S,OAAO;MAC5B;MACA,IAAI,CAACnD,gBAAgB,IAAI,CAACkD,OAAO,EAAE;QAC/B,OAAO1S,OAAO;MAClB;MACA,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;QAC9B,OAAO;UAAE2S,OAAO,EAAE3S,OAAO;UAAE0S,OAAO,EAAE;QAAK,CAAC;MAC9C;MACA,IAAI,CAAC1S,OAAO,EAAE;QACV,OAAO;UAAE0S,OAAO,EAAE;QAAK,CAAC;MAC5B;MACA,IAAI,OAAO1S,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAAC0S,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO;UAAE,GAAG1S,OAAO;UAAE0S,OAAO,EAAE;QAAK,CAAC;MACxC;MACA,OAAO1S,OAAO;IAClB;IACA,MAAM4S,oBAAoB,GAAG,SAAAA,CAAUzY,IAAI,EAAE;MACzC;MACA;MACA,IAAI+X,QAAQ,CAACW,UAAU,EAAE;QACrB;MACJ;MACA,OAAOV,sBAAsB,CAAC/R,IAAI,CAAC8R,QAAQ,CAACrV,MAAM,EAAEqV,QAAQ,CAACrL,SAAS,EAAEqL,QAAQ,CAACS,OAAO,GAAGjB,8BAA8B,GAAGD,uBAAuB,EAAES,QAAQ,CAAClS,OAAO,CAAC;IAC1K,CAAC;IACD,MAAM8S,kBAAkB,GAAG,SAAAA,CAAU3Y,IAAI,EAAE;MACvC;MACA;MACA;MACA,IAAI,CAACA,IAAI,CAAC+W,SAAS,EAAE;QACjB,MAAM6B,gBAAgB,GAAGrD,oBAAoB,CAACvV,IAAI,CAAC0M,SAAS,CAAC;QAC7D,IAAImM,eAAe;QACnB,IAAID,gBAAgB,EAAE;UAClBC,eAAe,GAAGD,gBAAgB,CAAC5Y,IAAI,CAACwY,OAAO,GAAG1O,QAAQ,GAAGC,SAAS,CAAC;QAC3E;QACA,MAAM+O,aAAa,GAAGD,eAAe,IAAI7Y,IAAI,CAAC0C,MAAM,CAACmW,eAAe,CAAC;QACrE,IAAIC,aAAa,EAAE;UACf,KAAK,IAAIzW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyW,aAAa,CAACxW,MAAM,EAAED,CAAC,EAAE,EAAE;YAC3C,MAAM0W,YAAY,GAAGD,aAAa,CAACzW,CAAC,CAAC;YACrC,IAAI0W,YAAY,KAAK/Y,IAAI,EAAE;cACvB8Y,aAAa,CAACtH,MAAM,CAACnP,CAAC,EAAE,CAAC,CAAC;cAC1B;cACArC,IAAI,CAAC+W,SAAS,GAAG,IAAI;cACrB,IAAI+B,aAAa,CAACxW,MAAM,KAAK,CAAC,EAAE;gBAC5B;gBACA;gBACAtC,IAAI,CAACgZ,UAAU,GAAG,IAAI;gBACtBhZ,IAAI,CAAC0C,MAAM,CAACmW,eAAe,CAAC,GAAG,IAAI;cACvC;cACA;YACJ;UACJ;QACJ;MACJ;MACA;MACA;MACA;MACA,IAAI,CAAC7Y,IAAI,CAACgZ,UAAU,EAAE;QAClB;MACJ;MACA,OAAOf,yBAAyB,CAAChS,IAAI,CAACjG,IAAI,CAAC0C,MAAM,EAAE1C,IAAI,CAAC0M,SAAS,EAAE1M,IAAI,CAACwY,OAAO,GAAGjB,8BAA8B,GAAGD,uBAAuB,EAAEtX,IAAI,CAAC6F,OAAO,CAAC;IAC7J,CAAC;IACD,MAAMoT,uBAAuB,GAAG,SAAAA,CAAUjZ,IAAI,EAAE;MAC5C,OAAOgY,sBAAsB,CAAC/R,IAAI,CAAC8R,QAAQ,CAACrV,MAAM,EAAEqV,QAAQ,CAACrL,SAAS,EAAE1M,IAAI,CAACJ,MAAM,EAAEmY,QAAQ,CAAClS,OAAO,CAAC;IAC1G,CAAC;IACD,MAAMqT,qBAAqB,GAAG,SAAAA,CAAUlZ,IAAI,EAAE;MAC1C,OAAOoY,0BAA0B,CAACnS,IAAI,CAAC8R,QAAQ,CAACrV,MAAM,EAAEqV,QAAQ,CAACrL,SAAS,EAAE1M,IAAI,CAACJ,MAAM,EAAEmY,QAAQ,CAAClS,OAAO,CAAC;IAC9G,CAAC;IACD,MAAMsT,qBAAqB,GAAG,SAAAA,CAAUnZ,IAAI,EAAE;MAC1C,OAAOiY,yBAAyB,CAAChS,IAAI,CAACjG,IAAI,CAAC0C,MAAM,EAAE1C,IAAI,CAAC0M,SAAS,EAAE1M,IAAI,CAACJ,MAAM,EAAEI,IAAI,CAAC6F,OAAO,CAAC;IACjG,CAAC;IACD,MAAMjE,cAAc,GAAG6V,iBAAiB,GAAGgB,oBAAoB,GAAGQ,uBAAuB;IACzF,MAAMjX,YAAY,GAAGyV,iBAAiB,GAAGkB,kBAAkB,GAAGQ,qBAAqB;IACnF,MAAMC,6BAA6B,GAAG,SAAAA,CAAUpZ,IAAI,EAAEyC,QAAQ,EAAE;MAC5D,MAAM4W,cAAc,GAAG,OAAO5W,QAAQ;MACtC,OAAQ4W,cAAc,KAAK,UAAU,IAAIrZ,IAAI,CAACb,QAAQ,KAAKsD,QAAQ,IAC9D4W,cAAc,KAAK,QAAQ,IAAIrZ,IAAI,CAACgV,gBAAgB,KAAKvS,QAAS;IAC3E,CAAC;IACD,MAAM6W,OAAO,GAAIpD,YAAY,IAAIA,YAAY,CAACqD,IAAI,GAAIrD,YAAY,CAACqD,IAAI,GAAGH,6BAA6B;IACvG,MAAMI,eAAe,GAAGnc,IAAI,CAAC6M,UAAU,CAAC,kBAAkB,CAAC,CAAC;IAC5D,MAAMuP,aAAa,GAAGpP,OAAO,CAACH,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC3D,MAAMwP,eAAe,GAAG,SAAAA,CAAUC,cAAc,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,cAAc,EAAEjC,YAAY,GAAG,KAAK,EAAEQ,OAAO,GAAG,KAAK,EAAE;MAClI,OAAO,YAAY;QACf,MAAM3V,MAAM,GAAG,IAAI,IAAI2H,OAAO;QAC9B,IAAIqC,SAAS,GAAGlN,SAAS,CAAC,CAAC,CAAC;QAC5B,IAAI0W,YAAY,IAAIA,YAAY,CAAC6D,iBAAiB,EAAE;UAChDrN,SAAS,GAAGwJ,YAAY,CAAC6D,iBAAiB,CAACrN,SAAS,CAAC;QACzD;QACA,IAAIjK,QAAQ,GAAGjD,SAAS,CAAC,CAAC,CAAC;QAC3B,IAAI,CAACiD,QAAQ,EAAE;UACX,OAAOkX,cAAc,CAACxU,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;QAChD;QACA,IAAIyL,MAAM,IAAIyB,SAAS,KAAK,mBAAmB,EAAE;UAC7C;UACA,OAAOiN,cAAc,CAACxU,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;QAChD;QACA;QACA;QACA;QACA,IAAIwa,aAAa,GAAG,KAAK;QACzB,IAAI,OAAOvX,QAAQ,KAAK,UAAU,EAAE;UAChC,IAAI,CAACA,QAAQ,CAACuU,WAAW,EAAE;YACvB,OAAO2C,cAAc,CAACxU,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;UAChD;UACAwa,aAAa,GAAG,IAAI;QACxB;QACA,IAAItC,eAAe,IAAI,CAACA,eAAe,CAACiC,cAAc,EAAElX,QAAQ,EAAEC,MAAM,EAAElD,SAAS,CAAC,EAAE;UAClF;QACJ;QACA,MAAM+Y,OAAO,GAAGlD,gBAAgB,IAAI,CAAC,CAACoE,aAAa,IAAIA,aAAa,CAAClL,OAAO,CAAC7B,SAAS,CAAC,KAAK,CAAC,CAAC;QAC9F,MAAM7G,OAAO,GAAGyS,yBAAyB,CAAC9Y,SAAS,CAAC,CAAC,CAAC,EAAE+Y,OAAO,CAAC;QAChE,IAAIiB,eAAe,EAAE;UACjB;UACA,KAAK,IAAInX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmX,eAAe,CAAClX,MAAM,EAAED,CAAC,EAAE,EAAE;YAC7C,IAAIqK,SAAS,KAAK8M,eAAe,CAACnX,CAAC,CAAC,EAAE;cAClC,IAAIkW,OAAO,EAAE;gBACT,OAAOoB,cAAc,CAAC1T,IAAI,CAACvD,MAAM,EAAEgK,SAAS,EAAEjK,QAAQ,EAAEoD,OAAO,CAAC;cACpE,CAAC,MACI;gBACD,OAAO8T,cAAc,CAACxU,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;cAChD;YACJ;UACJ;QACJ;QACA,MAAMgZ,OAAO,GAAG,CAAC3S,OAAO,GAAG,KAAK,GAAG,OAAOA,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO,CAAC2S,OAAO;QACxF,MAAM5H,IAAI,GAAG/K,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,GAAGA,OAAO,CAAC+K,IAAI,GAAG,KAAK;QAC1E,MAAMlT,IAAI,GAAGL,IAAI,CAACM,OAAO;QACzB,IAAIib,gBAAgB,GAAGrD,oBAAoB,CAAC7I,SAAS,CAAC;QACtD,IAAI,CAACkM,gBAAgB,EAAE;UACnBhD,iBAAiB,CAAClJ,SAAS,EAAEmJ,iBAAiB,CAAC;UAC/C+C,gBAAgB,GAAGrD,oBAAoB,CAAC7I,SAAS,CAAC;QACtD;QACA,MAAMmM,eAAe,GAAGD,gBAAgB,CAACJ,OAAO,GAAG1O,QAAQ,GAAGC,SAAS,CAAC;QACxE,IAAI+O,aAAa,GAAGpW,MAAM,CAACmW,eAAe,CAAC;QAC3C,IAAIH,UAAU,GAAG,KAAK;QACtB,IAAII,aAAa,EAAE;UACf;UACAJ,UAAU,GAAG,IAAI;UACjB,IAAIvb,cAAc,EAAE;YAChB,KAAK,IAAIkF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyW,aAAa,CAACxW,MAAM,EAAED,CAAC,EAAE,EAAE;cAC3C,IAAIiX,OAAO,CAACR,aAAa,CAACzW,CAAC,CAAC,EAAEI,QAAQ,CAAC,EAAE;gBACrC;gBACA;cACJ;YACJ;UACJ;QACJ,CAAC,MACI;UACDqW,aAAa,GAAGpW,MAAM,CAACmW,eAAe,CAAC,GAAG,EAAE;QAChD;QACA,IAAIzZ,MAAM;QACV,MAAM6a,eAAe,GAAGvX,MAAM,CAAClE,WAAW,CAAC,MAAM,CAAC;QAClD,MAAM0b,YAAY,GAAG1E,aAAa,CAACyE,eAAe,CAAC;QACnD,IAAIC,YAAY,EAAE;UACd9a,MAAM,GAAG8a,YAAY,CAACxN,SAAS,CAAC;QACpC;QACA,IAAI,CAACtN,MAAM,EAAE;UACTA,MAAM,GAAG6a,eAAe,GAAGL,SAAS,IAC/B/D,iBAAiB,GAAGA,iBAAiB,CAACnJ,SAAS,CAAC,GAAGA,SAAS,CAAC;QACtE;QACA;QACA;QACAqL,QAAQ,CAAClS,OAAO,GAAGA,OAAO;QAC1B,IAAI+K,IAAI,EAAE;UACN;UACA;UACA;UACAmH,QAAQ,CAAClS,OAAO,CAAC+K,IAAI,GAAG,KAAK;QACjC;QACAmH,QAAQ,CAACrV,MAAM,GAAGA,MAAM;QACxBqV,QAAQ,CAACS,OAAO,GAAGA,OAAO;QAC1BT,QAAQ,CAACrL,SAAS,GAAGA,SAAS;QAC9BqL,QAAQ,CAACW,UAAU,GAAGA,UAAU;QAChC,MAAM7X,IAAI,GAAG4W,iBAAiB,GAAGnC,8BAA8B,GAAGtU,SAAS;QAC3E;QACA,IAAIH,IAAI,EAAE;UACNA,IAAI,CAACkX,QAAQ,GAAGA,QAAQ;QAC5B;QACA,MAAM/X,IAAI,GAAGtC,IAAI,CAACuE,iBAAiB,CAAC7C,MAAM,EAAEqD,QAAQ,EAAE5B,IAAI,EAAEgZ,gBAAgB,EAAEC,cAAc,CAAC;QAC7F;QACA;QACA/B,QAAQ,CAACrV,MAAM,GAAG,IAAI;QACtB;QACA,IAAI7B,IAAI,EAAE;UACNA,IAAI,CAACkX,QAAQ,GAAG,IAAI;QACxB;QACA;QACA;QACA,IAAInH,IAAI,EAAE;UACN/K,OAAO,CAAC+K,IAAI,GAAG,IAAI;QACvB;QACA,IAAI,EAAE,CAACyE,gBAAgB,IAAI,OAAOrV,IAAI,CAAC6F,OAAO,KAAK,SAAS,CAAC,EAAE;UAC3D;UACA;UACA7F,IAAI,CAAC6F,OAAO,GAAGA,OAAO;QAC1B;QACA7F,IAAI,CAAC0C,MAAM,GAAGA,MAAM;QACpB1C,IAAI,CAACwY,OAAO,GAAGA,OAAO;QACtBxY,IAAI,CAAC0M,SAAS,GAAGA,SAAS;QAC1B,IAAIsN,aAAa,EAAE;UACf;UACAha,IAAI,CAACgV,gBAAgB,GAAGvS,QAAQ;QACpC;QACA,IAAI,CAAC4V,OAAO,EAAE;UACVS,aAAa,CAACzT,IAAI,CAACrF,IAAI,CAAC;QAC5B,CAAC,MACI;UACD8Y,aAAa,CAACqB,OAAO,CAACna,IAAI,CAAC;QAC/B;QACA,IAAI6X,YAAY,EAAE;UACd,OAAOnV,MAAM;QACjB;MACJ,CAAC;IACL,CAAC;IACD8K,KAAK,CAAC2I,kBAAkB,CAAC,GAAGuD,eAAe,CAAC1B,sBAAsB,EAAEpB,yBAAyB,EAAEhV,cAAc,EAAEI,YAAY,EAAE6V,YAAY,CAAC;IAC1I,IAAIO,0BAA0B,EAAE;MAC5B5K,KAAK,CAACqJ,sBAAsB,CAAC,GAAG6C,eAAe,CAACtB,0BAA0B,EAAEtB,6BAA6B,EAAEoC,qBAAqB,EAAElX,YAAY,EAAE6V,YAAY,EAAE,IAAI,CAAC;IACvK;IACArK,KAAK,CAAC6I,qBAAqB,CAAC,GAAG,YAAY;MACvC,MAAM3T,MAAM,GAAG,IAAI,IAAI2H,OAAO;MAC9B,IAAIqC,SAAS,GAAGlN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAI0W,YAAY,IAAIA,YAAY,CAAC6D,iBAAiB,EAAE;QAChDrN,SAAS,GAAGwJ,YAAY,CAAC6D,iBAAiB,CAACrN,SAAS,CAAC;MACzD;MACA,MAAM7G,OAAO,GAAGrG,SAAS,CAAC,CAAC,CAAC;MAC5B,MAAMgZ,OAAO,GAAG,CAAC3S,OAAO,GAAG,KAAK,GAAG,OAAOA,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO,CAAC2S,OAAO;MACxF,MAAM/V,QAAQ,GAAGjD,SAAS,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACiD,QAAQ,EAAE;QACX,OAAOwV,yBAAyB,CAAC9S,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;MAC3D;MACA,IAAIkY,eAAe,IACf,CAACA,eAAe,CAACO,yBAAyB,EAAExV,QAAQ,EAAEC,MAAM,EAAElD,SAAS,CAAC,EAAE;QAC1E;MACJ;MACA,MAAMoZ,gBAAgB,GAAGrD,oBAAoB,CAAC7I,SAAS,CAAC;MACxD,IAAImM,eAAe;MACnB,IAAID,gBAAgB,EAAE;QAClBC,eAAe,GAAGD,gBAAgB,CAACJ,OAAO,GAAG1O,QAAQ,GAAGC,SAAS,CAAC;MACtE;MACA,MAAM+O,aAAa,GAAGD,eAAe,IAAInW,MAAM,CAACmW,eAAe,CAAC;MAChE,IAAIC,aAAa,EAAE;QACf,KAAK,IAAIzW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyW,aAAa,CAACxW,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3C,MAAM0W,YAAY,GAAGD,aAAa,CAACzW,CAAC,CAAC;UACrC,IAAIiX,OAAO,CAACP,YAAY,EAAEtW,QAAQ,CAAC,EAAE;YACjCqW,aAAa,CAACtH,MAAM,CAACnP,CAAC,EAAE,CAAC,CAAC;YAC1B;YACA0W,YAAY,CAAChC,SAAS,GAAG,IAAI;YAC7B,IAAI+B,aAAa,CAACxW,MAAM,KAAK,CAAC,EAAE;cAC5B;cACA;cACAyW,YAAY,CAACC,UAAU,GAAG,IAAI;cAC9BtW,MAAM,CAACmW,eAAe,CAAC,GAAG,IAAI;cAC9B;cACA;cACA;cACA,IAAI,OAAOnM,SAAS,KAAK,QAAQ,EAAE;gBAC/B,MAAM0N,gBAAgB,GAAGpQ,kBAAkB,GAAG,aAAa,GAAG0C,SAAS;gBACvEhK,MAAM,CAAC0X,gBAAgB,CAAC,GAAG,IAAI;cACnC;YACJ;YACArB,YAAY,CAACrb,IAAI,CAACwE,UAAU,CAAC6W,YAAY,CAAC;YAC1C,IAAIlB,YAAY,EAAE;cACd,OAAOnV,MAAM;YACjB;YACA;UACJ;QACJ;MACJ;MACA;MACA;MACA;MACA;MACA,OAAOuV,yBAAyB,CAAC9S,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;IAC3D,CAAC;IACDgO,KAAK,CAAC+I,wBAAwB,CAAC,GAAG,YAAY;MAC1C,MAAM7T,MAAM,GAAG,IAAI,IAAI2H,OAAO;MAC9B,IAAIqC,SAAS,GAAGlN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAI0W,YAAY,IAAIA,YAAY,CAAC6D,iBAAiB,EAAE;QAChDrN,SAAS,GAAGwJ,YAAY,CAAC6D,iBAAiB,CAACrN,SAAS,CAAC;MACzD;MACA,MAAM8J,SAAS,GAAG,EAAE;MACpB,MAAMY,KAAK,GAAGiD,cAAc,CAAC3X,MAAM,EAAEmT,iBAAiB,GAAGA,iBAAiB,CAACnJ,SAAS,CAAC,GAAGA,SAAS,CAAC;MAClG,KAAK,IAAIrK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+U,KAAK,CAAC9U,MAAM,EAAED,CAAC,EAAE,EAAE;QACnC,MAAMrC,IAAI,GAAGoX,KAAK,CAAC/U,CAAC,CAAC;QACrB,IAAII,QAAQ,GAAGzC,IAAI,CAACgV,gBAAgB,GAAGhV,IAAI,CAACgV,gBAAgB,GAAGhV,IAAI,CAACb,QAAQ;QAC5EqX,SAAS,CAACnR,IAAI,CAAC5C,QAAQ,CAAC;MAC5B;MACA,OAAO+T,SAAS;IACpB,CAAC;IACDhJ,KAAK,CAACiJ,mCAAmC,CAAC,GAAG,YAAY;MACrD,MAAM/T,MAAM,GAAG,IAAI,IAAI2H,OAAO;MAC9B,IAAIqC,SAAS,GAAGlN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAI,CAACkN,SAAS,EAAE;QACZ,MAAM4N,IAAI,GAAG3T,MAAM,CAAC2T,IAAI,CAAC5X,MAAM,CAAC;QAChC,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiY,IAAI,CAAChY,MAAM,EAAED,CAAC,EAAE,EAAE;UAClC,MAAM8J,IAAI,GAAGmO,IAAI,CAACjY,CAAC,CAAC;UACpB,MAAMkY,KAAK,GAAG9E,sBAAsB,CAAC+E,IAAI,CAACrO,IAAI,CAAC;UAC/C,IAAIsO,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;UAC/B;UACA;UACA;UACA;UACA,IAAIE,OAAO,IAAIA,OAAO,KAAK,gBAAgB,EAAE;YACzC,IAAI,CAAChE,mCAAmC,CAAC,CAACxQ,IAAI,CAAC,IAAI,EAAEwU,OAAO,CAAC;UACjE;QACJ;QACA;QACA,IAAI,CAAChE,mCAAmC,CAAC,CAACxQ,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC;MAC1E,CAAC,MACI;QACD,IAAIiQ,YAAY,IAAIA,YAAY,CAAC6D,iBAAiB,EAAE;UAChDrN,SAAS,GAAGwJ,YAAY,CAAC6D,iBAAiB,CAACrN,SAAS,CAAC;QACzD;QACA,MAAMkM,gBAAgB,GAAGrD,oBAAoB,CAAC7I,SAAS,CAAC;QACxD,IAAIkM,gBAAgB,EAAE;UAClB,MAAMC,eAAe,GAAGD,gBAAgB,CAAC7O,SAAS,CAAC;UACnD,MAAM2Q,sBAAsB,GAAG9B,gBAAgB,CAAC9O,QAAQ,CAAC;UACzD,MAAMsN,KAAK,GAAG1U,MAAM,CAACmW,eAAe,CAAC;UACrC,MAAM8B,YAAY,GAAGjY,MAAM,CAACgY,sBAAsB,CAAC;UACnD,IAAItD,KAAK,EAAE;YACP,MAAMwD,WAAW,GAAGxD,KAAK,CAAC3N,KAAK,CAAC,CAAC;YACjC,KAAK,IAAIpH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuY,WAAW,CAACtY,MAAM,EAAED,CAAC,EAAE,EAAE;cACzC,MAAMrC,IAAI,GAAG4a,WAAW,CAACvY,CAAC,CAAC;cAC3B,IAAII,QAAQ,GAAGzC,IAAI,CAACgV,gBAAgB,GAAGhV,IAAI,CAACgV,gBAAgB,GAAGhV,IAAI,CAACb,QAAQ;cAC5E,IAAI,CAACkX,qBAAqB,CAAC,CAACpQ,IAAI,CAAC,IAAI,EAAEyG,SAAS,EAAEjK,QAAQ,EAAEzC,IAAI,CAAC6F,OAAO,CAAC;YAC7E;UACJ;UACA,IAAI8U,YAAY,EAAE;YACd,MAAMC,WAAW,GAAGD,YAAY,CAAClR,KAAK,CAAC,CAAC;YACxC,KAAK,IAAIpH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuY,WAAW,CAACtY,MAAM,EAAED,CAAC,EAAE,EAAE;cACzC,MAAMrC,IAAI,GAAG4a,WAAW,CAACvY,CAAC,CAAC;cAC3B,IAAII,QAAQ,GAAGzC,IAAI,CAACgV,gBAAgB,GAAGhV,IAAI,CAACgV,gBAAgB,GAAGhV,IAAI,CAACb,QAAQ;cAC5E,IAAI,CAACkX,qBAAqB,CAAC,CAACpQ,IAAI,CAAC,IAAI,EAAEyG,SAAS,EAAEjK,QAAQ,EAAEzC,IAAI,CAAC6F,OAAO,CAAC;YAC7E;UACJ;QACJ;MACJ;MACA,IAAIgS,YAAY,EAAE;QACd,OAAO,IAAI;MACf;IACJ,CAAC;IACD;IACA9O,qBAAqB,CAACyE,KAAK,CAAC2I,kBAAkB,CAAC,EAAE6B,sBAAsB,CAAC;IACxEjP,qBAAqB,CAACyE,KAAK,CAAC6I,qBAAqB,CAAC,EAAE4B,yBAAyB,CAAC;IAC9E,IAAIE,wBAAwB,EAAE;MAC1BpP,qBAAqB,CAACyE,KAAK,CAACiJ,mCAAmC,CAAC,EAAE0B,wBAAwB,CAAC;IAC/F;IACA,IAAID,eAAe,EAAE;MACjBnP,qBAAqB,CAACyE,KAAK,CAAC+I,wBAAwB,CAAC,EAAE2B,eAAe,CAAC;IAC3E;IACA,OAAO,IAAI;EACf;EACA,IAAI2C,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIxY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4T,IAAI,CAAC3T,MAAM,EAAED,CAAC,EAAE,EAAE;IAClCwY,OAAO,CAACxY,CAAC,CAAC,GAAGmV,uBAAuB,CAACvB,IAAI,CAAC5T,CAAC,CAAC,EAAE6T,YAAY,CAAC;EAC/D;EACA,OAAO2E,OAAO;AAClB;AACA,SAASR,cAAcA,CAAC3X,MAAM,EAAEgK,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,EAAE;IACZ,MAAMoO,UAAU,GAAG,EAAE;IACrB,KAAK,IAAI3O,IAAI,IAAIzJ,MAAM,EAAE;MACrB,MAAM6X,KAAK,GAAG9E,sBAAsB,CAAC+E,IAAI,CAACrO,IAAI,CAAC;MAC/C,IAAIsO,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;MAC/B,IAAIE,OAAO,KAAK,CAAC/N,SAAS,IAAI+N,OAAO,KAAK/N,SAAS,CAAC,EAAE;QAClD,MAAM0K,KAAK,GAAG1U,MAAM,CAACyJ,IAAI,CAAC;QAC1B,IAAIiL,KAAK,EAAE;UACP,KAAK,IAAI/U,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+U,KAAK,CAAC9U,MAAM,EAAED,CAAC,EAAE,EAAE;YACnCyY,UAAU,CAACzV,IAAI,CAAC+R,KAAK,CAAC/U,CAAC,CAAC,CAAC;UAC7B;QACJ;MACJ;IACJ;IACA,OAAOyY,UAAU;EACrB;EACA,IAAIjC,eAAe,GAAGtD,oBAAoB,CAAC7I,SAAS,CAAC;EACrD,IAAI,CAACmM,eAAe,EAAE;IAClBjD,iBAAiB,CAAClJ,SAAS,CAAC;IAC5BmM,eAAe,GAAGtD,oBAAoB,CAAC7I,SAAS,CAAC;EACrD;EACA,MAAMqO,iBAAiB,GAAGrY,MAAM,CAACmW,eAAe,CAAC9O,SAAS,CAAC,CAAC;EAC5D,MAAMiR,gBAAgB,GAAGtY,MAAM,CAACmW,eAAe,CAAC/O,QAAQ,CAAC,CAAC;EAC1D,IAAI,CAACiR,iBAAiB,EAAE;IACpB,OAAOC,gBAAgB,GAAGA,gBAAgB,CAACvR,KAAK,CAAC,CAAC,GAAG,EAAE;EAC3D,CAAC,MACI;IACD,OAAOuR,gBAAgB,GAAGD,iBAAiB,CAACE,MAAM,CAACD,gBAAgB,CAAC,GAChED,iBAAiB,CAACtR,KAAK,CAAC,CAAC;EACjC;AACJ;AACA,SAASpB,mBAAmBA,CAAC1L,MAAM,EAAE6R,GAAG,EAAE;EACtC,MAAM0M,KAAK,GAAGve,MAAM,CAAC,OAAO,CAAC;EAC7B,IAAIue,KAAK,IAAIA,KAAK,CAACtU,SAAS,EAAE;IAC1B4H,GAAG,CAACvG,WAAW,CAACiT,KAAK,CAACtU,SAAS,EAAE,0BAA0B,EAAGnE,QAAQ,IAAK,UAAUsD,IAAI,EAAEG,IAAI,EAAE;MAC7FH,IAAI,CAAC4P,4BAA4B,CAAC,GAAG,IAAI;MACzC;MACA;MACA;MACAlT,QAAQ,IAAIA,QAAQ,CAAC0C,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;IAC1C,CAAC,CAAC;EACN;AACJ;AAEA,SAAS+C,cAAcA,CAACuF,GAAG,EAAE9L,MAAM,EAAEyY,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAE;EAChE,MAAM1T,MAAM,GAAGtK,IAAI,CAACH,UAAU,CAACke,MAAM,CAAC;EACtC,IAAI1Y,MAAM,CAACiF,MAAM,CAAC,EAAE;IAChB;EACJ;EACA,MAAM2T,cAAc,GAAG5Y,MAAM,CAACiF,MAAM,CAAC,GAAGjF,MAAM,CAAC0Y,MAAM,CAAC;EACtD1Y,MAAM,CAAC0Y,MAAM,CAAC,GAAG,UAAUte,IAAI,EAAEye,IAAI,EAAE1V,OAAO,EAAE;IAC5C,IAAI0V,IAAI,IAAIA,IAAI,CAAC3U,SAAS,EAAE;MACxByU,SAAS,CAACG,OAAO,CAAC,UAAUrc,QAAQ,EAAE;QAClC,MAAMC,MAAM,GAAI,GAAE+b,UAAW,IAAGC,MAAO,IAAG,GAAGjc,QAAQ;QACrD,MAAMyH,SAAS,GAAG2U,IAAI,CAAC3U,SAAS;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI;UACA,IAAIA,SAAS,CAACzI,cAAc,CAACgB,QAAQ,CAAC,EAAE;YACpC,MAAMsc,UAAU,GAAGjN,GAAG,CAAC/F,8BAA8B,CAAC7B,SAAS,EAAEzH,QAAQ,CAAC;YAC1E,IAAIsc,UAAU,IAAIA,UAAU,CAAClW,KAAK,EAAE;cAChCkW,UAAU,CAAClW,KAAK,GAAGiJ,GAAG,CAAC3F,mBAAmB,CAAC4S,UAAU,CAAClW,KAAK,EAAEnG,MAAM,CAAC;cACpEoP,GAAG,CAACxF,iBAAiB,CAACuS,IAAI,CAAC3U,SAAS,EAAEzH,QAAQ,EAAEsc,UAAU,CAAC;YAC/D,CAAC,MACI,IAAI7U,SAAS,CAACzH,QAAQ,CAAC,EAAE;cAC1ByH,SAAS,CAACzH,QAAQ,CAAC,GAAGqP,GAAG,CAAC3F,mBAAmB,CAACjC,SAAS,CAACzH,QAAQ,CAAC,EAAEC,MAAM,CAAC;YAC9E;UACJ,CAAC,MACI,IAAIwH,SAAS,CAACzH,QAAQ,CAAC,EAAE;YAC1ByH,SAAS,CAACzH,QAAQ,CAAC,GAAGqP,GAAG,CAAC3F,mBAAmB,CAACjC,SAAS,CAACzH,QAAQ,CAAC,EAAEC,MAAM,CAAC;UAC9E;QACJ,CAAC,CACD,MAAM;UACF;UACA;QAAA;MAER,CAAC,CAAC;IACN;IACA,OAAOkc,cAAc,CAACrV,IAAI,CAACvD,MAAM,EAAE5F,IAAI,EAAEye,IAAI,EAAE1V,OAAO,CAAC;EAC3D,CAAC;EACD2I,GAAG,CAACzF,qBAAqB,CAACrG,MAAM,CAAC0Y,MAAM,CAAC,EAAEE,cAAc,CAAC;AAC7D;;AAEA;AACA;AACA;AACA;AACA,SAASxS,gBAAgBA,CAACpG,MAAM,EAAEsK,YAAY,EAAE0O,gBAAgB,EAAE;EAC9D,IAAI,CAACA,gBAAgB,IAAIA,gBAAgB,CAACpZ,MAAM,KAAK,CAAC,EAAE;IACpD,OAAO0K,YAAY;EACvB;EACA,MAAM2O,GAAG,GAAGD,gBAAgB,CAACE,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACnZ,MAAM,KAAKA,MAAM,CAAC;EAC/D,IAAI,CAACiZ,GAAG,IAAIA,GAAG,CAACrZ,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAO0K,YAAY;EACvB;EACA,MAAM8O,sBAAsB,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACD,gBAAgB;EACtD,OAAO1O,YAAY,CAAC4O,MAAM,CAACG,EAAE,IAAID,sBAAsB,CAACvN,OAAO,CAACwN,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/E;AACA,SAASC,uBAAuBA,CAACtZ,MAAM,EAAEsK,YAAY,EAAE0O,gBAAgB,EAAE9U,SAAS,EAAE;EAChF;EACA;EACA,IAAI,CAAClE,MAAM,EAAE;IACT;EACJ;EACA,MAAMuZ,kBAAkB,GAAGnT,gBAAgB,CAACpG,MAAM,EAAEsK,YAAY,EAAE0O,gBAAgB,CAAC;EACnF1T,iBAAiB,CAACtF,MAAM,EAAEuZ,kBAAkB,EAAErV,SAAS,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA,SAASsV,eAAeA,CAACxZ,MAAM,EAAE;EAC7B,OAAOiE,MAAM,CAACwV,mBAAmB,CAACzZ,MAAM,CAAC,CACpCkZ,MAAM,CAAC9e,IAAI,IAAIA,IAAI,CAACsf,UAAU,CAAC,IAAI,CAAC,IAAItf,IAAI,CAACwF,MAAM,GAAG,CAAC,CAAC,CACxD+Z,GAAG,CAACvf,IAAI,IAAIA,IAAI,CAACwf,SAAS,CAAC,CAAC,CAAC,CAAC;AACvC;AACA,SAASC,uBAAuBA,CAAC/N,GAAG,EAAEnE,OAAO,EAAE;EAC3C,IAAIY,MAAM,IAAI,CAACG,KAAK,EAAE;IAClB;EACJ;EACA,IAAI/N,IAAI,CAACmR,GAAG,CAAC7G,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE;IACjC;IACA;EACJ;EACA,MAAM+T,gBAAgB,GAAGrR,OAAO,CAAC,6BAA6B,CAAC;EAC/D;EACA,IAAImS,YAAY,GAAG,EAAE;EACrB,IAAIrR,SAAS,EAAE;IACX,MAAMf,cAAc,GAAGlB,MAAM;IAC7BsT,YAAY,GAAGA,YAAY,CAACvB,MAAM,CAAC,CAC/B,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EACzF,qBAAqB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,QAAQ,CACjG,CAAC;IACF,MAAMwB,qBAAqB,GAAGtO,IAAI,CAAC,CAAC,GAAG,CAAC;MAAEzL,MAAM,EAAE0H,cAAc;MAAEsR,gBAAgB,EAAE,CAAC,OAAO;IAAE,CAAC,CAAC,GAAG,EAAE;IACrG;IACA;IACAM,uBAAuB,CAAC5R,cAAc,EAAE8R,eAAe,CAAC9R,cAAc,CAAC,EAAEsR,gBAAgB,GAAGA,gBAAgB,CAACT,MAAM,CAACwB,qBAAqB,CAAC,GAAGf,gBAAgB,EAAErS,oBAAoB,CAACe,cAAc,CAAC,CAAC;EACxM;EACAoS,YAAY,GAAGA,YAAY,CAACvB,MAAM,CAAC,CAC/B,gBAAgB,EAAE,2BAA2B,EAAE,UAAU,EAAE,YAAY,EAAE,kBAAkB,EAC3F,aAAa,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,CAC5D,CAAC;EACF,KAAK,IAAI5Y,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGma,YAAY,CAACla,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,MAAMK,MAAM,GAAG2H,OAAO,CAACmS,YAAY,CAACna,CAAC,CAAC,CAAC;IACvCK,MAAM,IAAIA,MAAM,CAACkE,SAAS,IACtBoV,uBAAuB,CAACtZ,MAAM,CAACkE,SAAS,EAAEsV,eAAe,CAACxZ,MAAM,CAACkE,SAAS,CAAC,EAAE8U,gBAAgB,CAAC;EACtG;AACJ;AAEAre,IAAI,CAACW,YAAY,CAAC,MAAM,EAAE,CAACrB,MAAM,EAAEU,IAAI,EAAEmR,GAAG,KAAK;EAC7C;EACA;EACA,MAAMkO,UAAU,GAAGR,eAAe,CAACvf,MAAM,CAAC;EAC1C6R,GAAG,CAACxG,iBAAiB,GAAGA,iBAAiB;EACzCwG,GAAG,CAACvG,WAAW,GAAGA,WAAW;EAC7BuG,GAAG,CAACtG,aAAa,GAAGA,aAAa;EACjCsG,GAAG,CAACpG,cAAc,GAAGA,cAAc;EACnC;EACA;EACA;EACA;EACA;EACA;EACA,MAAMuU,0BAA0B,GAAGtf,IAAI,CAACH,UAAU,CAAC,qBAAqB,CAAC;EACzE,MAAM0f,uBAAuB,GAAGvf,IAAI,CAACH,UAAU,CAAC,kBAAkB,CAAC;EACnE,IAAIP,MAAM,CAACigB,uBAAuB,CAAC,EAAE;IACjCjgB,MAAM,CAACggB,0BAA0B,CAAC,GAAGhgB,MAAM,CAACigB,uBAAuB,CAAC;EACxE;EACA,IAAIjgB,MAAM,CAACggB,0BAA0B,CAAC,EAAE;IACpCtf,IAAI,CAACsf,0BAA0B,CAAC,GAAGtf,IAAI,CAACuf,uBAAuB,CAAC,GAC5DjgB,MAAM,CAACggB,0BAA0B,CAAC;EAC1C;EACAnO,GAAG,CAACnG,mBAAmB,GAAGA,mBAAmB;EAC7CmG,GAAG,CAACzG,gBAAgB,GAAGA,gBAAgB;EACvCyG,GAAG,CAAClG,UAAU,GAAGA,UAAU;EAC3BkG,GAAG,CAAChG,oBAAoB,GAAGA,oBAAoB;EAC/CgG,GAAG,CAAC/F,8BAA8B,GAAGA,8BAA8B;EACnE+F,GAAG,CAAC9F,YAAY,GAAGA,YAAY;EAC/B8F,GAAG,CAAC7F,UAAU,GAAGA,UAAU;EAC3B6F,GAAG,CAAC5F,UAAU,GAAGA,UAAU;EAC3B4F,GAAG,CAAC3F,mBAAmB,GAAGA,mBAAmB;EAC7C2F,GAAG,CAAC1F,gBAAgB,GAAGA,gBAAgB;EACvC0F,GAAG,CAACzF,qBAAqB,GAAGA,qBAAqB;EACjDyF,GAAG,CAACxF,iBAAiB,GAAGrC,MAAM,CAACyC,cAAc;EAC7CoF,GAAG,CAACvF,cAAc,GAAGA,cAAc;EACnCuF,GAAG,CAACjG,gBAAgB,GAAG,OAAO;IAC1BiN,aAAa;IACbD,oBAAoB;IACpBmH,UAAU;IACVvR,SAAS;IACTC,KAAK;IACLH,MAAM;IACNnB,QAAQ;IACRC,SAAS;IACTC,kBAAkB;IAClBN,sBAAsB;IACtBC;EACJ,CAAC,CAAC;AACN,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,SAASkT,mBAAmBA,CAAClgB,MAAM,EAAE6R,GAAG,EAAE;EACtCA,GAAG,CAACvG,WAAW,CAACtL,MAAM,EAAE,gBAAgB,EAAG8F,QAAQ,IAAK;IACpD,OAAO,UAAUsD,IAAI,EAAEG,IAAI,EAAE;MACzB7I,IAAI,CAACM,OAAO,CAACgE,iBAAiB,CAAC,gBAAgB,EAAEuE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;EACL,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA,MAAM4W,UAAU,GAAG5S,UAAU,CAAC,UAAU,CAAC;AACzC,SAAS6S,UAAUA,CAAC7T,MAAM,EAAE8T,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACzD,IAAIrP,SAAS,GAAG,IAAI;EACpB,IAAIsP,WAAW,GAAG,IAAI;EACtBH,OAAO,IAAIE,UAAU;EACrBD,UAAU,IAAIC,UAAU;EACxB,MAAME,eAAe,GAAG,CAAC,CAAC;EAC1B,SAAShc,YAAYA,CAACpB,IAAI,EAAE;IACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;IACtBA,IAAI,CAACqF,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY;MACvB,OAAOlG,IAAI,CAACJ,MAAM,CAACuF,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;IAC7C,CAAC;IACDqB,IAAI,CAAC6F,QAAQ,GAAGmH,SAAS,CAAC1I,KAAK,CAAC+D,MAAM,EAAErI,IAAI,CAACqF,IAAI,CAAC;IAClD,OAAOlG,IAAI;EACf;EACA,SAASqd,SAASA,CAACrd,IAAI,EAAE;IACrB,OAAOmd,WAAW,CAAClX,IAAI,CAACiD,MAAM,EAAElJ,IAAI,CAACa,IAAI,CAAC6F,QAAQ,CAAC;EACvD;EACAmH,SAAS,GACL5F,WAAW,CAACiB,MAAM,EAAE8T,OAAO,EAAGva,QAAQ,IAAK,UAAUsD,IAAI,EAAEG,IAAI,EAAE;IAC7D,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;MAC/B,MAAML,OAAO,GAAG;QACZ/E,UAAU,EAAEoc,UAAU,KAAK,UAAU;QACrCI,KAAK,EAAGJ,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,UAAU,GAAIhX,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GACzElF,SAAS;QACbkF,IAAI,EAAEA;MACV,CAAC;MACD,MAAM/G,QAAQ,GAAG+G,IAAI,CAAC,CAAC,CAAC;MACxBA,IAAI,CAAC,CAAC,CAAC,GAAG,SAASqX,KAAKA,CAAA,EAAG;QACvB,IAAI;UACA,OAAOpe,QAAQ,CAACgG,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;QAC1C,CAAC,SACO;UACJ;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAAEqG,OAAO,CAAC/E,UAAW,EAAE;YACvB,IAAI,OAAO+E,OAAO,CAACa,QAAQ,KAAK,QAAQ,EAAE;cACtC;cACA;cACA,OAAO0W,eAAe,CAACvX,OAAO,CAACa,QAAQ,CAAC;YAC5C,CAAC,MACI,IAAIb,OAAO,CAACa,QAAQ,EAAE;cACvB;cACA;cACAb,OAAO,CAACa,QAAQ,CAACoW,UAAU,CAAC,GAAG,IAAI;YACvC;UACJ;QACJ;MACJ,CAAC;MACD,MAAM9c,IAAI,GAAGiK,gCAAgC,CAAC+S,OAAO,EAAE9W,IAAI,CAAC,CAAC,CAAC,EAAEL,OAAO,EAAEzE,YAAY,EAAEic,SAAS,CAAC;MACjG,IAAI,CAACrd,IAAI,EAAE;QACP,OAAOA,IAAI;MACf;MACA;MACA,MAAMwd,MAAM,GAAGxd,IAAI,CAACa,IAAI,CAAC6F,QAAQ;MACjC,IAAI,OAAO8W,MAAM,KAAK,QAAQ,EAAE;QAC5B;QACA;QACAJ,eAAe,CAACI,MAAM,CAAC,GAAGxd,IAAI;MAClC,CAAC,MACI,IAAIwd,MAAM,EAAE;QACb;QACA;QACAA,MAAM,CAACV,UAAU,CAAC,GAAG9c,IAAI;MAC7B;MACA;MACA;MACA,IAAIwd,MAAM,IAAIA,MAAM,CAACC,GAAG,IAAID,MAAM,CAACE,KAAK,IAAI,OAAOF,MAAM,CAACC,GAAG,KAAK,UAAU,IACxE,OAAOD,MAAM,CAACE,KAAK,KAAK,UAAU,EAAE;QACpC1d,IAAI,CAACyd,GAAG,GAAGD,MAAM,CAACC,GAAG,CAACE,IAAI,CAACH,MAAM,CAAC;QAClCxd,IAAI,CAAC0d,KAAK,GAAGF,MAAM,CAACE,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;MAC1C;MACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,EAAE;QACtC,OAAOA,MAAM;MACjB;MACA,OAAOxd,IAAI;IACf,CAAC,MACI;MACD;MACA,OAAOyC,QAAQ,CAAC0C,KAAK,CAAC+D,MAAM,EAAEhD,IAAI,CAAC;IACvC;EACJ,CAAC,CAAC;EACNiX,WAAW,GACPlV,WAAW,CAACiB,MAAM,EAAE+T,UAAU,EAAGxa,QAAQ,IAAK,UAAUsD,IAAI,EAAEG,IAAI,EAAE;IAChE,MAAM0X,EAAE,GAAG1X,IAAI,CAAC,CAAC,CAAC;IAClB,IAAIlG,IAAI;IACR,IAAI,OAAO4d,EAAE,KAAK,QAAQ,EAAE;MACxB;MACA5d,IAAI,GAAGod,eAAe,CAACQ,EAAE,CAAC;IAC9B,CAAC,MACI;MACD;MACA5d,IAAI,GAAG4d,EAAE,IAAIA,EAAE,CAACd,UAAU,CAAC;MAC3B;MACA,IAAI,CAAC9c,IAAI,EAAE;QACPA,IAAI,GAAG4d,EAAE;MACb;IACJ;IACA,IAAI5d,IAAI,IAAI,OAAOA,IAAI,CAACI,IAAI,KAAK,QAAQ,EAAE;MACvC,IAAIJ,IAAI,CAACE,KAAK,KAAK,cAAc,KAC5BF,IAAI,CAACe,QAAQ,IAAIf,IAAI,CAACa,IAAI,CAACC,UAAU,IAAId,IAAI,CAACW,QAAQ,KAAK,CAAC,CAAC,EAAE;QAChE,IAAI,OAAOid,EAAE,KAAK,QAAQ,EAAE;UACxB,OAAOR,eAAe,CAACQ,EAAE,CAAC;QAC9B,CAAC,MACI,IAAIA,EAAE,EAAE;UACTA,EAAE,CAACd,UAAU,CAAC,GAAG,IAAI;QACzB;QACA;QACA9c,IAAI,CAACtC,IAAI,CAACwE,UAAU,CAAClC,IAAI,CAAC;MAC9B;IACJ,CAAC,MACI;MACD;MACAyC,QAAQ,CAAC0C,KAAK,CAAC+D,MAAM,EAAEhD,IAAI,CAAC;IAChC;EACJ,CAAC,CAAC;AACV;AAEA,SAAS2X,mBAAmBA,CAACxT,OAAO,EAAEmE,GAAG,EAAE;EACvC,MAAM;IAAErD,SAAS;IAAEC;EAAM,CAAC,GAAGoD,GAAG,CAACjG,gBAAgB,CAAC,CAAC;EACnD,IAAK,CAAC4C,SAAS,IAAI,CAACC,KAAK,IAAK,CAACf,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,gBAAgB,IAAIA,OAAO,CAAC,EAAE;IACxF;EACJ;EACA,MAAMgR,SAAS,GAAG,CAAC,mBAAmB,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,0BAA0B,CAAC;EAC9G7M,GAAG,CAACvF,cAAc,CAACuF,GAAG,EAAEnE,OAAO,CAACyT,cAAc,EAAE,gBAAgB,EAAE,QAAQ,EAAEzC,SAAS,CAAC;AAC1F;AAEA,SAAS0C,gBAAgBA,CAAC1T,OAAO,EAAEmE,GAAG,EAAE;EACpC,IAAInR,IAAI,CAACmR,GAAG,CAAC7G,MAAM,CAAC,kBAAkB,CAAC,CAAC,EAAE;IACtC;IACA;EACJ;EACA,MAAM;IAAE+U,UAAU;IAAEnH,oBAAoB;IAAEzL,QAAQ;IAAEC,SAAS;IAAEC;EAAmB,CAAC,GAAGwE,GAAG,CAACjG,gBAAgB,CAAC,CAAC;EAC5G;EACA,KAAK,IAAIlG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqa,UAAU,CAACpa,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,MAAMqK,SAAS,GAAGgQ,UAAU,CAACra,CAAC,CAAC;IAC/B,MAAMyT,cAAc,GAAGpJ,SAAS,GAAG3C,SAAS;IAC5C,MAAMgM,aAAa,GAAGrJ,SAAS,GAAG5C,QAAQ;IAC1C,MAAMnC,MAAM,GAAGqC,kBAAkB,GAAG8L,cAAc;IAClD,MAAME,aAAa,GAAGhM,kBAAkB,GAAG+L,aAAa;IACxDR,oBAAoB,CAAC7I,SAAS,CAAC,GAAG,CAAC,CAAC;IACpC6I,oBAAoB,CAAC7I,SAAS,CAAC,CAAC3C,SAAS,CAAC,GAAGpC,MAAM;IACnD4N,oBAAoB,CAAC7I,SAAS,CAAC,CAAC5C,QAAQ,CAAC,GAAGkM,aAAa;EAC7D;EACA,MAAMgI,YAAY,GAAG3T,OAAO,CAAC,aAAa,CAAC;EAC3C,IAAI,CAAC2T,YAAY,IAAI,CAACA,YAAY,CAACpX,SAAS,EAAE;IAC1C;EACJ;EACA4H,GAAG,CAACzG,gBAAgB,CAACsC,OAAO,EAAEmE,GAAG,EAAE,CAACwP,YAAY,IAAIA,YAAY,CAACpX,SAAS,CAAC,CAAC;EAC5E,OAAO,IAAI;AACf;AACA,SAASqX,UAAUA,CAACthB,MAAM,EAAE6R,GAAG,EAAE;EAC7BA,GAAG,CAACnG,mBAAmB,CAAC1L,MAAM,EAAE6R,GAAG,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACAnR,IAAI,CAACW,YAAY,CAAC,QAAQ,EAAGrB,MAAM,IAAK;EACpC,MAAMuhB,WAAW,GAAGvhB,MAAM,CAACU,IAAI,CAACH,UAAU,CAAC,aAAa,CAAC,CAAC;EAC1D,IAAIghB,WAAW,EAAE;IACbA,WAAW,CAAC,CAAC;EACjB;AACJ,CAAC,CAAC;AACF7gB,IAAI,CAACW,YAAY,CAAC,QAAQ,EAAGrB,MAAM,IAAK;EACpC,MAAMmO,GAAG,GAAG,KAAK;EACjB,MAAMqT,KAAK,GAAG,OAAO;EACrBpB,UAAU,CAACpgB,MAAM,EAAEmO,GAAG,EAAEqT,KAAK,EAAE,SAAS,CAAC;EACzCpB,UAAU,CAACpgB,MAAM,EAAEmO,GAAG,EAAEqT,KAAK,EAAE,UAAU,CAAC;EAC1CpB,UAAU,CAACpgB,MAAM,EAAEmO,GAAG,EAAEqT,KAAK,EAAE,WAAW,CAAC;AAC/C,CAAC,CAAC;AACF9gB,IAAI,CAACW,YAAY,CAAC,uBAAuB,EAAGrB,MAAM,IAAK;EACnDogB,UAAU,CAACpgB,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,CAAC;EACzDogB,UAAU,CAACpgB,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,gBAAgB,CAAC;EAC/DogB,UAAU,CAACpgB,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,gBAAgB,CAAC;AACzE,CAAC,CAAC;AACFU,IAAI,CAACW,YAAY,CAAC,UAAU,EAAE,CAACrB,MAAM,EAAEU,IAAI,KAAK;EAC5C,MAAM+gB,eAAe,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;EACtD,KAAK,IAAI/b,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+b,eAAe,CAAC9b,MAAM,EAAED,CAAC,EAAE,EAAE;IAC7C,MAAMvF,IAAI,GAAGshB,eAAe,CAAC/b,CAAC,CAAC;IAC/B4F,WAAW,CAACtL,MAAM,EAAEG,IAAI,EAAE,CAAC2F,QAAQ,EAAEkF,MAAM,EAAE7K,IAAI,KAAK;MAClD,OAAO,UAAUuhB,CAAC,EAAEnY,IAAI,EAAE;QACtB,OAAO7I,IAAI,CAACM,OAAO,CAAC8B,GAAG,CAACgD,QAAQ,EAAE9F,MAAM,EAAEuJ,IAAI,EAAEpJ,IAAI,CAAC;MACzD,CAAC;IACL,CAAC,CAAC;EACN;AACJ,CAAC,CAAC;AACFO,IAAI,CAACW,YAAY,CAAC,aAAa,EAAE,CAACrB,MAAM,EAAEU,IAAI,EAAEmR,GAAG,KAAK;EACpDyP,UAAU,CAACthB,MAAM,EAAE6R,GAAG,CAAC;EACvBuP,gBAAgB,CAACphB,MAAM,EAAE6R,GAAG,CAAC;EAC7B;EACA,MAAM8P,yBAAyB,GAAG3hB,MAAM,CAAC,2BAA2B,CAAC;EACrE,IAAI2hB,yBAAyB,IAAIA,yBAAyB,CAAC1X,SAAS,EAAE;IAClE4H,GAAG,CAACzG,gBAAgB,CAACpL,MAAM,EAAE6R,GAAG,EAAE,CAAC8P,yBAAyB,CAAC1X,SAAS,CAAC,CAAC;EAC5E;AACJ,CAAC,CAAC;AACFvJ,IAAI,CAACW,YAAY,CAAC,kBAAkB,EAAE,CAACrB,MAAM,EAAEU,IAAI,EAAEmR,GAAG,KAAK;EACzD5F,UAAU,CAAC,kBAAkB,CAAC;EAC9BA,UAAU,CAAC,wBAAwB,CAAC;AACxC,CAAC,CAAC;AACFvL,IAAI,CAACW,YAAY,CAAC,sBAAsB,EAAE,CAACrB,MAAM,EAAEU,IAAI,EAAEmR,GAAG,KAAK;EAC7D5F,UAAU,CAAC,sBAAsB,CAAC;AACtC,CAAC,CAAC;AACFvL,IAAI,CAACW,YAAY,CAAC,YAAY,EAAE,CAACrB,MAAM,EAAEU,IAAI,EAAEmR,GAAG,KAAK;EACnD5F,UAAU,CAAC,YAAY,CAAC;AAC5B,CAAC,CAAC;AACFvL,IAAI,CAACW,YAAY,CAAC,aAAa,EAAE,CAACrB,MAAM,EAAEU,IAAI,EAAEmR,GAAG,KAAK;EACpD+N,uBAAuB,CAAC/N,GAAG,EAAE7R,MAAM,CAAC;AACxC,CAAC,CAAC;AACFU,IAAI,CAACW,YAAY,CAAC,gBAAgB,EAAE,CAACrB,MAAM,EAAEU,IAAI,EAAEmR,GAAG,KAAK;EACvDqP,mBAAmB,CAAClhB,MAAM,EAAE6R,GAAG,CAAC;AACpC,CAAC,CAAC;AACFnR,IAAI,CAACW,YAAY,CAAC,KAAK,EAAE,CAACrB,MAAM,EAAEU,IAAI,KAAK;EACvC;EACAkhB,QAAQ,CAAC5hB,MAAM,CAAC;EAChB,MAAM6hB,QAAQ,GAAGtU,UAAU,CAAC,SAAS,CAAC;EACtC,MAAMuU,QAAQ,GAAGvU,UAAU,CAAC,SAAS,CAAC;EACtC,MAAMwU,YAAY,GAAGxU,UAAU,CAAC,aAAa,CAAC;EAC9C,MAAMyU,aAAa,GAAGzU,UAAU,CAAC,cAAc,CAAC;EAChD,MAAM0U,OAAO,GAAG1U,UAAU,CAAC,QAAQ,CAAC;EACpC,MAAM2U,0BAA0B,GAAG3U,UAAU,CAAC,yBAAyB,CAAC;EACxE,SAASqU,QAAQA,CAACrV,MAAM,EAAE;IACtB,MAAM4V,cAAc,GAAG5V,MAAM,CAAC,gBAAgB,CAAC;IAC/C,IAAI,CAAC4V,cAAc,EAAE;MACjB;MACA;IACJ;IACA,MAAMC,uBAAuB,GAAGD,cAAc,CAAClY,SAAS;IACxD,SAASoY,eAAeA,CAACtc,MAAM,EAAE;MAC7B,OAAOA,MAAM,CAAC8b,QAAQ,CAAC;IAC3B;IACA,IAAIS,cAAc,GAAGF,uBAAuB,CAACnV,8BAA8B,CAAC;IAC5E,IAAIsV,iBAAiB,GAAGH,uBAAuB,CAAClV,iCAAiC,CAAC;IAClF,IAAI,CAACoV,cAAc,EAAE;MACjB,MAAMX,yBAAyB,GAAGpV,MAAM,CAAC,2BAA2B,CAAC;MACrE,IAAIoV,yBAAyB,EAAE;QAC3B,MAAMa,kCAAkC,GAAGb,yBAAyB,CAAC1X,SAAS;QAC9EqY,cAAc,GAAGE,kCAAkC,CAACvV,8BAA8B,CAAC;QACnFsV,iBAAiB,GAAGC,kCAAkC,CAACtV,iCAAiC,CAAC;MAC7F;IACJ;IACA,MAAMuV,kBAAkB,GAAG,kBAAkB;IAC7C,MAAMC,SAAS,GAAG,WAAW;IAC7B,SAASje,YAAYA,CAACpB,IAAI,EAAE;MACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;MACtB,MAAM6B,MAAM,GAAG7B,IAAI,CAAC6B,MAAM;MAC1BA,MAAM,CAACic,aAAa,CAAC,GAAG,KAAK;MAC7Bjc,MAAM,CAACmc,0BAA0B,CAAC,GAAG,KAAK;MAC1C;MACA,MAAMpT,QAAQ,GAAG/I,MAAM,CAACgc,YAAY,CAAC;MACrC,IAAI,CAACO,cAAc,EAAE;QACjBA,cAAc,GAAGvc,MAAM,CAACkH,8BAA8B,CAAC;QACvDsV,iBAAiB,GAAGxc,MAAM,CAACmH,iCAAiC,CAAC;MACjE;MACA,IAAI4B,QAAQ,EAAE;QACVyT,iBAAiB,CAACjZ,IAAI,CAACvD,MAAM,EAAE0c,kBAAkB,EAAE3T,QAAQ,CAAC;MAChE;MACA,MAAM6T,WAAW,GAAG5c,MAAM,CAACgc,YAAY,CAAC,GAAG,MAAM;QAC7C,IAAIhc,MAAM,CAAC6c,UAAU,KAAK7c,MAAM,CAAC8c,IAAI,EAAE;UACnC;UACA;UACA,IAAI,CAAC3e,IAAI,CAAC4e,OAAO,IAAI/c,MAAM,CAACic,aAAa,CAAC,IAAI3e,IAAI,CAACE,KAAK,KAAKmf,SAAS,EAAE;YACpE;YACA;YACA;YACA;YACA;YACA;YACA;YACA,MAAMK,SAAS,GAAGhd,MAAM,CAACrF,IAAI,CAACH,UAAU,CAAC,WAAW,CAAC,CAAC;YACtD,IAAIwF,MAAM,CAACyQ,MAAM,KAAK,CAAC,IAAIuM,SAAS,IAAIA,SAAS,CAACpd,MAAM,GAAG,CAAC,EAAE;cAC1D,MAAMqd,SAAS,GAAG3f,IAAI,CAACJ,MAAM;cAC7BI,IAAI,CAACJ,MAAM,GAAG,YAAY;gBACtB;gBACA;gBACA,MAAM8f,SAAS,GAAGhd,MAAM,CAACrF,IAAI,CAACH,UAAU,CAAC,WAAW,CAAC,CAAC;gBACtD,KAAK,IAAImF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqd,SAAS,CAACpd,MAAM,EAAED,CAAC,EAAE,EAAE;kBACvC,IAAIqd,SAAS,CAACrd,CAAC,CAAC,KAAKrC,IAAI,EAAE;oBACvB0f,SAAS,CAAClO,MAAM,CAACnP,CAAC,EAAE,CAAC,CAAC;kBAC1B;gBACJ;gBACA,IAAI,CAACxB,IAAI,CAAC4e,OAAO,IAAIzf,IAAI,CAACE,KAAK,KAAKmf,SAAS,EAAE;kBAC3CM,SAAS,CAAC1Z,IAAI,CAACjG,IAAI,CAAC;gBACxB;cACJ,CAAC;cACD0f,SAAS,CAACra,IAAI,CAACrF,IAAI,CAAC;YACxB,CAAC,MACI;cACDA,IAAI,CAACJ,MAAM,CAAC,CAAC;YACjB;UACJ,CAAC,MACI,IAAI,CAACiB,IAAI,CAAC4e,OAAO,IAAI/c,MAAM,CAACic,aAAa,CAAC,KAAK,KAAK,EAAE;YACvD;YACAjc,MAAM,CAACmc,0BAA0B,CAAC,GAAG,IAAI;UAC7C;QACJ;MACJ,CAAC;MACDI,cAAc,CAAChZ,IAAI,CAACvD,MAAM,EAAE0c,kBAAkB,EAAEE,WAAW,CAAC;MAC5D,MAAMM,UAAU,GAAGld,MAAM,CAAC8b,QAAQ,CAAC;MACnC,IAAI,CAACoB,UAAU,EAAE;QACbld,MAAM,CAAC8b,QAAQ,CAAC,GAAGxe,IAAI;MAC3B;MACA6f,UAAU,CAAC1a,KAAK,CAACzC,MAAM,EAAE7B,IAAI,CAACqF,IAAI,CAAC;MACnCxD,MAAM,CAACic,aAAa,CAAC,GAAG,IAAI;MAC5B,OAAO3e,IAAI;IACf;IACA,SAAS8f,mBAAmBA,CAAA,EAAG,CAAE;IACjC,SAASzC,SAASA,CAACrd,IAAI,EAAE;MACrB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;MACtB;MACA;MACAA,IAAI,CAAC4e,OAAO,GAAG,IAAI;MACnB,OAAOM,WAAW,CAAC5a,KAAK,CAACtE,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,CAACqF,IAAI,CAAC;IACpD;IACA,MAAM8Z,UAAU,GAAG/X,WAAW,CAAC8W,uBAAuB,EAAE,MAAM,EAAE,MAAM,UAAUhZ,IAAI,EAAEG,IAAI,EAAE;MACxFH,IAAI,CAAC0Y,QAAQ,CAAC,GAAGvY,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK;MACjCH,IAAI,CAAC6Y,OAAO,CAAC,GAAG1Y,IAAI,CAAC,CAAC,CAAC;MACvB,OAAO8Z,UAAU,CAAC7a,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;IACvC,CAAC,CAAC;IACF,MAAM+Z,qBAAqB,GAAG,qBAAqB;IACnD,MAAMC,iBAAiB,GAAGhW,UAAU,CAAC,mBAAmB,CAAC;IACzD,MAAMiW,mBAAmB,GAAGjW,UAAU,CAAC,qBAAqB,CAAC;IAC7D,MAAM2V,UAAU,GAAG5X,WAAW,CAAC8W,uBAAuB,EAAE,MAAM,EAAE,MAAM,UAAUhZ,IAAI,EAAEG,IAAI,EAAE;MACxF,IAAI7I,IAAI,CAACM,OAAO,CAACwiB,mBAAmB,CAAC,KAAK,IAAI,EAAE;QAC5C;QACA;QACA;QACA,OAAON,UAAU,CAAC1a,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;MACvC;MACA,IAAIH,IAAI,CAAC0Y,QAAQ,CAAC,EAAE;QAChB;QACA,OAAOoB,UAAU,CAAC1a,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;MACvC,CAAC,MACI;QACD,MAAML,OAAO,GAAG;UAAEnD,MAAM,EAAEqD,IAAI;UAAEqa,GAAG,EAAEra,IAAI,CAAC6Y,OAAO,CAAC;UAAE9d,UAAU,EAAE,KAAK;UAAEoF,IAAI,EAAEA,IAAI;UAAEuZ,OAAO,EAAE;QAAM,CAAC;QACnG,MAAMzf,IAAI,GAAGiK,gCAAgC,CAACgW,qBAAqB,EAAEH,mBAAmB,EAAEja,OAAO,EAAEzE,YAAY,EAAEic,SAAS,CAAC;QAC3H,IAAItX,IAAI,IAAIA,IAAI,CAAC8Y,0BAA0B,CAAC,KAAK,IAAI,IAAI,CAAChZ,OAAO,CAAC4Z,OAAO,IACrEzf,IAAI,CAACE,KAAK,KAAKmf,SAAS,EAAE;UAC1B;UACA;UACA;UACArf,IAAI,CAACJ,MAAM,CAAC,CAAC;QACjB;MACJ;IACJ,CAAC,CAAC;IACF,MAAMmgB,WAAW,GAAG9X,WAAW,CAAC8W,uBAAuB,EAAE,OAAO,EAAE,MAAM,UAAUhZ,IAAI,EAAEG,IAAI,EAAE;MAC1F,MAAMlG,IAAI,GAAGgf,eAAe,CAACjZ,IAAI,CAAC;MAClC,IAAI/F,IAAI,IAAI,OAAOA,IAAI,CAACI,IAAI,IAAI,QAAQ,EAAE;QACtC;QACA;QACA;QACA;QACA,IAAIJ,IAAI,CAACe,QAAQ,IAAI,IAAI,IAAKf,IAAI,CAACa,IAAI,IAAIb,IAAI,CAACa,IAAI,CAAC4e,OAAQ,EAAE;UAC3D;QACJ;QACAzf,IAAI,CAACtC,IAAI,CAACwE,UAAU,CAAClC,IAAI,CAAC;MAC9B,CAAC,MACI,IAAI3C,IAAI,CAACM,OAAO,CAACuiB,iBAAiB,CAAC,KAAK,IAAI,EAAE;QAC/C;QACA,OAAOH,WAAW,CAAC5a,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;MACxC;MACA;MACA;MACA;IACJ,CAAC,CAAC;EACN;AACJ,CAAC,CAAC;;AACF7I,IAAI,CAACW,YAAY,CAAC,aAAa,EAAGrB,MAAM,IAAK;EACzC;EACA,IAAIA,MAAM,CAAC,WAAW,CAAC,IAAIA,MAAM,CAAC,WAAW,CAAC,CAAC0jB,WAAW,EAAE;IACxD9V,cAAc,CAAC5N,MAAM,CAAC,WAAW,CAAC,CAAC0jB,WAAW,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;EAC5F;AACJ,CAAC,CAAC;AACFhjB,IAAI,CAACW,YAAY,CAAC,uBAAuB,EAAE,CAACrB,MAAM,EAAEU,IAAI,KAAK;EACzD;EACA,SAASijB,2BAA2BA,CAAC7F,OAAO,EAAE;IAC1C,OAAO,UAAU1L,CAAC,EAAE;MAChB,MAAMwR,UAAU,GAAGlG,cAAc,CAAC1d,MAAM,EAAE8d,OAAO,CAAC;MAClD8F,UAAU,CAAC/E,OAAO,CAACnb,SAAS,IAAI;QAC5B;QACA;QACA,MAAMmgB,qBAAqB,GAAG7jB,MAAM,CAAC,uBAAuB,CAAC;QAC7D,IAAI6jB,qBAAqB,EAAE;UACvB,MAAMC,GAAG,GAAG,IAAID,qBAAqB,CAAC/F,OAAO,EAAE;YAAEhK,OAAO,EAAE1B,CAAC,CAAC0B,OAAO;YAAE4C,MAAM,EAAEtE,CAAC,CAACC;UAAU,CAAC,CAAC;UAC3F3O,SAAS,CAACT,MAAM,CAAC6gB,GAAG,CAAC;QACzB;MACJ,CAAC,CAAC;IACN,CAAC;EACL;EACA,IAAI9jB,MAAM,CAAC,uBAAuB,CAAC,EAAE;IACjCU,IAAI,CAAC6M,UAAU,CAAC,kCAAkC,CAAC,CAAC,GAChDoW,2BAA2B,CAAC,oBAAoB,CAAC;IACrDjjB,IAAI,CAAC6M,UAAU,CAAC,yBAAyB,CAAC,CAAC,GACvCoW,2BAA2B,CAAC,kBAAkB,CAAC;EACvD;AACJ,CAAC,CAAC;AACFjjB,IAAI,CAACW,YAAY,CAAC,gBAAgB,EAAE,CAACrB,MAAM,EAAEU,IAAI,EAAEmR,GAAG,KAAK;EACvDqO,mBAAmB,CAAClgB,MAAM,EAAE6R,GAAG,CAAC;AACpC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}